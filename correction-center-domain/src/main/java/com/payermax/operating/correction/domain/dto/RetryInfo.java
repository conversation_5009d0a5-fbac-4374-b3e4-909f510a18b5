package com.payermax.operating.correction.domain.dto;

import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 重试信息
 * @date 2023/5/15
 */
@Data
public class RetryInfo {

    private Integer handleRetry;

    private Integer msgRetry;

    public RetryInfo() {
        this.handleRetry = CorrectionConstant.NUM_ZERO;
        this.msgRetry = CorrectionConstant.NUM_ZERO;
    }
}
