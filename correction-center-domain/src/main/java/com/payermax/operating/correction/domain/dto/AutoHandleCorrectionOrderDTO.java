package com.payermax.operating.correction.domain.dto;

import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.TradeType;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 自动处理差错订单所需信息
 * @date 2023/4/20
 */
@Data
public class AutoHandleCorrectionOrderDTO {

    private String correctionNo;

    private String correctionCode;

    private String channelCode;

    private String merchantNo;

    private String merchantOrderNo;

    private String paymentMethod;

    private String sysSources;

    private TradeType tradeType;

    /**
     * 凭证类型
     */
    private DCVoucherType voucherType;

    /**
     * 订单凭证号
     */
    private String voucherNo;

    /**
     * 业务类型
     * 02 代表电商商户
     */
    private String bizType;
}
