package com.payermax.operating.correction.domain.enums;


import com.payermax.operating.correction.core.common.utils.Nullable;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc 系统来源
 * @date 2022/3/4
 */
public enum SourceEnum {
    MANUAL((byte) 1, "人工录入"),

    SYSTEM((byte) 2, "系统接入");

    @Getter
    private Byte order;

    @Getter
    private String msg;

    SourceEnum(Byte order, String msg) {
        this.order = order;
        this.msg = msg;
    }

    public static SourceEnum getByOrder(byte order) {
        return Arrays.stream(values()).filter(e -> e.getOrder().equals(order)).findFirst().orElse(Nullable.getNullVal());//CHECKED 枚举匹配只会匹配一个
    }
}
