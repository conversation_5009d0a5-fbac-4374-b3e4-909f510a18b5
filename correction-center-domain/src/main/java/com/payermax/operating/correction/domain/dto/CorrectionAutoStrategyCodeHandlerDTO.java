package com.payermax.operating.correction.domain.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 策略处理基本所需信息
 * @date 2022/10/19
 */
@Getter
@Builder
@ToString
public class CorrectionAutoStrategyCodeHandlerDTO implements Serializable {
    private static final long serialVersionUID = -1L;
    /**
     * 策略Code
     */
    private String strategyCode;

    /**
     * 运营原因
     */
    private String operationCorrectionCode;

    /**
     * 差错单
     */
    private String correctionNo;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 子策略code
     */
    private String childStrategyCode;

}
