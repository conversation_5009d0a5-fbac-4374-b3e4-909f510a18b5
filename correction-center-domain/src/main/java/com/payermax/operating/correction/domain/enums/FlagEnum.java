package com.payermax.operating.correction.domain.enums;

import com.payermax.operating.correction.core.common.utils.Nullable;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc 状态枚举
 * @date 2023/6/1
 */
public enum FlagEnum {
    YES((byte) 1),
    NO((byte) 0);

    FlagEnum(Byte val) {
        this.val = val;
    }

    @Getter
    private Byte val;

    public static FlagEnum getByName(String name) {
        return StringUtils.isBlank(name) ? Nullable.getNullVal() : FlagEnum.valueOf(name);
    }

    public static FlagEnum getByVal(byte val) {
        return Arrays.stream(values()).filter(e -> e.getVal() == val).findFirst().orElse(Nullable.getNullVal());//CHECKED 枚举匹配只会匹配一个
    }
}
