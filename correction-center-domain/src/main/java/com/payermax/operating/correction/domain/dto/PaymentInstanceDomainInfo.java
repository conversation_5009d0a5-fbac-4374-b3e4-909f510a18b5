package com.payermax.operating.correction.domain.dto;

import com.payermax.common.lang.util.money.Money;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 支付实例domain对象
 * @date 2022/12/15
 */
@Data
@NoArgsConstructor
public class PaymentInstanceDomainInfo {

    private String merchantNo;

    private String bizIdentify;

    private String country;

    private String paymentMethodNo;

    private Money payAmount;

    private String targetOrg;

    private String paymentType;

    private String cashierProductNo;

    public PaymentInstanceDomainInfo(String country, String paymentMethodNo, String paymentType) {
        this.country = country;
        this.paymentMethodNo = paymentMethodNo;
        this.paymentType = paymentType;
    }

    public PaymentInstanceDomainInfo(String country, String paymentMethodNo, String paymentType,String targetOrg) {
        this.country = country;
        this.paymentMethodNo = paymentMethodNo;
        this.paymentType = paymentType;
        this.targetOrg=targetOrg;
    }
}
