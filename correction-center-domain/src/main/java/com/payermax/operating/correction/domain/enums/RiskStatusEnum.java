package com.payermax.operating.correction.domain.enums;

import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.utils.Nullable;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc 风控状态枚举
 * @date 2023/1/12
 */
public enum RiskStatusEnum {
    SUCCESS(1),
    FAIL(2);

    @Getter
    private Integer code;

    RiskStatusEnum(Integer code) {
        this.code = code;
    }

    public static RiskStatusEnum getByCommonStatus(CommonStatusEnum commonStatus) {
        switch (commonStatus) {
            case SUCCESS:
                return RiskStatusEnum.SUCCESS;
            case FAILURE:
                return RiskStatusEnum.FAIL;
            default:
                return Nullable.getNullVal();
        }
    }
}
