package com.payermax.operating.correction.domain.dto;

import com.payermax.common.lang.model.dto.request.PageRequest;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 查询差错信息对象
 * @date 2022/4/6
 */
@Data
@NoArgsConstructor
public class QueryCorrectionInfoDto {
    /**
     * 订单号
     */
    private String voucherNo;
    /**
     * 凭证类型
     */
    private String voucherType;
    /**
     * 差错原因
     */
    private String correctionCode;
    /**
     * 处理状态
     * @mock PROCESSED
     */
    private ProcessStatusEnum processStatus;
    /**
     * page信息
     */
    private PageRequest page;
    /**
     * 操作人
     */
    private String operator;
}
