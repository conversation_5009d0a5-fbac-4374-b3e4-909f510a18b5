package com.payermax.operating.correction.domain.dto;

import com.payermax.operating.correction.core.common.enums.UserInfoEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @desc 额外信息对象
 * <AUTHOR>
 * @date 2022/12/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExtraUserInfoDTO {
    private UserInfoEnum userEnum;

    private String token;

    private String extraInfo;

    private Integer seqNo;

    public ExtraUserInfoDTO(UserInfoEnum userEnum, String token) {
        this.userEnum = userEnum;
        this.token = token;
    }
}
