package com.payermax.operating.correction.domain.dto;

import com.payermax.common.lang.util.money.Money;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 渠道对账冗余信息
 * @date 2023/5/23
 */
@Data
@NoArgsConstructor
public class ChannelReconcileRedundantDTO extends TradeRedundantInfo{

    /**
     * 主体
     */
    private String entity;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 外部完成时间
     */
    private String completeTime;

    /**
     * 提交单完成时间(金融交换)
     */
    private String commitCompleteTime;

    /**
     * 外部状态
     */
    private String channelStatus;

    /**
     * 提交单状态(金融交换)
     */
    private String commitStatus;

    /**
     * 批次信息
     */
    private String batchNo;

    /**
     * 提交单号
     */
    private String channelCommitNo;

    /**
     * 三方单号
     */
    private String thirdOrderNo;

    /**
     * 四方单号
     */
    private String fourthOrderNo;

    /**
     * 支付金额
     */
    private Money payAmount;

    /**
     * 对账规则Id
     */
    private String reconcileRuleId;

    /**
     * 交易对账差错凭证号
     */
    private String errorOrderNo;

    /**
     * 交易类型(外部)
     */
    private String channelTradeType;

    /**
     * 客资负责人
     */
    private String owner;

}
