package com.payermax.operating.correction.domain.dto;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.CorrectionBaseInfo;
import com.payermax.operating.correction.core.common.dto.OperationManualFillIn;
import com.payermax.operating.correction.domain.enums.CorrectionEvent;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domain.enums.SourceEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @desc 差错domain对象信息
 * @date 2022/4/1
 */
@Data
@NoArgsConstructor
@ToString
public class OperationDomainCorrectionInfo {
    /**
     * 差错基础信息
     */
    private CorrectionBaseInfo baseInfo;

    /**
     * 商户订单号
     */
    private String merchantOrderNo;

    /**
     * 来源
     */
    private SourceEnum source;

    /**
     * 系统来源
     */
    private String sysSource;

    /**
     * 支付总金额
     */
    private Money payTotalMoney;

    /**
     * 处理状态
     */
    private ProcessStatusEnum processStatus;

    /**
     * 差错事件
     */
    private CorrectionEvent correctionEvent;

    /**
     * 重试类型
     */
    private String retryType;

    /**
     * 处理策略
     */
    private String strategyCode;

    /**
     * 运营处理原因
     */
    private String operationCorrectionCode;

    /**
     * 运营手动填写信息
     */
    private OperationManualFillIn operationManual;

    /**
     * 交易对账冗余信息
     */
    private ChannelReconcileRedundantDTO channelReconcileRedundant;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 复核人
     */
    private EventTransferInfo transferInfo;

    /**
     * 重试信息
     */
    private RetryInfo retryInfo;

    public void fillSystemCorrectionInfoDTO() {
        this.source = SourceEnum.SYSTEM;
        this.operator = CorrectionConstant.SYSTEM_OPERATION;
        this.processStatus = ProcessStatusEnum.PROCESSED;
    }

    public void fillManualCorrectionInfoDTO(String operator) {
        this.operator = operator;
        this.source = SourceEnum.MANUAL;
        this.processStatus = ProcessStatusEnum.PROCESSED;
    }

    public OperationDomainCorrectionInfo(String correctionNo, String strategyCode, String operator, OperationManualFillIn operationManual) {
        this.baseInfo = new CorrectionBaseInfo(correctionNo);
        this.strategyCode = strategyCode;
        this.operator = operator;
        this.operationManual = operationManual;
    }
}
