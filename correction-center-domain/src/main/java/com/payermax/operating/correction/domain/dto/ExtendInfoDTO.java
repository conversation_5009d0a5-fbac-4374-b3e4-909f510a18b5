package com.payermax.operating.correction.domain.dto;

import com.payermax.operating.correction.core.common.enums.ExtendEnum;
import com.payermax.operating.correction.core.common.enums.ExtendEnum;
import com.payermax.operating.correction.core.common.enums.ValidType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 扩展信息
 * @date 2022/11/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExtendInfoDTO {

    private ExtendEnum operation;

    private String json;

    private Integer seqNo;

    public ExtendInfoDTO(ExtendEnum operation, String json) {
        this.operation = operation;
        this.json = json;
    }
}
