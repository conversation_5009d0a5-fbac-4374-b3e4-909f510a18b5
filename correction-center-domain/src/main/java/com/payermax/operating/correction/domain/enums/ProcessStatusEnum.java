package com.payermax.operating.correction.domain.enums;

import com.payermax.operating.correction.core.common.utils.Nullable;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @desc 处理状态
 * @date 2022/3/7
 */
public enum ProcessStatusEnum {
    PROCESSED("待处理"),
    REVIEWED("转交待处理"),
    VERIFICATION("核实待处理"),
    REVIEWING("已处理待审核"),

    PENDING("已受理"),

    SUCCESS("成功"),
    FAILURE("失败"),
    ;
    @Getter
    private String msg;

    ProcessStatusEnum(String msg) {
        this.msg = msg;
    }

    public static ProcessStatusEnum getByName(String name) {
        return StringUtils.isBlank(name) ? Nullable.getNullVal() : ProcessStatusEnum.valueOf(name);
    }
}
