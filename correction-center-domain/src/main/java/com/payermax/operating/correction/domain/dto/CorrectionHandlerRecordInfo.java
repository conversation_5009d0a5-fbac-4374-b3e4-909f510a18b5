package com.payermax.operating.correction.domain.dto;

import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 差错处理记录
 * @date 2022/10/11
 */
@Data
public class CorrectionHandlerRecordInfo {

    /**
     * 差错单
     */
    private String correctionNo;

    /**
     * 策略code
     */
    private String strategyCode;

    /**
     * 处理类型
     */
    private String handlerType;

    /**
     * 处理状态
     */
    private CommonStatusEnum handlerStatus;

    /**
     * 处理结果
     */
    private String processResult;

    /**
     * 操作基础信息
     */
    private OperationBasicInfo opBasicInfo;
}
