package com.payermax.operating.correction.domain.dto;

import com.payermax.operating.correction.domain.enums.FlagEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 事件转移
 * @date 2023/6/1
 */
@Data
@NoArgsConstructor
public class EventTransferInfo {
    private String correctionNo;

    private FlagEnum flag;

    private String reviewer;

    private String operator;

    public EventTransferInfo(String forwardType, String reviewer,String correctionNo,String operator) {
        this.flag = FlagEnum.getByName(forwardType);
        this.reviewer = reviewer;
        this.correctionNo=correctionNo;
        this.operator=operator;
    }
}
