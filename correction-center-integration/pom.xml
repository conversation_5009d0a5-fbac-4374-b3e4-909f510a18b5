<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fin-correction-center</artifactId>
        <groupId>com.payermax.operating</groupId>
        <version>1.0.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>correction-center-integration</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.payermax.trade</groupId>
            <artifactId>paylink-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>ionia-log-digest-core</artifactId>
                    <groupId>com.payermax.infra</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fintech-components-log</artifactId>
                    <groupId>com.payermax.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>lettuce-core</artifactId>
                    <groupId>io.lettuce</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.payermax.sales</groupId>
            <artifactId>sales-product-solution-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.payermax.operating</groupId>
            <artifactId>dal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.operating</groupId>
            <artifactId>common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ushareit.fintech.parent</groupId>
            <artifactId>fintech-common</artifactId>
        </dependency>
        <!-- 依赖服务jar包    -->
        <dependency>
            <groupId>com.payermax.sale</groupId>
            <artifactId>sale-product-centre-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.risk</groupId>
            <artifactId>risk-engine-client</artifactId>
        </dependency>
        <dependency>
            <artifactId>payment-assetsx-facade</artifactId>
            <groupId>com.payermax.payment</groupId>
        </dependency>
        <dependency>
            <artifactId>assetx-correction-facade</artifactId>
            <groupId>com.payermax.payment</groupId>
        </dependency>
        <dependency>
            <groupId>com.payermax.data</groupId>
            <artifactId>dc-query-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo-registry-nacos</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <artifactId>fintech-security-service-api</artifactId>
            <groupId>com.ushareit.fintech.security</groupId>
        </dependency>
        <dependency>
            <groupId>com.payermax.trade</groupId>
            <artifactId>trade-ordercenter-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.basic</groupId>
            <artifactId>context-center-service-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>sale-fee-center-facade</artifactId>
                    <groupId>com.payermax.sale</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.payermax.payment</groupId>
            <artifactId>payment-cashier-core-facade</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo-registry-nacos</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.payermax.sales</groupId>
            <artifactId>sales-omc-merchant-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-exchange-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.trade</groupId>
            <artifactId>trade-settle-facade</artifactId>
        </dependency>
        <!--    其他依赖jar包    -->
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-rocketMQ</artifactId>
        </dependency>
        <dependency>
            <artifactId>basic-voucher-facade</artifactId>
            <groupId>com.payermax.basic</groupId>
        </dependency>
        <!-- mybatis 分页插件 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.common</groupId>
            <artifactId>common-lang</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-yaml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-log-digest-dubbo3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-log-digest-annotation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.funds</groupId>
            <artifactId>funds-order-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.channel</groupId>
            <artifactId>channel-reconcile-facade</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
