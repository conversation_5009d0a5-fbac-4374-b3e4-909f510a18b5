package com.payermax.operating.correction.integration.rpc.security.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.integration.rpc.security.repository.SecurityRepository;
import com.ushareit.fintech.security.api.SecurityService;
import com.ushareit.fintech.security.entity.req.DecryptContentRequest;
import com.ushareit.fintech.security.entity.req.EncryptRequest;
import com.ushareit.fintech.security.entity.req.QuerySecurityContentRequest;
import com.ushareit.fintech.security.entity.resp.DecryptContentResponse;
import com.ushareit.fintech.security.entity.resp.EncryptResponse;
import com.ushareit.fintech.security.entity.resp.SecurityResponse;
import com.ushareit.fintech.security.enums.EncryptTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 安全服务资源实现类
 * @date 2022/12/13
 */
@Repository
@Slf4j
public class SecurityRepositoryImpl implements SecurityRepository {

    @DubboReference(version = "1.0", retries = 2, timeout = 5000)
    private SecurityService securityService;

    @Override
    public String encrypt(String json) {
        EncryptRequest encryptRequest=new EncryptRequest();
        encryptRequest.setName(CorrectionConstant.USER_INFO_NAME);
        encryptRequest.setValue(json);
        encryptRequest.setEncryptType(EncryptTypeEnum.OTHER);
        Result<EncryptResponse> encrypt = securityService.encrypt(encryptRequest);
        AssertUtil.isTrue(ResultUtil.isApplySuccess(encrypt)&& Objects.nonNull(encrypt.getData()), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_SECURITY_EXCEPTION);
        return encrypt.getData().getToken();
    }

    @Override
    public String decrypt(String token) {
        QuerySecurityContentRequest securityContentRequest=new QuerySecurityContentRequest();
        securityContentRequest.setToken(token);
        securityContentRequest.setNeedDecrypt(Boolean.TRUE);
        Result<SecurityResponse> result = securityService.query(securityContentRequest);
        AssertUtil.isTrue(ResultUtil.isApplySuccess(result) && Objects.nonNull(result.getData()), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_SECURITY_EXCEPTION);
        log.info("decrypt,token : [{}]", JSONObject.toJSONString(result.getData()));
        return result.getData().getValue();
    }
}
