package com.payermax.operating.correction.integration.rpc.payouts.repository;

import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsCoreInfo;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsFiledInfo;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsRuleInfo;

import java.util.List;

/**
 * The interface Order center repository.
 *
 * <AUTHOR>
 * @desc 出款服务资源类
 * @date 2022 /10/10
 */
public interface PayoutsRepository {

    /**
     * Query payouts field info list.
     *
     * @param country          the country
     * @param cashierProductNo the cashier product no
     * @return the list
     */
    List<PayoutsFiledInfo>queryPayoutsFieldInfo(String country,String cashierProductNo);

    /**
     * Payouts rule check boolean.
     *
     * @param payoutsRuleInfo the payouts rule info
     * @param coreInfo        the core info
     * @return the boolean
     */
    Boolean payoutsRuleCheck(PayoutsRuleInfo payoutsRuleInfo, PayoutsCoreInfo coreInfo);

    /**
     * 查询出款状态
     * @param merchantNo
     * @param outTradeNo
     * @return
     */
    String orderQueryStatus(String merchantNo, String outTradeNo);
}
