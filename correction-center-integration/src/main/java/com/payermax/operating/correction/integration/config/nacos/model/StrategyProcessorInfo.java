package com.payermax.operating.correction.integration.config.nacos.model;

import com.payermax.operating.correction.core.common.enums.HandlerType;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOperationStrategyInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 策略处理器配置开发
 * @date 2022/9/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StrategyProcessorInfo {

    /**
     * 策略code
     */
    private String strategyCode;

    /**
     * 策略描述
     */
    private String desc;

    /**
     * 处理器列表
     */
    private List<Processor> processors;

    /**
     * db信息
     */
    CorrectionOperationStrategyInfoDTO dbInfo;

    @Data
    public static class Processor {
        /**
         * 处理器名称
         */
        private String processName;

        /**
         * 处理器Spring beanName
         **/
        private String beanName;

        /**
         * 处理器响应类型
         */
        private HandlerType handler;

    }
}
