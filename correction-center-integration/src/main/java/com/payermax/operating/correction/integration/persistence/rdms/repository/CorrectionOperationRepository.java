package com.payermax.operating.correction.integration.persistence.rdms.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.payermax.common.lang.model.dto.request.PageRequest;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.dto.OperationManualFillIn;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.enums.ValidType;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOperationStrategyInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.QueryCorrectionOrderPageDTO;

import java.util.List;

/**
 * The interface Correction operation repository.
 *
 * <AUTHOR>
 * @desc 差错运营资源接口
 * @date 2022 /9/20
 */
public interface CorrectionOperationRepository {

    /**
     * Store operation strategy info.
     *
     * @param info the info
     */
    void storeOperationStrategyInfo(CorrectionOperationStrategyInfoDTO info);

    /**
     * Load operation strategy correction operation strategy info model.
     *
     * @param strategyCode the strategy code
     * @return the correction operation strategy info model
     */
    CorrectionOperationStrategyInfoDTO loadOperationStrategy(String strategyCode);

    /**
     * Load operation strategy info list.
     *
     * @param page      the page
     * @param validType the valid type
     * @return the list
     */
    Page<CorrectionOperationStrategyInfoDTO> loadOperationStrategyInfo(PageRequest page, ValidType validType);


    /**
     * Load all children  basic info list.
     *
     * @return the list
     */
    List<CorrectionBasicInfoDTO> loadAllChildrenBasicInfo();

    /**
     * Load all parent basic info list.
     *
     * @return the list
     */
    List<CorrectionBasicInfoDTO> loadAllParentBasicInfo();

    /**
     * Load operation strategy all list.
     *
     * @return the list
     */
    List<CorrectionOperationStrategyInfoDTO> loadOperationStrategyAll();

    /**
     * Store correction basic info.
     *
     * @param info the info
     */
    void storeCorrectionBasicInfo(CorrectionBasicInfoDTO info);


    /**
     * Load basic info correction basic info dto.
     *
     * @param correctionCode the correction code
     * @return the correction basic info dto
     */
    CorrectionBasicInfoDTO loadBasicInfo(String correctionCode);

    /**
     * Load reason basic info page.
     *
     * @param page      the page
     * @param validType the valid type
     * @return the page
     */
    Page<CorrectionBasicInfoDTO> loadReasonBasicInfo(PageRequest page, ValidType validType);

    /**
     * Load children reason basic by parent list.
     *
     * @param parentCorrectionCode the parent correction code
     * @param tradeType            the trade type
     * @return the list
     */
    List<CorrectionBasicInfoDTO> loadChildrenReasonBasicByParent(String parentCorrectionCode,TradeType tradeType);

    /**
     * Store strategy info reason mapping.
     *
     * @param strategyList   the strategy list
     * @param correctionCode the correction code
     * @param operator       the operator
     * @param tradeType      the trade type
     */
    void storeStrategyInfoReasonMapping(List<String> strategyList, final String correctionCode, final String operator, TradeType tradeType);


    /**
     * Load correction order info page.
     *
     * @param queryCorrectionPage the correction page query
     * @return the page
     */
    Page<CorrectionOrderInfoDTO> loadCorrectionOrderInfo(QueryCorrectionOrderPageDTO queryCorrectionPage);

    /**
     * Load correction order info page.
     *
     * @param page          the page
     * @param correctionNos the correction nos
     * @param voucherNos    the voucher nos
     * @return the page
     */
    Page<CorrectionOrderInfoDTO> loadCorrectionOrderInfo(PageRequest page,List<String>correctionNos,List<String>voucherNos);

    /**
     * Store manual fill info.
     *
     * @param correctionNo    the correction no
     * @param operationManual the operation manual
     */
    void storeManualFillInfo(String correctionNo, OperationManualFillIn operationManual);

    /**
     * Refresh reason basic.
     *
     * @param basicInfoDTO the basic info dto
     */
    void refreshReasonBasic(CorrectionBasicInfoDTO basicInfoDTO);

    /**
     * Invalid strategy info reason mapping.
     *
     * @param correctionCode the correction code
     * @param tradeType      the trade type
     */
    void invalidStrategyInfoReasonMapping(String correctionCode, TradeType tradeType);

    /**
     * Store operator.
     *
     * @param correctionNo the correction no
     * @param basicInfo    the basic info
     */
    void storeCorrectionOpBasicInfo(String correctionNo, OperationBasicInfo basicInfo);
}
