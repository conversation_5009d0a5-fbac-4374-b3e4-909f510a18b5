package com.payermax.operating.correction.integration.rpc.merchant.dto;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class BatchQueryMerchantBaseInfoReps {

    private List<MerchantBaseInfo> merchantBaseInfos;

    /**
     * 获取商户内部名称
     * @param merchantNo
     * @return
     */
    public String getInnerName(String merchantNo) {
        if (merchantBaseInfos == null) {
            return null;
        }

        if (merchantNo == null || StringUtils.isBlank(merchantNo)) {
            return null;
        }

        return  merchantBaseInfos.stream()
                .filter(merchantBaseInfo -> merchantBaseInfo.getMerchantNo().equals(merchantNo))
                .findFirst().map(MerchantBaseInfo::getInnerName).orElse(null);
    }
}


