package com.payermax.operating.correction.integration.config.persistence.repository;

import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOperationStrategyInfoDTO;

import java.util.List;

/**
 * The interface Rdms config repository.
 *
 * <AUTHOR>
 * @desc 数据库资源配置
 * @date 2022 /9/28
 */
public interface RdmsConfigRepository {

    /**
     * Gets basic info.
     *
     * @param correctionCode the correction code
     * @return the basic info
     */
    CorrectionBasicInfoDTO getBasicInfo(String correctionCode);


    /**
     * Gets correction operation strategy info.
     *
     * @param strategyCode the strategy code
     * @return the correction operation strategy info
     */
    CorrectionOperationStrategyInfoDTO getCorrectionOperationStrategyInfo(String strategyCode);

    /**
     * Load all parent basic info list.
     *
     * @return the list
     */
    List<CorrectionBasicInfoDTO> loadAllParentBasicInfo();
}
