package com.payermax.operating.correction.integration.rpc.ordercenter.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
  * @Author: raoxw
  * @date 2021/12/30 16:20 
  * @description 
  **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderCenterPayOrderExtendField {
    /**
     * 重复支付差错场景-补单所关联的原支付单号
     */
    private String correctionRepeatOriPayRequestNo;
}
