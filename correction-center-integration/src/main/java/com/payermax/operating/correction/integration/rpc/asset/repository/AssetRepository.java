package com.payermax.operating.correction.integration.rpc.asset.repository;

import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.integration.dto.CorrectionPayoutsInfo;
import com.payermax.operating.correction.integration.rpc.asset.dto.AssetNotifyInfo;

/**
 * The interface Asset repository.
 *
 * <AUTHOR>
 * @desc 资产交换资源接口类
 * @date 2022 /12/13
 */
public interface AssetRepository {

    /**
     * Correction payouts base res process.
     *
     * @param payoutsInfo the payouts info
     * @return the base res process
     */
    BaseResProcess correctionPayouts(CorrectionPayoutsInfo payoutsInfo);

    /**
     * 通知资产交换修改重复支付场景关联关系
     * @param assetNotifyInfo
     * @return
     */
    BaseResProcess correctionRepeatPay(AssetNotifyInfo assetNotifyInfo);
}
