package com.payermax.operating.correction.integration.rpc.financial.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.fin.exchange.service.facade.CallbackFacade;
import com.payermax.fin.exchange.service.facade.CompensateFacade;
import com.payermax.fin.exchange.service.facade.InquiryOrderInfoFacade;
import com.payermax.fin.exchange.service.request.InquiryCommitOrderRequest;
import com.payermax.fin.exchange.service.response.InquiryCommitOrderResponse;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.integration.rpc.financial.assembler.FinancialExchangeAssembler;
import com.payermax.operating.correction.integration.rpc.financial.dto.ChannelCommitResp;
import com.payermax.operating.correction.integration.rpc.financial.dto.ChannelResultNotifyInfo;
import com.payermax.operating.correction.integration.rpc.financial.dto.ChannelCommitReq;
import com.payermax.operating.correction.integration.rpc.financial.repository.ChannelExchangeRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 金融交换资源实现类
 * @date 2022/12/14
 */
@Slf4j
@Repository
public class ChannelExchangeRepositoryImpl implements ChannelExchangeRepository {

    @DubboReference(version = "1.0", retries = 1, timeout = 3000)
    private CallbackFacade callbackFacade;

    @DubboReference(version = "1.0", retries = 1, timeout = 3000)
    private CompensateFacade compensateFacade;

    @DubboReference(version = "1.0", retries = 1, timeout = 3000)
    private InquiryOrderInfoFacade inquiryOrderInfoFacade;

    @Resource
    private FinancialExchangeAssembler exchangeAssembler;

    @Override
    public BaseResProcess financialResultNotify(ChannelResultNotifyInfo resultNotifyInfo) {
        Result ret = callbackFacade.callbackNotify(exchangeAssembler.toCallbackNotify(resultNotifyInfo));
        AssertUtil.isTrue(ResultUtil.isApplySuccess(ret), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_CHANNEL_EXCHANGE_EXCEPTION);
        String retStr = JSONObject.toJSONString(ret);
        if (CorrectionConstant.INSTITUTION_OFFLINE_REFUND.equals(resultNotifyInfo.getHandleType())) {
            return new BaseResProcess(new VoucherInfo(resultNotifyInfo.getThirdOrderNo(), DCVoucherType.THIRD_ORDER_NO), retStr);
        }
        return new BaseResProcess(new VoucherInfo(resultNotifyInfo.getChannelCommitNo(), DCVoucherType.CHANNEL_COMMIT_NO), retStr);
    }

    @Override
    public BaseResProcess bounceBack(ChannelResultNotifyInfo resultNotifyInfo) {
        Result<String> ret = compensateFacade.compensateOrderStatus(exchangeAssembler.toCompensateOrderStatus(resultNotifyInfo));
        AssertUtil.isTrue(ResultUtil.isApplySuccess(ret), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_CHANNEL_EXCHANGE_EXCEPTION);
        return new BaseResProcess(new VoucherInfo(resultNotifyInfo.getChannelCommitNo(), DCVoucherType.CHANNEL_COMMIT_NO), ret.getData(), CommonStatusEnum.SUCCESS);
    }

    @Override
    public ChannelCommitResp queryChannelByChannelPayCommitNo(ChannelCommitReq channelCommitReq) {
        InquiryCommitOrderRequest inquiryCommitOrderRequest = new InquiryCommitOrderRequest();
        inquiryCommitOrderRequest.setChannelPayCommitNo(channelCommitReq.getChannelPayCommitNo());
        inquiryCommitOrderRequest.setPaymentType(channelCommitReq.getPaymentType());

        Result<List<InquiryCommitOrderResponse>> listResult = inquiryOrderInfoFacade.inquiryCommitOrder(inquiryCommitOrderRequest);
        List<InquiryCommitOrderResponse> data = listResult.getData();

        if (data.size() != 1) {
            return null;
        }
        InquiryCommitOrderResponse inquiryCommitOrderResponse = data.get(0);

        return ChannelCommitResp.builder()
                .channelCode(inquiryCommitOrderResponse.getChannelCode())
                .channelPayCommitNo(inquiryCommitOrderResponse.getChannelPayCommitNo())
                .status(inquiryCommitOrderResponse.getStatus().name())
                .build();
    }
}
