package com.payermax.operating.correction.integration.config.persistence.repository.impl;

import com.payermax.operating.correction.integration.config.persistence.repository.RdmsConfigRepository;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOperationStrategyInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionOperationRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 数据库的配置加载
 * @date 2022/9/28
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class RdmsConfigRepositoryImpl implements RdmsConfigRepository {

    private final CorrectionOperationRepository correctionRepository;



    @Override
    public CorrectionBasicInfoDTO getBasicInfo(String correctionCode) {
        return correctionRepository.loadBasicInfo(correctionCode);
    }

    @Override
    public CorrectionOperationStrategyInfoDTO getCorrectionOperationStrategyInfo(String strategyCode) {
        return correctionRepository.loadOperationStrategy(strategyCode);
    }

    @Override
    public List<CorrectionBasicInfoDTO> loadAllParentBasicInfo() {
        return correctionRepository.loadAllParentBasicInfo();
    }


}
