package com.payermax.operating.correction.integration.queue.dto;

import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import lombok.*;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 退款结果通知mq
 * @date 2022/10/8
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ResultNotifyDTO extends BaseMqMessage implements Serializable {

    private static final long serialVersionUID = -1234245664565623L;

    private ResultNotify messageBody;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ResultNotify {

        private String voucherNo;

        private String status;

        private String voucherType;

        private String correctionCode;

        private String correctionNo;

        private String strategyProcess;

        private String tag;

        private String detailCode;

        private String detailMsg;

        private String redundantInfo;

    }

}
