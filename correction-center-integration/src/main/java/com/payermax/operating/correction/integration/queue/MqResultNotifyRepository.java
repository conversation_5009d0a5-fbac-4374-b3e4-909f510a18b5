package com.payermax.operating.correction.integration.queue;

import com.payermax.operating.correction.integration.queue.dto.CorrectionEventMessageInfo;
import com.payermax.operating.correction.integration.queue.dto.ExternalResultInfoMessageInfo;
import com.payermax.operating.correction.integration.queue.dto.ResultNotifyDTO;

/**
 * The interface Mq result notify repository.
 *
 * <AUTHOR>
 * @desc mq 结果通知
 * @date 2022 /10/8
 */
public interface MqResultNotifyRepository {

    /**
     * Handler result notify.
     *
     * @param resultNotify the refund notify
     */
    void handlerResultNotify(ResultNotifyDTO resultNotify);

    /**
     * Delay handler notify.
     *
     * @param correctionNo the correction no
     * @param retryNum     the retry num
     */
    void delayHandlerNotify(String correctionNo,Integer retryNum);

    /**
     * Delay push event.
     *
     * @param eventMessageInfo the event message info
     */
    void delayPushEvent(CorrectionEventMessageInfo eventMessageInfo);

    /**
     * Delay push external result event.
     *
     * @param externalResult the external result
     */
    void delayPushExternalResultEvent(ExternalResultInfoMessageInfo externalResult);
}
