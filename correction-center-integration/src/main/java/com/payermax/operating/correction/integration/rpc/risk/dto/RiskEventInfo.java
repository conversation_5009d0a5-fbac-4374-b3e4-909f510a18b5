package com.payermax.operating.correction.integration.rpc.risk.dto;

import com.alibaba.fastjson.JSONObject;
import com.payermax.operating.correction.integration.rpc.risk.enums.RiskType;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 风控事件
 * @date 2022/12/12
 */
@Data
public class RiskEventInfo {

    /**
     * 风控类型
     */
    private RiskType riskType;

    /**
     * 事件body
     */
    private JSONObject eventBody;

    /**
     * 差错出款凭证号
     */
    private String requestId;
}
