package com.payermax.operating.correction.integration.rpc.paylink.assembler;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.rpc.paylink.dto.PayLinkPatchNoticeRequest;
import com.payermax.operating.correction.integration.rpc.paylink.dto.PayLinkPatchNoticeResponse;
import com.payermax.trade.paylink.facade.request.PatchPayLinkNoticeRequest;
import com.payermax.trade.paylink.facade.response.PatchPayLinkNoticeResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Optional;

/**
 * The interface Order center assembler.
 *
 * <AUTHOR>
 * @desc 出款服务转换类
 * @date 2022 /10/10
 */
@Mapper(componentModel = "spring", imports = {Optional.class, Nullable.class, CorrectionConstant.class})
public interface PayLinkAssembler {

    @Mappings({
            @Mapping(target = "merchantAppId", source = "appId")
    })
    PatchPayLinkNoticeRequest toPatchPayLinkNotice(PayLinkPatchNoticeRequest payLinkPatchNoticeRequest);


    @Mappings({
            @Mapping(target = "code", source = "result.code"),
            @Mapping(target = "msg", source = "result.msg")
    })
    PayLinkPatchNoticeResponse toPayLinkPatchNoticeResponse(Result<PatchPayLinkNoticeResponse> result,PatchPayLinkNoticeResponse response);



}

