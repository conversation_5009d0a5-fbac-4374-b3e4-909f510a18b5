package com.payermax.operating.correction.integration.rpc.paylink.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2025-03-18 17:30
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PayLinkPatchNoticeResponse {


    /**
     * 返回code码
     */
    private String code;

    /**
     * 返回消息体
     */
    private String msg;

    /**
     * 补通知状态
     */
    private String status;
}
