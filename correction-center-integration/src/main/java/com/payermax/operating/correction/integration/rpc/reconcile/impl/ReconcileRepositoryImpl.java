package com.payermax.operating.correction.integration.rpc.reconcile.impl;

import com.payermax.channel.reconcile.facade.FileExternalOrderFacade;
import com.payermax.channel.reconcile.response.QueryFileExternalOrderResponse;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.correction.integration.rpc.reconcile.ReconcileRepository;
import com.payermax.operating.correction.integration.rpc.reconcile.assembler.ReconcileInfoAssembler;
import com.payermax.operating.correction.integration.rpc.reconcile.dto.ReconcileInfo;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class ReconcileRepositoryImpl implements ReconcileRepository {

    @DubboReference(version = "1.0", timeout = 5000)
    private FileExternalOrderFacade fileExternalOrderFacade;

    @Resource
    private ReconcileInfoAssembler reconcileInfoAssembler;
    @Override
    public List<ReconcileInfo> queryReconcileByChannelCommitNo(String channelCommitNo) {

        Result<List<QueryFileExternalOrderResponse>> result = fileExternalOrderFacade.getFileExternalOrderByInternalOrderNo(channelCommitNo);
        List<QueryFileExternalOrderResponse> data = result.getData();

        List<QueryFileExternalOrderResponse> collect =
                data.stream().filter(x -> x.getInternalOrderNo().equals(channelCommitNo)).collect(Collectors.toList());

        return reconcileInfoAssembler.toReconcileInfo(collect);
    }
}
