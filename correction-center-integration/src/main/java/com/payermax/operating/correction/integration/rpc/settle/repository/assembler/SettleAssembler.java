package com.payermax.operating.correction.integration.rpc.settle.repository.assembler;

import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.trade.settle.domain.enums.OutStatusEnum;
import com.payermax.trade.settle.service.facade.entity.req.RefundBackClearReq;
import com.payermax.trade.settle.service.facade.entity.resp.RefundBackClearResp;
import org.mapstruct.*;

import java.util.Optional;

/**
 * The interface Settle assembler.
 *
 * <AUTHOR>
 * @desc 结算转换器
 * @date 2023 /4/17
 */
@Mapper(componentModel = "spring", imports = {Optional.class, Nullable.class, DCVoucherType.class})
public interface SettleAssembler {

    /**
     * To refund back req refund back clear req.
     *
     * @param correctionNo  the correction no
     * @param refundTradeNo the refund trade no
     * @return the refund back clear req
     */
    @Mappings({
            @Mapping(target = "tradeOrderNo", source = "correctionNo"),
            @Mapping(target = "origTradeNo", source = "refundTradeNo"),
    })
    RefundBackClearReq toRefundBackReq(String correctionNo, String refundTradeNo);

    /**
     * To base res process.
     *
     * @param resp          the resp
     * @param refundTradeNo the refund trade no
     * @return the base res process
     */
    @Mappings({
            @Mapping(target = "resVoucherInfo.voucherNo", source = "refundTradeNo"),
            @Mapping(target = "resVoucherInfo.voucherType", expression = "java(DCVoucherType.REFUND_TRADE_NO)"),
            @Mapping(target = "status", expression = "java(statusMapping(resp.getStatus()))"),
    })
    BaseResProcess toBaseRes(RefundBackClearResp resp, String refundTradeNo);

    /**
     * Status mapping common status enum.
     *
     * @param outStatus the out status
     * @return the common status enum
     */
    @ValueMappings({
                @ValueMapping(target = "SUCCESS", source = "SUCCESS"),
                @ValueMapping(target = "PENDING", source = "ACCEPTING"),
                @ValueMapping(target = "PENDING", source = "FAIL"),
                @ValueMapping(target = "SUCCESS", source = "SETTLE_SUCCESS"),
            })
    CommonStatusEnum statusMapping(OutStatusEnum outStatus);
}
