package com.payermax.operating.correction.integration.rpc.financial.assembler;

import com.payermax.fin.exchange.service.request.CallbackNotifyRequest;
import com.payermax.fin.exchange.service.request.CompensateStatusRequest;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.enums.ChannelOrderStatus;
import com.payermax.operating.correction.integration.rpc.financial.dto.ChannelResultNotifyInfo;
import com.payermax.operating.correction.integration.rpc.financial.enums.ChannelNotifyType;
import org.mapstruct.*;

import java.util.Optional;

/**
 * The interface Financial exchange assembler.
 *
 * <AUTHOR>
 * @desc 金融交换转换器
 * @date 2023 /4/18
 */
@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL
        , imports = {Optional.class, Nullable.class, TradeType.class, ChannelOrderStatus.class})
public interface FinancialExchangeAssembler {

    /**
     * To callback notify request.
     *
     * @param channelResultNotifyInfo the channel result notify info
     * @return the callback notify request
     */
    @Mappings({
            @Mapping(target = "referenceNo", source = "channelCommitNo"),
            @Mapping(target = "notifyType", expression = "java(notifyTypeMapping(channelResultNotifyInfo.getNotifyType()))"),
            @Mapping(target = "status", expression = "java(statusMapping(channelResultNotifyInfo.getProcessStatus()))"),
            @Mapping(target = "payThirdChannelNo", source = "thirdOrderNo"),
            @Mapping(target = "payAmount", source = "payAmount"),
            @Mapping(target = "addField2", source = "handleType"),
            @Mapping(target = "addField3", source = "fourthOrderNo"),
            @Mapping(target = "respCode", source = "respCode"),
            @Mapping(target = "respMsg", source = "respMsg"),
    })
    CallbackNotifyRequest toCallbackNotify(ChannelResultNotifyInfo channelResultNotifyInfo);


    /**
     * Notify type mapping callback notify request . notify type enum.
     *
     * @param notifyType the notify type
     * @return the callback notify request . notify type enum
     */
    @ValueMappings({
            @ValueMapping(target = "REFUND", source = "REFUND"),
            @ValueMapping(target = "PAYMENT", source = "PAYMENT"),
            @ValueMapping(target = "PAYOUT", source = "PAYOUT"),
    })
    CallbackNotifyRequest.NotifyTypeEnum notifyTypeMapping(ChannelNotifyType notifyType);

    /**
     * Status mapping callback notify request . notify status enum.
     *
     * @param status the status
     * @return the callback notify request . notify status enum
     */
    @ValueMappings({
            @ValueMapping(target = "PENDING", source = "PENDING"),
            @ValueMapping(target = "FAILED", source = "FAILURE"),
            @ValueMapping(target = "SUCCESS", source = "SUCCESS"),
            @ValueMapping(target = "PENDING", source = "DISCARD"),
    })
    CallbackNotifyRequest.NotifyStatusEnum statusMapping(CommonStatusEnum status);

    @Mappings({
            @Mapping(target = "channelPayCommitNo", source = "channelCommitNo"),
            @Mapping(target = "paymentType", expression = "java(TradeType.PAYOUTS.getChannelPaymentType())"),
            @Mapping(target = "currentStatus", expression = "java(ChannelOrderStatus.SUCCESS.name())"),
            @Mapping(target = "targetStatus", expression = "java(ChannelOrderStatus.BOUNCEBACK.name())"),
            @Mapping(target = "payAmount", source = "payAmount"),
            @Mapping(target = "ignoreCompensateInquiry", expression = "java(Boolean.TRUE)"),
            @Mapping(target = "ignoreBounceBackInquiry", expression = "java(Boolean.TRUE)"),
    })
    CompensateStatusRequest toCompensateOrderStatus(ChannelResultNotifyInfo resultNotifyInfo);
}
