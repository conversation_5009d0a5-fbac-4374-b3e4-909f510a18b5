package com.payermax.operating.correction.integration.config.nacos.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 接入系统配置
 * @date 2022/9/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CorrectionSystemInfo {

    /**
     * 差错Code
     */
    private String correctionCode;

    /**
     * 来源系统
     */
    private String sysSource;

    /**
     * 系统名称
     */
    private String name;

    /**
     * 是否需要回调通知
     */
    private Boolean notifyFlag;

}
