package com.payermax.operating.correction.integration.rpc.ordercenter.repository;

import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderRequest;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderResponse;

/**
 * The interface Order center repository.
 *
 * <AUTHOR>
 * @desc 订单中心资源类
 * @date 2022 /10/10
 */
public interface OrderCenterRepository {

    /**
     * Patch pay order trade order patch order response.
     *
     * @param patchOrderRequest the patch order request
     * @return the trade order patch order response
     */
    TradeOrderPatchOrderResponse patchPayOrder(TradeOrderPatchOrderRequest patchOrderRequest);

    /**
     * Patch trade order patch order response.
     *
     * @param patchOrderRequest the patch order request
     * @return the trade order patch order response
     */
    TradeOrderPatchOrderResponse patchTradeOrder(TradeOrderPatchOrderRequest patchOrderRequest);

    /**
     * Patch trade pay order trade order patch order response.
     *
     * @param patchOrderRequest the patch order request
     * @return the trade order patch order response
     */
    TradeOrderPatchOrderResponse patchTradePayOrder(TradeOrderPatchOrderRequest patchOrderRequest);


    /**
     * Patch duplicate trade order patch response.
     *
     * @param patchOrderRequest the patch order request
     * @return the trade order patch order response
     */
    TradeOrderPatchOrderResponse patchExternalDuplicateTradeOrder(TradeOrderPatchOrderRequest patchOrderRequest);

    /**
     * Patch duplicate trade order patch response.
     *
     * @param patchOrderRequest the patch order request
     * @return the trade order patch order response
     */
    TradeOrderPatchOrderResponse patchInnerDuplicateTradeOrder(TradeOrderPatchOrderRequest patchOrderRequest);

    /**
     * 重复支付触发自动退款，退款失败进行补单操作
     * @param patchOrderRequest
     * @return
     */
    TradeOrderPatchOrderResponse patchRefundFailedDuplicateTradeOrder(TradeOrderPatchOrderRequest patchOrderRequest);
}
