package com.payermax.operating.correction.integration.rpc.merchant.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 查询商户基本信息请求入参
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-11-26 11:36
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantBaseInfoRequest {
    /**
     * 商户号
     */
    private String merchantNo;
    /**
     * 商户appID
     */
    private String appId;
}
