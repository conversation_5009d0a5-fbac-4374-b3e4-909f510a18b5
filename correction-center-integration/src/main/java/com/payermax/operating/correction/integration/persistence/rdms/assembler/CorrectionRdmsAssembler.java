package com.payermax.operating.correction.integration.persistence.rdms.assembler;

import com.alibaba.fastjson.JSONObject;
import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.dto.CorrectionBaseInfo;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.*;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.core.common.utils.ObjectsUtils;
import com.payermax.operating.correction.core.dal.po.*;
import com.payermax.operating.correction.integration.persistence.rdms.dto.*;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Currency;
import java.util.Date;
import java.util.Optional;

/**
 * The interface Operation strategy assembler.
 *
 * <AUTHOR>
 * @desc 运营策略转换器
 * @date 2022 /9/20
 */
@Mapper(componentModel = "spring", imports = {Date.class, Optional.class, Nullable.class, AssemblerUtils.class,
        TradeType.class, CorrectionBaseInfo.class, VoucherInfo.class, Money.class, Currency.class, CommonStatusEnum.class
        , DCVoucherType.class, ValidationRuleConditionDTO.class, OperationBasicInfo.class, ExtendEnum.class,
        StringUtils.class, UserInfoEnum.class, OriginalTradeRedundantInfo.class, JSONObject.class, ValidType.class, ObjectsUtils.class})
public interface CorrectionRdmsAssembler {

    /**
     * Model to po correction operation strategy info.
     *
     * @param model model
     * @return the correction operation strategy info
     */
    @Mappings({
            @Mapping(target = "operationStrategyCode", source = "strategyCode"),
            @Mapping(target = "operationStrategyName", source = "strategyName"),
            @Mapping(target = "extendShow", expression = "java(Optional.ofNullable(model.getExtendShow()).map(com.payermax.operating.correction.core.common.enums.ExtendEnum::name).orElse(StringUtils.EMPTY))"),
            @Mapping(target = "collectionShow", expression = "java(Optional.ofNullable(model.getExtraUserInfo()).map(com.payermax.operating.correction.core.common.enums.UserInfoEnum::name).orElse(StringUtils.EMPTY))"),
            @Mapping(target = "isValid", expression = "java(Optional.ofNullable(model.getOpBasicInfo()).flatMap(e->Optional.ofNullable(e.getValid())).flatMap(e->Optional.ofNullable(e.getVal())).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "operator", expression = "java(Optional.ofNullable(model.getOpBasicInfo()).flatMap(e->Optional.ofNullable(e.getOperator())).orElse(Nullable.getNullVal()))"),
    })
    CorrectionOperationStrategyInfo operationStrategyModelToPo(CorrectionOperationStrategyInfoDTO model);

    /**
     * Operation strategy po to model correction operation strategy info dto.
     *
     * @param po the po
     * @return the correction operation strategy info dto
     */
    @Mappings({
            @Mapping(target = "strategyCode", source = "operationStrategyCode"),
            @Mapping(target = "strategyName", source = "operationStrategyName"),
            @Mapping(target = "extendShow", expression = "java(ExtendEnum.getByName(po.getExtendShow()))"),
            @Mapping(target = "extraUserInfo", expression = "java(UserInfoEnum.getByName(po.getCollectionShow()))"),
            @Mapping(target = "opBasicInfo", expression = "java(AssemblerUtils.getOpBasicInfo(po.getOperator(),po.getIsValid(),po.getUtcCreate(),po.getUtcModified()))"),
    })
    CorrectionOperationStrategyInfoDTO operationStrategyPoToModel(CorrectionOperationStrategyInfo po);

    /**
     * Basic model to po correction basic info.
     *
     * @param info the info
     * @return the correction basic info
     */
    @Mappings({
            @Mapping(target = "tradeType", expression = "java(Optional.ofNullable(info.getTradeType()).map(TradeType::name).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "isValid", expression = "java(Optional.ofNullable(info.getOpBasicInfo()).map(OperationBasicInfo::getValid).map(ValidType::getVal).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "operator", expression = "java(Optional.ofNullable(info.getOpBasicInfo()).flatMap(e->Optional.of(e.getOperator())).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "execValidateRule", expression = "java(Optional.ofNullable(info.getExecValidateRules()).map(JSONObject::toJSONString).orElse(Nullable.getNullVal()))")
    })
    CorrectionBasicInfo basicModelToPo(CorrectionBasicInfoDTO info);

    /**
     * Basic po to model correction basic info dto.
     *
     * @param po the po
     * @return the correction basic info dto
     */
    @Mappings({
            @Mapping(target = "execValidateRules", expression = "java(com.alibaba.fastjson.JSONObject.parseObject(po.getExecValidateRule(),ValidationRuleConditionDTO.class))"),
            @Mapping(target = "opBasicInfo", expression = "java(AssemblerUtils.getOpBasicInfo(po.getOperator(),po.getIsValid(),po.getUtcCreate(),po.getUtcModified()))"),
            @Mapping(target = "tradeType", expression = "java(TradeType.getTradeTypeByName(po.getTradeType()))"),
    })
    CorrectionBasicInfoDTO basicPoToModel(CorrectionBasicInfo po);


    /**
     * Order info model to po correction order info.
     *
     * @param model the correction order info
     * @return the correction order info
     */
    @Mappings({
            @Mapping(target = "correctionNo", source = "baseInfo.correctionNo"),
            @Mapping(target = "correctionCode", source = "baseInfo.correctionCode"),
            @Mapping(target = "voucherNo", expression = "java(Optional.ofNullable(model.getBaseInfo()).map(CorrectionBaseInfo::getVoucherInfo).map(VoucherInfo::getVoucherNo).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "voucherType", expression = "java(Optional.ofNullable(model.getBaseInfo()).map(CorrectionBaseInfo::getVoucherInfo).map(VoucherInfo::getVoucherType).map(Enum::name).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "operationStrategyCode", source = "strategyCode"),
            @Mapping(target = "reqVoucherInfo", expression = "java(Optional.ofNullable(model.getOriVoucherInfo()).map(VoucherInfo::toString).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "resVoucherInfo", expression = "java(Optional.ofNullable(model.getResVoucherInfo()).map(VoucherInfo::toString).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "currency", expression = "java(Optional.ofNullable(model.getPayTotalMoney()).map(Money::getCurrency).map(Currency::toString).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "totalAmount", expression = "java(Optional.ofNullable(model.getPayTotalMoney()).map(Money::getAmount).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "operator", expression = "java(Optional.ofNullable(model.getOpBasicInfo()).flatMap(e->Optional.of(e.getOperator())).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "correctionRemark", source = "detailDesc"),
            @Mapping(target = "tradeType", expression = "java(model.getBaseInfo().getTradeType().name())"),
            @Mapping(target = "originalRedundantInfo", expression = "java(Optional.ofNullable(model.getOriginalTradeRedundantInfo()).map(JSONObject::toJSONString).orElse(Nullable.getNullVal()))"),

    })
    CorrectionOrderInfo orderInfoModelToPo(CorrectionOrderInfoDTO model);

    /**
     * Order info po to model correction order info dto.
     *
     * @param po the correction order info
     * @return the correction order info dto
     */
    @Mappings({
            @Mapping(target = "baseInfo", expression = "java(AssemblerUtils.getBaseInfo(po.getCorrectionNo(),po.getCorrectionCode(),po.getVoucherNo(),po.getVoucherType(),po.getCorrectionRemark(),po.getOriginalRedundantInfo(),po.getTradeType()))"),
            @Mapping(target = "operationCorrectionCode", source = "operationCorrectionCode"),
            @Mapping(target = "strategyCode", source = "operationStrategyCode"),
            @Mapping(target = "channelCode", source = "channelCode"),
            @Mapping(target = "detailDesc", source = "correctionRemark"),
            @Mapping(target = "reviewer", source = "reviewer"),
            @Mapping(target = "oriVoucherInfo", expression = "java(AssemblerUtils.getVoucherInfo(po.getReqVoucherInfo()))"),
            @Mapping(target = "resVoucherInfo", expression = "java(AssemblerUtils.getVoucherInfo(po.getResVoucherInfo()))"),
            @Mapping(target = "payTotalMoney", expression = "java(ObjectsUtils.buildMoney(po.getTotalAmount(),po.getCurrency()))"),
            @Mapping(target = "operationManual", expression = "java(AssemblerUtils.getOrderInfoOperation(po.getMemo(),po.getExtendInfo(),po.getUserInfo()))"),
            @Mapping(target = "opBasicInfo", expression = "java(new OperationBasicInfo(po.getOperator(),po.getUtcCreate().getTime(),po.getUtcModified().getTime(),po.getCompleteTime()))"),
            @Mapping(target = "originalTradeRedundantInfo", expression = "java(AssemblerUtils.getOriginalRedundantInfo(po.getOriginalRedundantInfo()))"),
    })
    CorrectionOrderInfoDTO orderInfoPoToModel(CorrectionOrderInfo po);

    /**
     * Handle record model to po correction order handler record.
     *
     * @param handlerRecord the handler record
     * @return the correction order handler record
     */
    @Mappings({
            @Mapping(target = "correctionNo", source = "handlerInfo.correctionNo"),
            @Mapping(target = "strategyCode", source = "handlerInfo.strategyCode"),
            @Mapping(target = "handlerType", source = "handlerInfo.handlerType"),
            @Mapping(target = "processRequest", source = "handlerInfo.processRequest"),
            @Mapping(target = "processStatus", expression = "java(handlerRecord.getStatus().name())"),
            @Mapping(target = "operator", expression = "java(Optional.ofNullable(handlerRecord.getOpBasicInfo()).map(OperationBasicInfo::getOperator).orElse(StringUtils.EMPTY))"),
    })
    CorrectionOrderHandlerRecord handleRecordModelToPo(CorrectionOrderHandlerRecordDTO handlerRecord);

    /**
     * Handler record po to model correction order handler record dto.
     *
     * @param po the correction order handler record
     * @return the correction order handler record dto
     */
    @Mappings({
            @Mapping(target = "handlerInfo.correctionNo", source = "correctionNo"),
            @Mapping(target = "handlerInfo.strategyCode", source = "strategyCode"),
            @Mapping(target = "handlerInfo.handlerType", source = "handlerType"),
            @Mapping(target = "handlerInfo.processRequest", source = "processRequest"),
            @Mapping(target = "processResult", source = "processResult"),
            @Mapping(target = "status", expression = "java(CommonStatusEnum.valueOf(po.getProcessStatus()))"),
            @Mapping(target = "opBasicInfo", expression = "java(AssemblerUtils.getOpBasicInfo(po.getOperator(),po.getUtcCreate(),po.getUtcModified()))"),
    })
    CorrectionOrderHandlerRecordDTO handlerRecordPoToModel(CorrectionOrderHandlerRecord po);

    /**
     * To operation uni correction operation unique dto.
     *
     * @param correctionOperationStrategyMapping the correction operation strategy mapping
     * @return the correction operation unique dto
     */
    @Mappings({
            @Mapping(target = "strategyCode", source = "operationStrategyCode"),
            @Mapping(target = "tradeType", expression = "java(TradeType.getTradeTypeByName(correctionOperationStrategyMapping.getTradeType()))"),
    })
    CorrectionOperationUniqueDTO toOperationUni(CorrectionOperationStrategyMapping correctionOperationStrategyMapping);
}
