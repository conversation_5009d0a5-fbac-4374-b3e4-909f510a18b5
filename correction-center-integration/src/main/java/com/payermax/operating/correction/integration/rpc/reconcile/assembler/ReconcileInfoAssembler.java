package com.payermax.operating.correction.integration.rpc.reconcile.assembler;

import com.payermax.channel.reconcile.response.QueryFileExternalOrderResponse;
import com.payermax.operating.correction.integration.rpc.reconcile.dto.ReconcileInfo;
import org.mapstruct.*;

import java.util.List;

/**
 * The interface Financial exchange assembler.
 *
 * <AUTHOR>
 * @desc 金融交换转换器
 * @date 2023 /4/18
 */
@Mapper(componentModel = "spring", nullValueMappingStrategy = NullValueMappingStrategy.RETURN_NULL, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
public interface ReconcileInfoAssembler {

    List<ReconcileInfo> toReconcileInfo (List<QueryFileExternalOrderResponse> queryFileExternalOrderResponse);
}
