package com.payermax.operating.correction.integration.persistence.rdms.dto;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 渠道对账冗余信息
 * @date 2023/5/23
 */
@Data
@NoArgsConstructor
public class ReconcileRedundantDTO extends OriginalTradeRedundantInfo {

    /**
     * 主体
     */
    private String entity;

    /**
     * 机构名称
     */
    @Getter
    private String orgName;

    /**
     * 完成时间(外部完成时间)
     */
    private String completeTime;

    /**
     * 提交单完成时间(金融交换)
     */
    private String commitCompleteTime;

    /**
     * 外部状态
     */
    private String channelStatus;

    /**
     * 提交单状态(金融交换)
     */
    private String commitStatus;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 批次信息
     */
    private String batchNo;

    /**
     * 提交单号
     */
    private String channelCommitNo;

    /**
     * 三方单号
     */
    private String thirdOrderNo;

    /**
     * 四方单号
     */
    private String fourthOrderNo;

    /**
     * 支付金额
     */
    private Money payAmount;

    /**
     * 对账规则Id
     */
    private String reconcileRuleId;

    /**
     * 交易对账差错凭证号
     */
    private String errorOrderNo;

    /**
     * 交易类型(外部)
     */
    private String channelTradeType;

    /**
     * 客资负责人
     */
    @Getter
    private String owner;

    public String queryChannelCommitNoStr() {
        if (StringUtils.isEmpty(channelCommitNo)) {
            return StringUtils.EMPTY;
        }
        return " & +\"channelCommitNo\" & +\"" + channelCommitNo + "\"";
    }

    public String queryThirdOrderNoStr() {
        if (StringUtils.isEmpty(thirdOrderNo)) {
            return StringUtils.EMPTY;
        }
        return " & +\"thirdOrderNo\" & +\"" + thirdOrderNo + "\"";
    }

    public String queryFourthOrderNoStr() {
        if (StringUtils.isEmpty(fourthOrderNo)) {
            return StringUtils.EMPTY;
        }
        return " & +\"fourthOrderNo\" & +\"" + fourthOrderNo + "\"";
    }

    public String queryBatchNoStr() {
        if (StringUtils.isEmpty(batchNo)) {
            return StringUtils.EMPTY;
        }
        return " & +\"batchNo\" & +\"" + batchNo + "\"";
    }

    public String queryReconcileRuleIdStr() {
        if (StringUtils.isEmpty(reconcileRuleId)) {
            return StringUtils.EMPTY;
        }
        return " & +\"reconcileRuleId\" & +\"" + reconcileRuleId + "\"";
    }

    public String queryOrgNameStr() {
        if (StringUtils.isEmpty(orgName)) {
            return StringUtils.EMPTY;
        } else if (orgName.length() <= CorrectionConstant.NUM_TWO) {
            this.orgName = CorrectionConstant.FULL_TEXT_PREFIX + orgName;
        } else if (orgName.contains(",")){
            List<String> list = Arrays.asList(orgName.split(","));
            for (int i =0;i<list.size();i++){
                if (list.get(i).trim().length()<= CorrectionConstant.NUM_TWO){
                    list.set(i,"\"" + CorrectionConstant.FULL_TEXT_PREFIX + list.get(i) + "\"" );
                } else {
                    list.set(i,"\"" + list.get(i) + "\"" );
                }
            }
            orgName = StringUtils.strip(list.toString(),"[]");

            return " & +\"orgName\" & +(" + orgName + ")";
        }
        return " & +\"orgName\" & +\"" + orgName + "\"";
    }

    public String queryOwnerStr() {
        if (StringUtils.isEmpty(owner)) {
            return StringUtils.EMPTY;
        } else if (owner.length() <= CorrectionConstant.NUM_TWO) {
            this.owner = CorrectionConstant.FULL_TEXT_PREFIX + owner;
        } else if (owner.contains(",")){
            List<String> list = Arrays.asList(owner.split(","));
            for (int i =0;i<list.size();i++){
                if (list.get(i).trim().length()<= CorrectionConstant.NUM_TWO){
                    list.set(i,"\"" + CorrectionConstant.FULL_TEXT_PREFIX + list.get(i) + "\"" );
                } else {
                    list.set(i,"\"" + list.get(i) + "\"" );
                }
            }
            owner = StringUtils.strip(list.toString(),"[]");
            return " & +\"owner\" & +(" + owner + ")";
        }
        return " & +\"owner\" & +\"" + owner + "\"";
    }

    public String queryEntityStr() {
        if (StringUtils.isEmpty(entity)) {
            return StringUtils.EMPTY;
        }
        return " & +\"entity\" & +\"" + entity + "\"";
    }

    public String queryRedundantInfoStr() {
        return queryChannelCommitNoStr() +
                queryThirdOrderNoStr() +
                queryFourthOrderNoStr() +
                queryBatchNoStr() +
                queryOrgNameStr() +
                queryEntityStr() +
                queryReconcileRuleIdStr() +
                queryOwnerStr() +
                super.queryMerchantNoStr();
    }

    public void setOrgName(String orgName) {
        if (StringUtils.isNotBlank(orgName) && orgName.length() <= CorrectionConstant.NUM_TWO) {
            this.orgName = CorrectionConstant.FULL_TEXT_PREFIX + orgName;
        } else {
            this.orgName = orgName;
        }
    }

    public void setOwner(String owner) {
        if (StringUtils.isNotBlank(owner) && owner.length() <= CorrectionConstant.NUM_TWO) {
            this.owner = CorrectionConstant.FULL_TEXT_PREFIX + owner;
        } else {
            this.owner = owner;
        }
    }
}
