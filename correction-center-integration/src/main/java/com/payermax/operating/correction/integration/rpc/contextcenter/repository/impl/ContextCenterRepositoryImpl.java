package com.payermax.operating.correction.integration.rpc.contextcenter.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.payermax.basic.contexcenter.service.client.ContextCenterManager;
import com.payermax.basic.contexcenter.service.client.enums.PaymentContextTypeEnum;
import com.payermax.basic.contexcenter.service.client.model.payment.PaymentContextInfo;
import com.payermax.basic.contexcenter.service.client.model.payment.PaymentContextSaveRequestDTO;
import com.payermax.fin.exchange.service.context.PayoutRequestContext;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.dto.CorrectionPayoutsInfo;
import com.payermax.operating.correction.integration.dto.PaymentInstanceDTO;
import com.payermax.operating.correction.integration.rpc.contextcenter.repository.ContextCenterRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 上下文资源实现类
 * @date 2022/12/13
 */
@Repository
@Slf4j
public class ContextCenterRepositoryImpl implements ContextCenterRepository {

    @Resource
    private ContextCenterManager contextCenterManager;

    @Override
    public void savePayoutContextCenter(CorrectionPayoutsInfo correctionPayoutsInfo, JSONObject userJson,String payoutsNo) {
        PaymentContextInfo paymentContextInfo = new PaymentContextInfo();
        PayoutRequestContext payoutRequestContext = new PayoutRequestContext();
        paymentContextInfo.setPayoutRequestInfo(payoutRequestContext);

        //出款上下文信息
        payoutRequestContext.setPaymentMethod(this.buildPaymentMethod(correctionPayoutsInfo.getPayPaymentInstanceInfo().getPaymentInstanceDTO().getExtendPropertyList(), correctionPayoutsInfo.getPayPaymentInstanceInfo().getCountry()));
        payoutRequestContext.setOrderInfo(this.buildOrderInfo(correctionPayoutsInfo.getMerchantNo()));
        Map paramMap = userJson.getInnerMap();
        //出款逻辑特殊处理(兼容新老出款)
        String payer = (String) paramMap.get("payer");
        if (StringUtils.isNotEmpty(payer)) {
            paramMap.put("payerName", payer);
        }
        String email = (String) paramMap.get("email");
        if (StringUtils.isNotEmpty(payer)) {
            paramMap.put("emailId", email);
        }
        String payeeName = (String) paramMap.get("payeeName");
        if (StringUtils.isNotEmpty(payeeName)) {
            paramMap.put("payeeAccountName", payeeName);
        }
        payoutRequestContext.setParams(paramMap);

        contextCenterManager.savePaymentContext(paymentContextInfo, this.buildContextSaveRequestInfo(correctionPayoutsInfo.getPayoutNo()));

    }

    private PaymentContextSaveRequestDTO buildContextSaveRequestInfo(String payoutNo) {
        PaymentContextSaveRequestDTO saveRequestDTO = new PaymentContextSaveRequestDTO();
        saveRequestDTO.setType(PaymentContextTypeEnum.PAYOUT_REQUEST);
        saveRequestDTO.setRequestNo(payoutNo);
        saveRequestDTO.setTimeOut(3600L);
        saveRequestDTO.setIsUpdateExpireTime(Boolean.TRUE);
        return saveRequestDTO;
    }

    private PayoutRequestContext.OrderInfo buildOrderInfo(String merchantNo) {
        PayoutRequestContext.OrderInfo orderInfo = new PayoutRequestContext.OrderInfo();
        orderInfo.setMerchantNo(merchantNo);
        //不做校验
        orderInfo.setUserMemberId(merchantNo);
        return orderInfo;
    }

    private PayoutRequestContext.PaymentMethod buildPaymentMethod(List<PaymentInstanceDTO.ExtendProperty> list, String country) {
        PayoutRequestContext.PaymentMethod paymentMethod = new PayoutRequestContext.PaymentMethod();
        paymentMethod.setPaymentType(CorrectionConstant.PAYOUTS_PAYMENT_METHODS);
        paymentMethod.setCountry(country);
        paymentMethod.setCustomerType(CorrectionConstant.PAYOUTS_USER_TYPE);
        paymentMethod.setExtendProperties(this.convertExtendProperty(list));
        return paymentMethod;
    }

    private List<PayoutRequestContext.PaymentMethod.ExtendProperty> convertExtendProperty(List<PaymentInstanceDTO.ExtendProperty> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Nullable.getNullVal();
        }
        return list.stream().map(e -> {
            PayoutRequestContext.PaymentMethod.ExtendProperty extendProperty = new PayoutRequestContext.PaymentMethod.ExtendProperty();
            extendProperty.setKey(e.getKey());
            extendProperty.setLogicKey(e.getLogicKey());
            extendProperty.setValue(e.getValue());
            return extendProperty;
        }).collect(Collectors.toList());
    }
}
