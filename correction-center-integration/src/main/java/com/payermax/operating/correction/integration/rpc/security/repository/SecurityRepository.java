package com.payermax.operating.correction.integration.rpc.security.repository;

/**
 * The interface Security repository.
 *
 * <AUTHOR>
 * @desc 安全服务资源接口
 * @date 2022 /12/13
 */
public interface SecurityRepository {

    /**
     * Encrypt string.
     *
     * @param json the json
     * @return the string
     */
    String encrypt(String json);


    /**
     * Decrypt string.
     *
     * @param token the token
     * @return the string
     */
    String decrypt(String token);

}
