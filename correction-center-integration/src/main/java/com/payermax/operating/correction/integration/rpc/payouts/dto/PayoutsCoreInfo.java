package com.payermax.operating.correction.integration.rpc.payouts.dto;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.integration.rpc.productcenter.dto.CashierProductInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 出款核心信息
 * @date 2022/12/11
 */
@Data
public class PayoutsCoreInfo {

    private CashierProductInfo cashierProductInfo;

    private Money payOutsMoney;

    /**
     * 差错单号
     */
    private String correctionNo;
}
