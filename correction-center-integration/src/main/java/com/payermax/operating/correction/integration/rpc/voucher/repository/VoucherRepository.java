package com.payermax.operating.correction.integration.rpc.voucher.repository;

import com.payermax.operating.correction.integration.rpc.voucher.repository.dto.VoucherRequestDTO;

/**
 * The interface Voucher repository.
 *
 * <AUTHOR>
 * @desc 凭证rpc调用资源类
 * @date 2023 /3/6
 */
public interface VoucherRepository {

    /**
     * Voucher no string.
     *
     * @param voucherInfo voucher info
     * @return the string
     */
    String getCorrectionVoucherNo(VoucherRequestDTO voucherInfo);

    /**
     * Gets payment voucher no.
     *
     * @param voucherInfo the voucher info
     * @return the payment voucher no
     */
    String getPaymentVoucherNo(VoucherRequestDTO voucherInfo);

    /**
     * Gets trade voucher no.
     *
     * @param voucherInfo the voucher info
     * @return the trade voucher no
     */
    String getTradeVoucherNo(VoucherRequestDTO voucherInfo);
}
