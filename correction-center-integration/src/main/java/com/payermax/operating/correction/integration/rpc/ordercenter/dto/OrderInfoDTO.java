package com.payermax.operating.correction.integration.rpc.ordercenter.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 订单中心修补订单对象
 * @date 2023/3/9
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderInfoDTO {

    /**
     * 外部交易单号
     */
    private String outTradeNo;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * merchantId
     */
    private String merchantAppId;

    /**
     * 请求单号
     */
    private String payRequestNo;

    /**
     * 交易单号
     */
    private String tradeOrderNo;
}
