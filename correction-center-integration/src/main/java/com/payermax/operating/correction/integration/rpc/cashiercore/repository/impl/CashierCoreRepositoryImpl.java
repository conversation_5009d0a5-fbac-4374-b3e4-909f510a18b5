package com.payermax.operating.correction.integration.rpc.cashiercore.repository.impl;

import com.payermax.cashier.core.facade.PaymentMethodServiceFacade;
import com.payermax.cashier.core.facade.model.base.ExtendProperty;
import com.payermax.cashier.core.facade.model.base.PaymentMethodInfo;
import com.payermax.cashier.core.facade.model.base.TradeInfo;
import com.payermax.cashier.core.facade.model.request.PaymentMethodInstanceRequestDTO;
import com.payermax.cashier.core.facade.model.response.PaymentMethodInstanceResponseDTO;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.dto.PaymentInstanceDTO;
import com.payermax.operating.correction.integration.rpc.cashiercore.dto.CashierCorePaymentInstanceInfo;
import com.payermax.operating.correction.integration.rpc.cashiercore.repository.CashierCoreRepository;
import com.payermax.operating.correction.integration.utils.IntegrationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 收银核心接口资源实现类
 * @date 2022/12/13
 */
@Repository
@Slf4j
public class CashierCoreRepositoryImpl implements CashierCoreRepository {

    @DubboReference(version = "1.0", retries = 2, timeout = 5000)
    private PaymentMethodServiceFacade paymentMethodServiceFacade;

    @Override
    public void getPaymentInstance(CashierCorePaymentInstanceInfo cashierCorePaymentInstanceInfoDTO, Money payAmount, String bizIdentify, String merchantNo) {
        PaymentMethodInstanceRequestDTO instanceRequestDTO = new PaymentMethodInstanceRequestDTO();
        //交易国家
        TradeInfo tradeInfo = new TradeInfo();
        tradeInfo.setCountry(cashierCorePaymentInstanceInfoDTO.getCountry());
        tradeInfo.setAmount(payAmount.getAmount());
        tradeInfo.setCurrency(payAmount.getCurrency().getCurrencyCode());
        tradeInfo.setMerchantId(merchantNo);
        tradeInfo.setPayeeType(CorrectionConstant.PAYOUTS_PAYEE_TYPE);
        //支付方式实例
        List<PaymentMethodInfo> paymentMethodInfos = cashierCorePaymentInstanceInfoDTO.getPaymentInstanceList().stream().map(e -> {
            PaymentMethodInfo paymentMethodInfo = new PaymentMethodInfo();
            paymentMethodInfo.setPaymentMethodNo(e.getPaymentMethodNo());
            paymentMethodInfo.setBizIdentify(bizIdentify);
            paymentMethodInfo.setProductCode(CorrectionConstant.PAYOUTS_PRODUCT_CODE);
            paymentMethodInfo.setPaymentType(cashierCorePaymentInstanceInfoDTO.getPaymentType());
            paymentMethodInfo.setPayAmount(IntegrationUtils.toMoney(payAmount));
            paymentMethodInfo.setTargetOrg(StringUtils.defaultIfBlank(e.getTargetOrg(),Nullable.getNullVal()));
            return paymentMethodInfo;
        }).collect(Collectors.toList());
        //
        instanceRequestDTO.setTrade(tradeInfo);
        instanceRequestDTO.setPaymentMethod(paymentMethodInfos);
        Result<PaymentMethodInstanceResponseDTO> ret = paymentMethodServiceFacade.getPaymentInstance(instanceRequestDTO);
        AssertUtil.isTrue(ResultUtil.isApplySuccess(ret) && Objects.nonNull(ret.getData()) && CollectionUtils.isNotEmpty(ret.getData().getPaymentInstance()), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_CASHIER_CORE_EXCEPTION);
        this.convertPaymentInstance(ret.getData(), cashierCorePaymentInstanceInfoDTO);
    }

    private void convertPaymentInstance(PaymentMethodInstanceResponseDTO instanceResponseDTO, CashierCorePaymentInstanceInfo cashierCorePaymentInstanceInfo) {
        List<PaymentInstanceDTO> list = instanceResponseDTO.getPaymentInstance().stream().map(e ->
                new PaymentInstanceDTO(e.getPaymentMethodNo(), e.getBizIdentify(), e.getTargetOrg(), e.getTargetOrgName(), this.getExtendList(e.getExtendProperties()))).collect(Collectors.toList());
        cashierCorePaymentInstanceInfo.setPaymentInstanceList(list);
    }

    private List<PaymentInstanceDTO.ExtendProperty> getExtendList(List<ExtendProperty> extendProperties) {
        if (CollectionUtils.isEmpty(extendProperties)) {
            return Nullable.getNullVal();
        }
        return extendProperties.stream().map(e -> new PaymentInstanceDTO.ExtendProperty(e.getKey(), e.getValue(), e.getLogicKey())).collect(Collectors.toList());
    }
}
