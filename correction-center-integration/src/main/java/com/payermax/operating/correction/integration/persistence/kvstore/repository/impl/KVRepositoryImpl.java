package com.payermax.operating.correction.integration.persistence.kvstore.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.persistence.kvstore.repository.KVRepository;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @desc 缓存资源实现类
 * @date 2023/7/13
 */
@Repository
@Slf4j
public class KVRepositoryImpl implements KVRepository {
    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @NacosValue(value = "${cache.global.order.timeout:30}", autoRefreshed = true)
    private Integer cacheGlobalOrderTime;

    @Override
    public void storeGlobalOrder(VoucherInfo voucherInfo, GlobalOrderInfo orderInfo) {
        redisTemplate.opsForValue().set(voucherInfo.uniqueNo(), JSONObject.toJSONString(orderInfo), cacheGlobalOrderTime, TimeUnit.MINUTES);
    }

    @Override
    public GlobalOrderInfo queryGlobalOrder(VoucherInfo voucherInfo) {
        GlobalOrderInfo globalOrderInfo = Nullable.getNullVal();
        try {
            String globalOrderInfoStr = (String) redisTemplate.opsForValue().get(voucherInfo.uniqueNo());
            if(Symbols.PARANTHESES_STR.equals(globalOrderInfoStr)){
                return globalOrderInfo;
            }
            globalOrderInfo = JSONObject.parseObject(globalOrderInfoStr, new TypeReference<GlobalOrderInfo>() {
            });
        } catch (Exception e) {
            log.error("queryGlobalOrder redis error:", e);
        }
        return globalOrderInfo;
    }

    @Override
    public void removeGlobalOrder(VoucherInfo voucherInfo) {
        redisTemplate.delete(voucherInfo.uniqueNo());
    }

    @Override
    public Boolean applyDistributeLock(String key, String value, long second) {
        return redisTemplate.opsForValue().setIfAbsent(key, value, Duration.ofSeconds(second));
    }

    @Override
    public Boolean releaseDistributeLock(String key, String expValue) {
        String actualValue = (String) redisTemplate.opsForValue().get(key);
        if (expValue.equals(actualValue)) {
            redisTemplate.delete(key);
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    @Override
    public Long incr(String key) {
        return redisTemplate.opsForValue().increment(key);
    }

    @Override
    public Boolean expire(String key, Long timeOut, TimeUnit unit) {
        Boolean flag = Boolean.FALSE;
        try {
            flag = redisTemplate.expire(key, timeOut, unit);
        } catch (Exception e) {
            log.info("KVRepositoryImpl expire is fail");
        }
        return flag;
    }

    @Override
    public Boolean remove(String key) {
        return redisTemplate.delete(key);
    }
}
