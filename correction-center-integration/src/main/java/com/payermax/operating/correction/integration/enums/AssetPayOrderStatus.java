package com.payermax.operating.correction.integration.enums;

import com.payermax.operating.correction.core.common.utils.Nullable;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc 资产交换订单状态
 * @date 2022/10/17
 */
public enum AssetPayOrderStatus {

    WAIT_PAY("1","等待中"),

    /**
     * 支付成功
     */
    SUCCESS("2","成功"),

    /**
     * 支付失败
     */
    FAILED("3","失败"),

    /**
     * 处理中
     */
    PROCESSING("4","处理中"),

    /**
     * 处理中
     */
    REVOKED("5","支付撤销"),

    /**
     * 处理中
     */
    BOUNCEBACK("6","退票"),
    ;

    AssetPayOrderStatus(String val, String name) {
        this.val = val;
        this.name = name;
    }

    @Getter
    private String val;

    @Getter
    private String name;

    public static AssetPayOrderStatus getByVal(String val) {
        return Arrays.stream(values()).filter(e -> e.getVal().equals(val)).findFirst().orElse(Nullable.getNullVal());//CHECKED 枚举匹配出来必然只有一个
    }
}
