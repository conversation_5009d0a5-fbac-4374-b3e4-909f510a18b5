package com.payermax.operating.correction.integration.rpc.merchant;

import com.payermax.operating.correction.integration.rpc.merchant.dto.MerchantBaseInfo;
import com.payermax.operating.correction.integration.rpc.merchant.dto.MerchantBaseInfoRequest;
import com.payermax.operating.correction.integration.rpc.merchant.dto.BatchQueryMerchantBaseInfoReps;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 商服服务调用资源类
 */
public interface MerchantReository {

    MerchantBaseInfo getMerchantBaseInfo(MerchantBaseInfoRequest request);

    @NotNull
    BatchQueryMerchantBaseInfoReps batchQueryMerchantBaseInfo(List<String> merchantNoList);
}
