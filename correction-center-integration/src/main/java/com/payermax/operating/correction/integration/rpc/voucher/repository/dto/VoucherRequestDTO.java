package com.payermax.operating.correction.integration.rpc.voucher.repository.dto;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.enums.TradeType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @desc 凭证对象
 * @date 2023/3/17
 */
@NoArgsConstructor
@AllArgsConstructor
@Getter
@ToString
public class VoucherRequestDTO {
    private String requestNo;

    private TradeType tradeType;

    private String productCode;

    private String bizIdentity;

    private String merchantNo;

    private String merchantOrderNo;

    private String correctionCode;

    private Money payAmount;

    private String accountNo;


    public VoucherRequestDTO(String requestNo, TradeType tradeType, String correctionCode) {
        this.requestNo = requestNo + Symbols.LINE + correctionCode;
        this.tradeType = tradeType;
    }

    public VoucherRequestDTO(String requestNo ,String correctionCode, TradeType tradeType, Money payAmount, String accountNo) {
        this.requestNo = requestNo  + Symbols.LINE + correctionCode;
        this.tradeType = tradeType;
        this.payAmount = payAmount;
        this.accountNo = accountNo;
    }
}


