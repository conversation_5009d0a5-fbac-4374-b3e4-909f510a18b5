package com.payermax.operating.correction.integration.rpc.productcenter.dto;

import com.payermax.operating.correction.integration.dto.PaymentInstanceDTO;
import com.payermax.operating.correction.integration.rpc.cashiercore.dto.CashierCorePaymentInstanceInfo;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @desc 收银产品信息
 * @date 2022/12/8
 */
@Getter
@Setter
@NoArgsConstructor
public class CashierProductInfo {

    private String cashierProductNo;

    private PaymentInstanceDTO paymentInstance;

    private String paymentType;

    private String country;

}
