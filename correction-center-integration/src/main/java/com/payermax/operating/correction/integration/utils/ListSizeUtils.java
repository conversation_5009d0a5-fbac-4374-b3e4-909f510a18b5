package com.payermax.operating.correction.integration.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.operating.correction.integration.config.nacos.constant.NacosGlobalConfigProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * @description: list大小日志打印工具类
 * @author: Wang<PERSON>ao
 * @create: 2024-04-10 17:28
 **/
@Slf4j
@Service
public class ListSizeUtils {

    /**
     * findFirst类日志打印方法
     *
     * @param objectStream
     * @param errorMsg
     */
    public static <T> void log(Stream<T> objectStream, String errorMsg) {
        try {
            if (objectStream.count() > 1) {
                log.error("listSizeGT1 {}",errorMsg);
            }
        } catch (Exception e) {
            log.error("findFirstLogFail ", e);
        }
    }

    /**
     * list类日志打印方法
     *
     * @param objectLists
     * @param errorMsg
     * @param <T>
     */
    public static <T> void log(List<T> objectLists, String errorMsg) {
        try {
            if (CollectionUtils.isNotEmpty(objectLists) && objectLists.size() > 1) {
                log.error("listSizeGT1 {}",errorMsg);
            }
        } catch (Exception e) {
            log.error("listLogFail ", e);
        }
    }

    /**
     * 判断listsize 是否等于1
     * @param objectLists
     * @return
     * @param <T>
     */
    public static <T> boolean listSizeEqualOne(List<T> objectLists,String errorMsg) {
        try {
            NacosGlobalConfigProperties nacosConfigUtils = SpringUtil.getBean(NacosGlobalConfigProperties.class);
            //开关未打开 直接返回true
            if(BooleanUtils.isNotTrue(nacosConfigUtils.isOpenSizeValidation())){
                return Boolean.TRUE;
            }
            if (CollectionUtils.isNotEmpty(objectLists) && objectLists.size() == 1) {
               return Boolean.TRUE;
            }
        } catch (Exception e) {
            log.error("listSizeEqualOneFail ", e);
            return Boolean.TRUE;
        }
        log.error("listSizeGT1 {}",errorMsg);
        return Boolean.FALSE;
    }

}
