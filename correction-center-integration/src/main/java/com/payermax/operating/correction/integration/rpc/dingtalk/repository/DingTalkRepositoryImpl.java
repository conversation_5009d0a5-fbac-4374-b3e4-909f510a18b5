package com.payermax.operating.correction.integration.rpc.dingtalk.repository;

import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.integration.config.nacos.constant.NacosGlobalConfigProperties;
import com.payermax.operating.correction.integration.rpc.dingtalk.dto.DingTalkMessageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class DingTalkRepositoryImpl implements DingTalkRepository {

    @Resource
    private NacosGlobalConfigProperties configProperties;

    @Override
    public void sendDingTalkMsg(DingTalkMessageInfo dingTalkMessageInfo) {
        String titleColor = StringUtils.defaultIfBlank(dingTalkMessageInfo.getColor(), "#000000");
        String msg = dingTalkMessageInfo.getMsg();
        String title = dingTalkMessageInfo.getTitle();
        String text = MessageFormat.format("### <font color={0}>{1}</font>\n---\n#### {2}", titleColor, title, msg);
        if (CollectionUtils.isNotEmpty(dingTalkMessageInfo.getAtMobiles())) {
            String mobiles = dingTalkMessageInfo.getAtMobiles().stream().collect(Collectors.joining(Symbols.ALERT, Symbols.ALERT, ""));
            text = text + mobiles;
        } else {
            text = text + "@所有人";
        }

        //构建markdown钉钉通知报文
        HashMap<String, Object> resultMap = new HashMap<>();
        resultMap.put("msgtype", "markdown");

        HashMap<String, String> markdownItems = new HashMap<>();
        markdownItems.put("title", title);
        markdownItems.put("text", text);
        resultMap.put("markdown", markdownItems);

        HashMap<String, Object> atItems = new HashMap<>();
        atItems.put("atMobiles", dingTalkMessageInfo.getAtMobiles());
        atItems.put("isAtAll", Boolean.FALSE);
        resultMap.put("at", atItems);

        try {
            String respBody = HttpRequest.post(configProperties.getDingTalkAlertUrl() + dingTalkMessageInfo.getToken())
                    .body(JSON.toJSONString(resultMap))//表单内容
                    .timeout(dingTalkMessageInfo.getTimeOut())//超时，毫秒
                    .execute().body();
            log.info("DingTalkRepositoryImpl sendDingTalkMsg messageInfo resp [{}]:", respBody);
        } catch (HttpException e) {
            log.error("DingTalkRepositoryImpl sendDingTalkMsg base system exception:", e);
        }
    }
}
