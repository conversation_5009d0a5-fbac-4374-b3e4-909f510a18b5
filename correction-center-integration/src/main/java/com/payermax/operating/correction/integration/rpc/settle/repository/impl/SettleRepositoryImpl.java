package com.payermax.operating.correction.integration.rpc.settle.repository.impl;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.integration.rpc.settle.repository.SettleRepository;
import com.payermax.operating.correction.integration.rpc.settle.repository.assembler.SettleAssembler;
import com.payermax.trade.settle.service.facade.api.SettleApplyFacade;
import com.payermax.trade.settle.service.facade.entity.resp.RefundBackClearResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 结算资源类
 * @date 2023/4/17
 */
@Repository
@Slf4j
public class SettleRepositoryImpl implements SettleRepository {

    @DubboReference(version = "1.0", timeout = 5000)
    private SettleApplyFacade settleApplyFacade;

    @Resource
    private SettleAssembler settleAssembler;

    @Override
    public BaseResProcess patchTradeClearing(String correctionNo, String refundTradeNo) {
        Result<RefundBackClearResp> backClearResp = settleApplyFacade.refundBackClear(settleAssembler.toRefundBackReq(correctionNo, refundTradeNo));
        return settleAssembler.toBaseRes(backClearResp.getData(),refundTradeNo);
    }
}
