package com.payermax.operating.correction.integration.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum TradeOrderStatus {
    //交易单状态
    INIT("0", "初始化"),
    PAY_SUCCESS("1", "支付成功"),
    TRADE_FAIL("2", "交易失败"),
    PENDING("3", "待支付"),
    TRADE_CLOSE("4", "交易关单"),
    TRADE_SUCCESS("5", "交易成功"),
    SETTLE_SUCCESS("6", "结算成功"),
    ;

    private final String code;
    private final String desc;

    private static final Map<String,TradeOrderStatus> CODE_STATUS_MAPPING =
            Arrays.stream(values()).collect(Collectors.toMap(TradeOrderStatus::getCode, Function.identity()));

    public static TradeOrderStatus getByCode(String code) {
        return CODE_STATUS_MAPPING.get(code);
    }

    /**
     * 内部状态机转换成商户识别的（0：支付处理中、1：支付成功、2：支付失败）
     * @param status 订单原始状态
     * @return 商户识别状态
     */
    public static TradeOrderStatus convertStatusForMerchant(TradeOrderStatus status) {
        switch (status){
            case PENDING:
            case INIT:
                return INIT;
            case TRADE_FAIL:
            case TRADE_CLOSE:
                return TRADE_FAIL;
            case PAY_SUCCESS:
            case TRADE_SUCCESS:
            case SETTLE_SUCCESS:
                return PAY_SUCCESS;
            default:
                return status;
        }
    }


    public static boolean isSuccessState(TradeOrderStatus status) {
        return PAY_SUCCESS == status
                || TRADE_SUCCESS == status
                || SETTLE_SUCCESS == status;
    }
}
