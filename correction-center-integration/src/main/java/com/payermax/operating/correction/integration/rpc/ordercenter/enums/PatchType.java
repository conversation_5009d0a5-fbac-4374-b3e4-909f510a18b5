package com.payermax.operating.correction.integration.rpc.ordercenter.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @desc 订单中心修复类型
 * @date 2022/10/10
 */
public enum PatchType {
    PATCH_PAY_ORDER("INNER"),
    PATCH_TRADE_ORDER("INNER"),
    PATCH_TRADE_PAY_ORDER("INNER"),
    DUPLICATE_TRADE_ORDER("EXTERNAL");

    @Getter
    private String type;

    PatchType(String type) {
        this.type = type;
    }
}
