package com.payermax.operating.correction.integration.config.nacos.constant;


import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
@Getter
@Setter
@Configuration
@Slf4j
public class NacosGlobalConfigProperties {

    /**
     * 退款延迟时间
     */
    @NacosValue(value = "${correction.refund.delay.time:10000}", autoRefreshed = true)
    private Long refundDelayTime;

    /**
     * 交易对账判断标识
     */
    @NacosValue(value = "${biz.channel.reconcile.special.symbol:U}", autoRefreshed = true)
    protected String channelReconcileSymbol;

    /**
     * 交易对账判断值
     */
    @NacosValue(value = "${biz.channel.reconcile.special.condition:CC001-PAYMENT}", autoRefreshed = true)
    protected String channelReconcileConditionValue;

    /**
     * 差错处理的校验器列表
     */
    @NacosValue(value = "${check.correctionCode.list}", autoRefreshed = true)
    private String needCheckCorrectionCodes;

    /**
     * 通知结果mq的topic
     */
    @NacosValue(value = "${correction.topic.result.notify:}", autoRefreshed = true)
    private String notifyTopic;

    /**
     * 延迟处理topic
     */
    @NacosValue(value = "${correction.topic.delay.handler:}", autoRefreshed = true)
    private String delayHandlerTopic;

    /**
     * 事件推送topic
     */
    @NacosValue(value = "${correction.topic.event.push:}", autoRefreshed = true)
    private String delayEventPush;

    /**
     * 默认值为level 4->30s
     * 1s、 5s、 10s、 30s、 1m、 2m、 3m、 4m、 5m、 6m、 7m、 8m、 9m、 10m、 20m、 30m、 1h、 2h
     */
    @NacosValue(value = "${correction.topic.delay.level:4}", autoRefreshed = true)
    private Integer delayLevel;


    @NacosValue(value = "${correction.auto.handle.black.list:''}", autoRefreshed = true)
    private String merchantOrderNoBlackList;

    @NacosValue(value = "#{'${correction.auto.handle.black.merchant.list:NULL}'.split(',')}", autoRefreshed = true)
    private Set<String> merchantNoBlackList;

    public boolean isHitMerchantNoBlackList(String merchantNo) {
        if(CollectionUtils.isEmpty(merchantNoBlackList)){
            return false;
        }
        return merchantNoBlackList.contains(merchantNo);
    }

    @NacosValue(value = "${processor.validation.switch}", autoRefreshed = true)
    private Boolean validationSwitch;

    @NacosValue(value = "${open.size.validation.switch:true}", autoRefreshed = true)
    private Boolean openSizeValidationSwitch;

    /**
     * 是否打开listSize校验逻辑
     * @return
     */
    public boolean isOpenSizeValidation(){
        return BooleanUtils.isTrue(openSizeValidationSwitch);
    }

    @NacosValue(value = "${ding.talk.alert.openapi.url}", autoRefreshed = true)
    private String dingTalkAlertUrl;

    @NacosValue(value = "${ding.talk.alert.token.transfer}", autoRefreshed = true)
    private String transferAlertToken;

    /**
     * dingTalk interval time (second)
     */
    @NacosValue(value = "${ding.talk.alert.interval.time:30}", autoRefreshed = true)
    private Long dingTalkAlertIntervalTime;

    @NacosValue(value = "${scheduled.pending.advance.switch:true}", autoRefreshed = true)
    private Boolean scheduledPendingSwitch;

    @NacosValue(value = "${gray.switch.flag:false}", autoRefreshed = true)
    private Boolean switchFlag;

    @NacosValue(value = "${scheduled.pending.advance.node:2}", autoRefreshed = true)
    private Integer scheduledPendingNodes;

    @NacosValue(value = "${scheduled.pending.advance.start.day:-10}", autoRefreshed = true)
    private Integer scheduledPendingStartDay;

    @NacosValue(value = "${scheduled.pending.advance.end.day:-1}", autoRefreshed = true)
    private Integer scheduledPendingEndDay;

    @NacosValue(value = "${ding.talk.alert.data.center.error:15001932315}", autoRefreshed = true)
    private String dataCenterErrorPhones;

    @NacosValue(value = "${ding.talk.alert.token.dataCenter:}", autoRefreshed = true)
    private String dataCenterAlertToken;


    @NacosValue(value = "#{'${redeliveryTradeFail.black.list:P01020115442490}'.split(',')}", autoRefreshed = true)
    private Set<String> redeliveryTradeFailBlackMerchantList;
    @NacosValue(value = "${strategy.code.list:redeliveryTradeFail,innerDuplicationPayment}", autoRefreshed = true)
    private String strategyCodeList;

    @NacosValue(value = "#{'${whiteList.haveDiscount.orderNo.list:xxxx}'.split(',')}", autoRefreshed = true)
    private Set<String> withListHaveDiscountOrderNoList; // 营销可处理订单的订单号白名单， 在客诉时可使用
}
