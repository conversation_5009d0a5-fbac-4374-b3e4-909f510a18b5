package com.payermax.operating.correction.integration.queue.dto;

import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 差错延时事件处理
 * @date 2022/10/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CorrectionDelayHandlerMessage extends BaseMqMessage {

    /**
     * 差错单
     */
    private String correctionNo;

    private Integer retry;
}
