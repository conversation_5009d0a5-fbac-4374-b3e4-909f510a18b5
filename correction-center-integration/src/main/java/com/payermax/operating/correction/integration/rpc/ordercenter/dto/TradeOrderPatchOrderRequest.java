package com.payermax.operating.correction.integration.rpc.ordercenter.dto;

import com.payermax.operating.correction.integration.rpc.ordercenter.enums.PatchType;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 订单中心修补订单对象
 * @date 2022/10/10
 */
@Data
@NoArgsConstructor
public class TradeOrderPatchOrderRequest {

    private OrderInfoDTO originalOrderInfo;

    private OrderInfoDTO patchOrderInfo;

    private PatchType patchType;

    private String correctionSuffix;

}
