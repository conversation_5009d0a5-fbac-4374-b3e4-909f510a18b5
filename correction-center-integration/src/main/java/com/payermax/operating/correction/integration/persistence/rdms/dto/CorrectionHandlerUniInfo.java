package com.payermax.operating.correction.integration.persistence.rdms.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @desc 差错处理唯一信息
 * @date 2022/10/10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CorrectionHandlerUniInfo {
    /**
     * 差错单
     */
    private String correctionNo;

    /**
     * 策略code
     */
    private String strategyCode;

    /**
     * 处理类型
     */
    private String handlerType;

    /**
     * 处理请求
     */
    private String processRequest = StringUtils.EMPTY;

    public CorrectionHandlerUniInfo(String correctionNo, String strategyCode, String handlerType) {
        this.correctionNo = correctionNo;
        this.strategyCode = strategyCode;
        this.handlerType = handlerType;
    }
}
