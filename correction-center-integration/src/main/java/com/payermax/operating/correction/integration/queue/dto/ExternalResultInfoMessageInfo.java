package com.payermax.operating.correction.integration.queue.dto;

import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 外部结果通知
 * @date 2023/7/11
 */
@Data
@NoArgsConstructor
public class ExternalResultInfoMessageInfo extends BaseMqMessage {

    private static final long serialVersionUID = 2034542432164623L;

    private ExternalResultMessage messageBody;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExternalResultMessage{
        /**
         * 事件信息
         */
        private CorrectionEventMessageInfo.EventInfo eventInfo;

        /**
         * 备注
         */
        private String memo;

        /**
         *  重试次数
         */
        private Integer retryNum;
    }

}
