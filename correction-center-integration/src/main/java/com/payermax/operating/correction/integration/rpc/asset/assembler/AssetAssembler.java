package com.payermax.operating.correction.integration.rpc.asset.assembler;

import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.dto.CorrectionPayoutsInfo;
import com.payermax.operating.correction.integration.rpc.asset.dto.AssetNotifyInfo;
import com.payermax.operating.correction.integration.utils.IntegrationUtils;
import com.payermax.payment.assetsx.correction.request.CorrectionPayoutsReq;
import com.payermax.payment.assetsx.correction.request.TradeOperationReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Optional;

/**
 * The interface Order center assembler.
 *
 * <AUTHOR>
 * @desc 出款服务转换类
 * @date 2022 /10/10
 */
@Mapper(componentModel = "spring", imports = {Optional.class, Nullable.class, IntegrationUtils.class, CorrectionConstant.class})
public interface AssetAssembler {

    /**
     * To correction payouts correction payouts req.
     *
     * @param payoutsInfo the payouts info
     * @param channelPayMethod the payouts info
     * @return the correction payouts req
     */
    @Mappings({
            @Mapping(target = "paymentOrderNo", source = "payoutsInfo.payoutNo"),
            @Mapping(target = "merchantNo", source = "payoutsInfo.merchantNo"),
            @Mapping(target = "productCode", expression = "java(CorrectionConstant.PAYOUTS_PRODUCT_CODE)"),
            @Mapping(target = "bizIdentify", source = "payoutsInfo.bizIdentify"),
            @Mapping(target = "payee", expression = "java(IntegrationUtils.getPayee(payoutsInfo,channelPayMethod))"),
            @Mapping(target = "bizApplyTime", expression = "java(System.currentTimeMillis())"),
    })
    CorrectionPayoutsReq toCorrectionPayouts(CorrectionPayoutsInfo payoutsInfo, String channelPayMethod);

    /**
     * 修复重复支付场景
     * @param assetNotifyInfo
     * @return
     */
    @Mapping(target = "bizApplyTime", expression = "java(System.currentTimeMillis())")
    @Mapping(target = "targetTradeOrderNo", source = "assetNotifyInfo.correctTradeOrderNo")
    TradeOperationReq toTradeOperationReq(AssetNotifyInfo assetNotifyInfo);


}

