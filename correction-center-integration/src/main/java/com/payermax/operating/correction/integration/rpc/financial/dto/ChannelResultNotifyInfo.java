package com.payermax.operating.correction.integration.rpc.financial.dto;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.integration.rpc.financial.enums.ChannelNotifyType;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc 渠道结果通知对象
 * @date 2022/12/14
 */
@Builder
@Getter
public class ChannelResultNotifyInfo {

    private String channelCommitNo;

    private ChannelNotifyType notifyType;

    private CommonStatusEnum processStatus;

    private String thirdOrderNo;

    private String fourthOrderNo;

    private Money payAmount;

    private String handleType;

    private String respCode;

    private String respMsg;
}
