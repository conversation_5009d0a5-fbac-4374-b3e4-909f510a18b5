package com.payermax.operating.correction.integration.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * @desc 支付请求单状态机
 * <AUTHOR>
 * @date 2022/11/15
 */
public enum PayOrderStatusEnum {
    PENDING("0","处理中"),
    PAY_SUCCESS("1","成功"),
    PAY_FAIL("2","失败");

    PayOrderStatusEnum(String val, String name) {
        this.val = val;
        this.name=name;
    }

    @Getter
    private String val;

    @Getter
    private String name;

    public static PayOrderStatusEnum getByVal(String val) {
        return Arrays.stream(values()).filter(e -> e.getVal().equals(val)).findAny().orElse(PayOrderStatusEnum.PENDING);
    }

}
