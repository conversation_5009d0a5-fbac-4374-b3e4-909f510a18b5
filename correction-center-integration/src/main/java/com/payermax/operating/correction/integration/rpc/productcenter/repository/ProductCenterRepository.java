package com.payermax.operating.correction.integration.rpc.productcenter.repository;

import com.payermax.operating.correction.integration.rpc.cashiercore.dto.CashierCorePaymentInstanceInfo;
import com.payermax.operating.correction.integration.rpc.productcenter.dto.CashierProductInfo;

import java.util.List;

/**
 * The interface Order center repository.
 *
 * <AUTHOR>
 * @desc 产品中心资源类
 * @date 2022 /10/10
 */
public interface ProductCenterRepository {

    /**
     * Gets cashier product.
     *
     * @param cashierCorePaymentInstanceInfoRequest the payment instance request
     * @return the cashier product
     */
    List<CashierProductInfo> getCashierProduct(CashierCorePaymentInstanceInfo cashierCorePaymentInstanceInfoRequest);

    /**
     * 从老产品中心迁移到解决方案的接口
     * @param cashierCorePaymentInstanceInfoRequest
     * @return
     */
    List<CashierProductInfo> getCashierProductV2(CashierCorePaymentInstanceInfo cashierCorePaymentInstanceInfoRequest);



}
