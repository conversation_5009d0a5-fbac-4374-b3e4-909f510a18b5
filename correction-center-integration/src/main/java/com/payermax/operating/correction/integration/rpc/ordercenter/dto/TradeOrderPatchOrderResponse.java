package com.payermax.operating.correction.integration.rpc.ordercenter.dto;

import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 交易订单号统一响应结果
 * @date 2022/10/10
 */
@Data
@Builder
public class TradeOrderPatchOrderResponse {
    /**
     * 凭证号
     */
    private String voucherNo;

    /**
     * 凭证类型
     */
    private DCVoucherType voucherType;

    /**
     * 返回code码
     */
    private String code;

    /**
     * 返回消息体
     */
    private String msg;

    public void defence() {
        AssertUtil.isTrue(Symbols.RES_SUCCESS.equals(this.code), this.code, ReturnMsg.RPC_ORDER_CENTER_EXCEPTION);
        AssertUtil.isTrue(StringUtils.isNotBlank(this.voucherNo) && Objects.nonNull(this.voucherType), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_ORDER_CENTER_EXCEPTION);
    }
}
