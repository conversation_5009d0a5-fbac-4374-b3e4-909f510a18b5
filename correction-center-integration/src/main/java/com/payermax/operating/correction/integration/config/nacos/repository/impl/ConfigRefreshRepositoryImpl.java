package com.payermax.operating.correction.integration.config.nacos.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.nacos.api.annotation.NacosInjected;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.api.config.annotation.NacosConfigListener;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.core.common.utils.ObjectsUtils;
import com.payermax.operating.correction.integration.config.nacos.model.*;
import com.payermax.operating.correction.integration.config.nacos.repository.ConfigRepository;
import com.payermax.operating.correction.integration.config.persistence.repository.RdmsConfigRepository;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 基于nacos刷新动态配置实现类
 * @date 2022/9/20
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ConfigRefreshRepositoryImpl implements ConfigRepository {

    @SuppressWarnings("unused")
    @NacosInjected
    private ConfigService configService;

    private final static String SHARDING_KEY = "1002";

    private final ConcurrentHashMap<String, StrategyProcessorInfo> strategyProcessMap = new ConcurrentHashMap();

    private final ConcurrentHashMap<String, CorrectionSystemInfo> correctionSysMap = new ConcurrentHashMap();

    private final ConcurrentHashMap<String, CorrectionBasicInfoDTO> parentReasonBasicMap = new ConcurrentHashMap();

    private final ConcurrentHashMap<String, List<MatcherRule>> matcherRuleMap = new ConcurrentHashMap();

    private final ConcurrentHashMap<String, StrategyRule> autoStrategyRuleMap = new ConcurrentHashMap();

    private final ConcurrentHashMap<String, String> payMethodMapping = new ConcurrentHashMap();

    private final ConcurrentHashMap<String, DingTalkNotifyMappingInfo> dingTalkMapping = new ConcurrentHashMap();
    private final ConcurrentHashMap<String, List<Expression>> strategyBlacklist = new ConcurrentHashMap();

    private final List<CorrectionPayoutsCountryInfo> payoutsCountryList = Lists.newArrayList();

    private final List<CorrectionBasicInfoDTO> parentReasonBasicList = Lists.newArrayList();

    private final RdmsConfigRepository configRepository;

    private final List<String> registerHandlerList = Lists.newArrayList();

    @NacosValue(value = "${config.register.handler.reason:}", autoRefreshed = true)
    public void setRegisterHandlerList(String registerStr) {
        List<String> registerList = ObjectsUtils.strToList(registerStr, Symbols.HASHTAG);
        registerHandlerList.clear();
        registerHandlerList.addAll(registerList);
    }

    @PostConstruct
    @SneakyThrows
    public void init() {
        int timeOut = 3000;
        try {

            //init strategyConfig
            String strategyProcessListener = configService.getConfig(CorrectionConstant.STRATEGY_PROCESS_JSON, CorrectionConstant.CORRECTION_CENTER, timeOut);
            this.strategyProcessListener(strategyProcessListener);

            //init correctionSystemConfig
            String correctionSystemConfig = configService.getConfig(CorrectionConstant.CORRECTION_SYSTEM_JSON, CorrectionConstant.CORRECTION_CENTER, timeOut);
            this.correctionSysListener(correctionSystemConfig);

            //init parentReasonBasicRefresh
            this.parentReasonBasicRefresh();

            //init matcherRule
            String matcherRuleStr = configService.getConfig(CorrectionConstant.MATCHER_RULE_JSON, CorrectionConstant.CORRECTION_CENTER, timeOut);
            this.matchRuleListener(matcherRuleStr);

            //init autoStrategy
            String autoStrategyRuleStr = configService.getConfig(CorrectionConstant.AUTO_STRATEGY_RULE_JSON, CorrectionConstant.CORRECTION_CENTER, timeOut);
            this.autoStrategyRuleListener(autoStrategyRuleStr);

            //init payOutsCountry
            String payoutsCountryStr = configService.getConfig(CorrectionConstant.PAYOUTS_COUNTRY_JSON, CorrectionConstant.CORRECTION_CENTER, timeOut);
            this.payoutsCountryListener(payoutsCountryStr);

            //init payMethodMapping
            String payMethodMappingStr = configService.getConfig(CorrectionConstant.PAY_METHOD_MAPPING_JSON, CorrectionConstant.CORRECTION_CENTER, timeOut);
            this.payMethodMappingListener(StringUtils.defaultIfBlank(payMethodMappingStr, Symbols.PARANTHESES_STR));

            //init dingTalkMapping
            String dingTalkMappingStr = configService.getConfig(CorrectionConstant.DING_TALK_MAPPING_JSON, CorrectionConstant.CORRECTION_CENTER, timeOut);
            this.dingTalkMappingListener(StringUtils.defaultIfBlank(dingTalkMappingStr, Symbols.PARANTHESES_STR));

            //init strategyBlacklist
            String strategyBlacklistStr = configService.getConfig(CorrectionConstant.STRATEGY_BLACKLIST_JSON, CorrectionConstant.CORRECTION_CENTER, timeOut);
            this.strategyBlacklistListener(StringUtils.defaultIfBlank(strategyBlacklistStr, Symbols.ARRAY_STR));

        } catch (NacosException e) {
            log.error("init config exception:", e);
            throw e;
        }
    }

    @NacosConfigListener(groupId = CorrectionConstant.CORRECTION_CENTER, dataId = CorrectionConstant.DING_TALK_MAPPING_JSON, type = ConfigType.JSON)
    public void dingTalkMappingListener(String dingTalkMappingStr) {
        try {
            log.info("ConfigRefreshRepositoryImpl dingTalkMappingListener [dingTalkMappingStr] [{}]:", dingTalkMappingStr);
            List<DingTalkNotifyMappingInfo> dingTalkNotifyMappingList = JSONObject.parseObject(dingTalkMappingStr, new TypeReference<List<DingTalkNotifyMappingInfo>>() {
            });
            Map<String, DingTalkNotifyMappingInfo> newMaps = dingTalkNotifyMappingList.stream().collect(Collectors.toMap(DingTalkNotifyMappingInfo::getShareId, Function.identity(), (key1, key2) -> key1));
            dingTalkMapping.clear();
            dingTalkMapping.putAll(newMaps);
        } catch (Exception e) {
            log.error("ConfigRefreshRepositoryImpl dingTalkMappingListener base system exception:", e);
        }
    }
    @NacosConfigListener(groupId = CorrectionConstant.CORRECTION_CENTER, dataId = CorrectionConstant.STRATEGY_BLACKLIST_JSON, type = ConfigType.JSON)
    public void strategyBlacklistListener(String strategyBlacklistStr) {
        try {
            log.info("ConfigRefreshRepositoryImpl strategyBlacklistListener [strategyBlacklistStr] [{}]:", strategyBlacklistStr);
            List<StrategyBlackInfo> strategyBlacklistInfo = JSONObject.parseObject(strategyBlacklistStr, new TypeReference<List<StrategyBlackInfo>>() {
            });
            Map<String, List<Expression>> newMaps = strategyBlacklistInfo.stream()
                    .collect(Collectors.toMap(StrategyBlackInfo::getStrategyCode, StrategyBlackInfo::getExps));
            strategyBlacklist.clear();
            strategyBlacklist.putAll(newMaps);
        } catch (Exception e) {
            log.error("ConfigRefreshRepositoryImpl strategyBlacklistListener base system exception:", e);
        }
    }

    @NacosConfigListener(groupId = CorrectionConstant.CORRECTION_CENTER, dataId = CorrectionConstant.PAY_METHOD_MAPPING_JSON, type = ConfigType.JSON)
    public void payMethodMappingListener(String payMethodMappingStr) {
        try {
            log.info("payMethodMappingListener,payMethodMappingStr : [{}]", payMethodMappingStr);
            Map<String, String> newMap = JSONObject.parseObject(payMethodMappingStr, new TypeReference<Map<String, String>>() {
            });
            payMethodMapping.clear();
            payMethodMapping.putAll(newMap);
        } catch (Exception e) {
            log.error("payMethodMappingListener nacos config parse fail:", e);
        }
    }

    @NacosConfigListener(groupId = CorrectionConstant.CORRECTION_CENTER, dataId = CorrectionConstant.PAYOUTS_COUNTRY_JSON, type = ConfigType.JSON)
    public void payoutsCountryListener(String payoutsCountryStr) {
        try {
            log.info("payoutsCountryListener,payoutsCountryJson : [{}]", payoutsCountryStr);
            List<CorrectionPayoutsCountryInfo> countryInfoList = JSONObject.parseObject(payoutsCountryStr, new TypeReference<List<CorrectionPayoutsCountryInfo>>() {
            });
            payoutsCountryList.clear();
            payoutsCountryList.addAll(countryInfoList);
        } catch (Exception e) {
            log.error("payoutsCountryListener nacos config parse fail:", e);
        }
    }

    @NacosConfigListener(groupId = CorrectionConstant.CORRECTION_CENTER, dataId = CorrectionConstant.MATCHER_RULE_JSON, type = ConfigType.JSON)
    public void matchRuleListener(String matcherRuleJson) {
        try {
            log.info("matchRuleListener,matcherRuleJson : [{}]", matcherRuleJson);
            List<MatcherRuleInfo> matcherRuleList = JSONObject.parseObject(matcherRuleJson, new TypeReference<List<MatcherRuleInfo>>() {
            });
            Map<String, List<MatcherRule>> newMap = matcherRuleList.stream().collect(Collectors.toMap(e -> this.getMatchUniqueKey(e.getCorrectionCode(), e.getTradeType()), MatcherRuleInfo::getRules, (key1, key2) -> key1));
            matcherRuleMap.clear();
            matcherRuleMap.putAll(newMap);
        } catch (Exception e) {
            log.error("matchRuleListener nacos config parse fail:", e);
        }
    }

    @NacosConfigListener(groupId = CorrectionConstant.CORRECTION_CENTER, dataId = CorrectionConstant.AUTO_STRATEGY_RULE_JSON, type = ConfigType.JSON)
    public void autoStrategyRuleListener(String autoStrategyRuleStr) {
        try {
            log.info("autoStrategyRuleListener,autoStrategyRuleStr : [{}]", autoStrategyRuleStr);
            List<AutomaticStrategyInfo> automaticStrategyInfos = JSONObject.parseObject(autoStrategyRuleStr, new TypeReference<List<AutomaticStrategyInfo>>() {
            });
            Map<String, StrategyRule> newMap = automaticStrategyInfos.stream().collect(Collectors.toMap(e -> this.getMatchUniqueKey(e.getCorrectionCode(), e.getTradeType()), AutomaticStrategyInfo::getRule, (key1, key2) -> key1));
            autoStrategyRuleMap.clear();
            autoStrategyRuleMap.putAll(newMap);
        } catch (Exception e) {
            log.error("autoStrategyRuleListener nacos config parse fail:", e);
        }
    }

    @NacosConfigListener(groupId = CorrectionConstant.CORRECTION_CENTER, dataId = CorrectionConstant.STRATEGY_PROCESS_JSON, type = ConfigType.JSON)
    public void strategyProcessListener(String strategyProcessJson) {
        try {
            log.info("strategyProcessListener,strategyProcessJson : [{}]", strategyProcessJson);
            ConcurrentHashMap<String, StrategyProcessorInfo> newMap = JSONObject.parseObject(strategyProcessJson, new TypeReference<ConcurrentHashMap<String, StrategyProcessorInfo>>() {
            });
            strategyProcessMap.clear();
            strategyProcessMap.putAll(newMap);
        } catch (Exception e) {
            log.error("strategyProcessListener nacos config parse fail:", e);
        }
    }

    @NacosConfigListener(groupId = CorrectionConstant.CORRECTION_CENTER, dataId = CorrectionConstant.CORRECTION_SYSTEM_JSON, type = ConfigType.JSON)
    public void correctionSysListener(String correctionSysJson) {
        try {
            log.info("correctionSysListener,correctionSysJson : [{}]", correctionSysJson);
            ConcurrentHashMap<String, CorrectionSystemInfo> newMap = JSONObject.parseObject(correctionSysJson, new TypeReference<ConcurrentHashMap<String, CorrectionSystemInfo>>() {
            });
            correctionSysMap.clear();
            correctionSysMap.putAll(newMap);
        } catch (Exception e) {
            log.error("correctionSysListener nacos config parse fail:", e);
        }
    }

    public void parentReasonBasicRefresh() {
        List<CorrectionBasicInfoDTO> basicInfo = configRepository.loadAllParentBasicInfo();
        Map<String, CorrectionBasicInfoDTO> newMap = basicInfo.stream().collect(Collectors.toMap(CorrectionBasicInfoDTO::getCorrectionCode, Function.identity(), (key1, key2) -> key2));
        parentReasonBasicList.clear();
        parentReasonBasicMap.clear();
        parentReasonBasicList.addAll(basicInfo);
        parentReasonBasicMap.putAll(newMap);
    }


    @Override
    public StrategyProcessorInfo getStrategyProcessor(String strategyCode) {
        if (StringUtils.isBlank(strategyCode)) {
            return Nullable.getNullVal();
        }
        return strategyProcessMap.get(strategyCode);
    }

    @Override
    public CorrectionSystemInfo getCorrectionSys(String correctionCode, String sysSource) {
        if (StringUtils.isAnyBlank(correctionCode, sysSource)) {
            return Nullable.getNullVal();
        }
        return correctionSysMap.get(Joiner.on(Symbols.COLON).join(correctionCode, sysSource));
    }

    @Override
    public Map<String, CorrectionBasicInfoDTO> getParentReasonBasicMap() {
        return parentReasonBasicMap;
    }

    @Override
    public List<CorrectionBasicInfoDTO> getParentReasonBasicInfoByMemory() {
        return parentReasonBasicList;
    }

    @Override
    public List<MatcherRule> getRuleInfo(String correctionCode, TradeType tradeType) {
        return matcherRuleMap.get(this.getMatchUniqueKey(correctionCode, tradeType));
    }

    @Override
    public StrategyRule getStrategyInfo(String correctionCode, TradeType tradeType) {
        return autoStrategyRuleMap.get(this.getMatchUniqueKey(correctionCode, tradeType));
    }

    @Override
    public List<CorrectionPayoutsCountryInfo> getPayOutsCountry() {
        return payoutsCountryList;
    }

    @Override
    public ConcurrentHashMap<String, List<Expression>> getStrategyBlacklist() {
        return strategyBlacklist;
    }

    @Override
    public String getPayMethod(String payMethod) {
        return StringUtils.defaultIfBlank(payMethodMapping.get(payMethod), payMethod);
    }

    @Override
    public String getMatchUniqueKey(String correctionCode, TradeType tradeType) {
        return correctionCode + Symbols.LINE + Optional.ofNullable(tradeType).map(TradeType::name).orElse("");
    }

    @Override
    public List<String> getAllRegisterHandlerList() {
        return registerHandlerList;
    }

    @Override
    public void refreshAllParentBasicInfo() {
        parentReasonBasicRefresh();
    }

    @Override
    public DingTalkNotifyMappingInfo getNotifyInfo(String shareId) {
        return dingTalkMapping.get(shareId);
    }
}
