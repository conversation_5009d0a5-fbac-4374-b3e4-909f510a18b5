package com.payermax.operating.correction.integration.rpc.cashiercore.repository;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.integration.rpc.cashiercore.dto.CashierCorePaymentInstanceInfo;

/**
 * The interface Cashier core repository.
 *
 * <AUTHOR>
 * @desc 收银核心资源接口
 * @date 2022 /12/13
 */
public interface CashierCoreRepository {

    /**
     * Gets payment instance.
     *
     * @param cashierCorePaymentInstanceInfoDTO the cashier core payment instance info dto
     * @param payAmount                         the pay amount
     * @param bizIdentify                       the biz identify
     * @param merchantNo                        the merchant no
     */
    void getPaymentInstance(CashierCorePaymentInstanceInfo cashierCorePaymentInstanceInfoDTO, Money payAmount, String bizIdentify, String merchantNo);

}
