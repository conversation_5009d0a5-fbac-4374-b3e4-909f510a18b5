package com.payermax.operating.correction.integration.rpc.voucher.repository.assembler;

import com.payermax.basic.voucher.common.enums.PaymentScene;
import com.payermax.basic.voucher.common.enums.TradeScene;
import com.payermax.basic.voucher.common.enums.TradeStep;
import com.payermax.basic.voucher.common.enums.VoucherSource;
import com.payermax.basic.voucher.facade.request.CreatePaymentVoucherRequest;
import com.payermax.basic.voucher.facade.request.CreateTradeVoucherRequest;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.rpc.voucher.repository.dto.VoucherRequestDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Optional;

/**
 * The interface Order center assembler.
 *
 * <AUTHOR>
 * @desc 出款服务转换类
 * @date 2022 /10/10
 */
@Mapper(componentModel = "spring", imports = {Optional.class, Nullable.class, CorrectionConstant.class,
        TradeStep.class, PaymentScene.class, TradeScene.class, VoucherSource.class})
public interface VoucherAssembler {

    /**
     * To create payment voucher request.
     *
     * @param voucherInfo the voucher info
     * @return the create payment voucher request
     */
    @Mappings({
            @Mapping(target = "requestNo", source = "requestNo"),
            @Mapping(target = "shardingKey", expression = "java(CorrectionConstant.SHARDING_KEY)"),
            @Mapping(target = "bizIdentity", source = "bizIdentity"),
            @Mapping(target = "productCode", source = "productCode"),
            @Mapping(target = "tradeStep", expression = "java(TradeStep.PAY.getCode())"),
            @Mapping(target = "paymentScene", expression = "java(PaymentScene.PAYMENT.getCode())"),
    })
    CreatePaymentVoucherRequest toPaymentVoucherRequest(VoucherRequestDTO voucherInfo);

    /**
     * To trade voucher request create trade voucher request.
     *
     * @param voucherInfo the voucher info
     * @return the create trade voucher request
     */
    @Mappings({
            @Mapping(target = "outTrade.merchantId", source = "merchantNo"),
            @Mapping(target = "outTrade.outTradeNo", source = "merchantOrderNo"),
            @Mapping(target = "bizIdentity", source = "bizIdentity"),
            @Mapping(target = "productCode", source = "productCode"),
            @Mapping(target = "shardingKey", expression = "java(CorrectionConstant.SHARDING_KEY)"),
            @Mapping(target = "tradeScene", expression = "java(TradeScene.RECEIPT.getCode())"),
            @Mapping(target = "source", expression = "java(VoucherSource.INNER.getCode())"),
    })
    CreateTradeVoucherRequest toTradeVoucherRequest(VoucherRequestDTO voucherInfo);
}

