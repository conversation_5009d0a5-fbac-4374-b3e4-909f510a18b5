package com.payermax.operating.correction.integration.rpc.risk.repository.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.integration.rpc.risk.dto.RiskEventInfo;
import com.payermax.operating.correction.integration.rpc.risk.repository.RiskRepository;
import com.payermax.risk.engine.api.dto.EventRequest;
import com.payermax.risk.engine.api.dto.EventResult;
import com.payermax.risk.engine.api.enums.EventResultEnum;
import com.payermax.risk.engine.client.RiskEngineSyncClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 风控资源实现类
 * @date 2022/12/19
 */
@Repository
@Slf4j
public class RiskRepositoryImpl implements RiskRepository {

    @Resource
    private RiskEngineSyncClient riskEngineSyncClient;

    @Override
    public Boolean riskEventReport(RiskEventInfo riskEventInfo) {
        EventResult eventResult = null;
        try {
            eventResult = riskEngineSyncClient.executeEvent(this.buildEvent(riskEventInfo), 3000, 0, EventResult.dummyPass());
        } catch (Exception e) {
            log.warn("风控调用失败，则算风控通过:", e);
            return Boolean.TRUE;
        }
        return EventResultEnum.PASS.getCode().equals(eventResult.getRiskResult());
    }

    private EventRequest buildEvent(RiskEventInfo riskEventInfo) {
        EventRequest event = new EventRequest();
        event.setEventBody(riskEventInfo.getEventBody().getInnerMap());
        event.setEventId(riskEventInfo.getRiskType().getEventId());
        event.setCheckPoint(riskEventInfo.getRiskType().getPoint());
        event.setBizAppId(CorrectionConstant.CORRECTION_CENTER);
        event.setBizType(CorrectionConstant.RISK_BIZ_TYPE);
        event.setRequestId(riskEventInfo.getRequestId());
        event.setRequestTime(DateUtil.current());
        log.info("buildEvent,riskEventInfo : [{}]", JSONObject.toJSONString(event));
        return event;
    }
}
