package com.payermax.operating.correction.integration.rpc.financial.repository;

import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.integration.rpc.financial.dto.ChannelCommitResp;
import com.payermax.operating.correction.integration.rpc.financial.dto.ChannelResultNotifyInfo;
import com.payermax.operating.correction.integration.rpc.financial.dto.ChannelCommitReq;

/**
 * The interface Channel exchange repository.
 *
 * <AUTHOR>
 * @desc 金融交换接口
 * @date 2022 /12/13
 */
public interface ChannelExchangeRepository {

    /**
     * Financial result notify base res process.
     *
     * @param resultNotifyInfo the result notify info
     * @return the base res process
     */
    BaseResProcess financialResultNotify(ChannelResultNotifyInfo resultNotifyInfo);

    BaseResProcess bounceBack(ChannelResultNotifyInfo resultNotifyInfo);

    /**
     * 根据渠道提交单获取渠道提交单信息
     *
     * @param channelCommitReq
     * @return 渠道提交单状态
     */
    ChannelCommitResp queryChannelByChannelPayCommitNo(ChannelCommitReq channelCommitReq);
}
