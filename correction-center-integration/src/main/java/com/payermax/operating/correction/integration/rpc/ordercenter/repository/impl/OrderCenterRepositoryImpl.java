package com.payermax.operating.correction.integration.rpc.ordercenter.repository.impl;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.correction.integration.rpc.ordercenter.assembler.OrderCenterAssembler;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderRequest;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderResponse;
import com.payermax.operating.correction.integration.rpc.ordercenter.repository.OrderCenterRepository;
import com.payermax.order.dto.response.*;
import com.payermax.order.service.CorrectionFacade;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 订单中心资源库
 * @date 2022/10/10
 */
@Repository
public class OrderCenterRepositoryImpl implements OrderCenterRepository {

    @Resource
    private OrderCenterAssembler orderCenterAssembler;

    @DubboReference(timeout = 5000, version = "1.0")
    private CorrectionFacade orderCenterCorrectionFacade;

    @Override
    public TradeOrderPatchOrderResponse patchPayOrder(TradeOrderPatchOrderRequest patchOrderRequest) {
        Result<PatchPaymentResponse> result = orderCenterCorrectionFacade.patchPayOrder(orderCenterAssembler.toPatchPayOrder(patchOrderRequest));
        return orderCenterAssembler.toPatchOrderResponse(result, result.getData());
    }

    @Override
    public TradeOrderPatchOrderResponse patchTradeOrder(TradeOrderPatchOrderRequest patchOrderRequest) {
        Result<PatchTradeOrderResponse> result = orderCenterCorrectionFacade.patchTradeOrder(orderCenterAssembler.toPatchTradeOrder(patchOrderRequest));
        return orderCenterAssembler.toPatchOrderResponse(result, result.getData());
    }

    @Override
    public TradeOrderPatchOrderResponse patchTradePayOrder(TradeOrderPatchOrderRequest patchOrderRequest) {
        Result<PatchTradeOrderAndPaymentResponse> result = orderCenterCorrectionFacade.patchTradePayOrder(orderCenterAssembler.toPatchTradePayOrder(patchOrderRequest));
        return orderCenterAssembler.toPatchOrderResponse(result, result.getData());
    }

    @Override
    public TradeOrderPatchOrderResponse patchExternalDuplicateTradeOrder(TradeOrderPatchOrderRequest patchOrderRequest){
        Result<PatchRepeatTradeOrderAndPayResponse> result = orderCenterCorrectionFacade.patchRepeatTradePay(orderCenterAssembler.toRepeatTrade(patchOrderRequest));
        return orderCenterAssembler.toPatchOrderResponse(result, result.getData());
    }

    @Override
    public TradeOrderPatchOrderResponse patchInnerDuplicateTradeOrder(TradeOrderPatchOrderRequest patchOrderRequest){
        Result<PatchRepeatTradeOrderAndPayResponse> result = orderCenterCorrectionFacade.patchRepeatTradePay(orderCenterAssembler.toRepeatTrade(patchOrderRequest));
        return orderCenterAssembler.toPatchTradeOrderResponse(result, result.getData());
    }

    @Override
    public TradeOrderPatchOrderResponse patchRefundFailedDuplicateTradeOrder(TradeOrderPatchOrderRequest patchOrderRequest) {
        Result<PatchRepeatPayResponse> result = orderCenterCorrectionFacade.patchRepeatPay(orderCenterAssembler.toPatchRepeatPay(patchOrderRequest));
        return orderCenterAssembler.toPatchRepeatPayResponse(result, result.getData());
    }

}
