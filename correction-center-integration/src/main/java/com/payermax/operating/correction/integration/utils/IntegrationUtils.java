package com.payermax.operating.correction.integration.utils;

import com.payermax.common.lang.util.money.Money;
import com.payermax.funds.order.disbursement.dto.request.PayoutRequest;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.dto.CorrectionPayoutsInfo;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsRuleInfo;
import com.payermax.operating.correction.integration.rpc.productcenter.dto.CashierProductInfo;
import com.payermax.payment.assetsx.service.common.payment.detail.ExternalPayDetail;
import com.payermax.payment.assetsx.service.common.paytool.impl.ExternalPayTool;

import java.util.Objects;

/**
 * <AUTHOR>
 * @desc Assembler 工具类
 * @date 2022/12/9
 */
public class IntegrationUtils {

    public static PayoutRequest.PayeeInfo getPayoutsPayeeInfoNew(PayoutsRuleInfo ruleInfo, CashierProductInfo cashierProduct) {
        return com.payermax.funds.order.disbursement.dto.request.PayoutRequest.PayeeInfo.builder()
                .email(ruleInfo.getEmail())
                .paymentMethod(cashierProduct.getPaymentInstance().getPaymentMethodNo())
                .payeePhone(ruleInfo.getPayeePhone())
                .name(com.payermax.funds.order.disbursement.dto.request.PayoutRequest.Name.builder().fullName(ruleInfo.getPayeeName()).build())
                .accountInfo(com.payermax.funds.order.disbursement.dto.request.PayoutRequest.AccountInfo.builder()
                        .accountNo(ruleInfo.getPayeeAccount())
                        .accountType(ruleInfo.getPayeeAccountType())
                        .checkDigit(ruleInfo.getCheckDigit())
                        .build())
                .bankInfo(com.payermax.funds.order.disbursement.dto.request.PayoutRequest.BankInfo.builder()
                        .bankCode(ruleInfo.getPayeeBankCode())
                        .bankName(ruleInfo.getPayeeBankName())
                        .bankCity(ruleInfo.getPayeeBankCity())
                        .bankBranch(ruleInfo.getPayeeBankBranch())
                        .corAccountNo(ruleInfo.getPayeeCorAccount())
                        .build())
                .build();
    }

    public static PayoutRequest.Trade getPayoutsTrade(Money amount) {
        if (Objects.isNull(amount)) {
            return Nullable.getNullVal();
        }
        return PayoutRequest.Trade.builder()
                .amount(amount.getAmount().toString())
                .currency(amount.getCurrency().getCurrencyCode())
                .build();
    }

    public static PayoutRequest.Trade getPayoutsTradeNew(Money amount) {
        if (Objects.isNull(amount)) {
            return Nullable.getNullVal();
        }
        return com.payermax.funds.order.disbursement.dto.request.PayoutRequest.Trade.builder()
                .amount(amount.getAmount().toString())
                .currency(amount.getCurrency().getCurrencyCode())
                .build();
    }

    public static ExternalPayDetail getPayee(CorrectionPayoutsInfo payoutsInfo, String channelPayMethod) {
        ExternalPayDetail externalPayDetail = new ExternalPayDetail();
        ExternalPayTool externalPayTool = new ExternalPayTool();
        externalPayTool.setPaymentMethodType(channelPayMethod);
        externalPayTool.setTargetOrg(payoutsInfo.getPayPaymentInstanceInfo().getPaymentInstanceDTO().getTargetOrg());
        //构建收款方
        externalPayDetail.setAmount(IntegrationUtils.toMoney(payoutsInfo.getPayAmount()));
        externalPayDetail.setPayTool(externalPayTool);
        return externalPayDetail;
    }

    public static com.ushareit.fintech.common.model.dto.Money toMoney(Money money) {
        if (Objects.isNull(money)) {
            return Nullable.getNullVal();
        }
        return new com.ushareit.fintech.common.model.dto.Money(money.getAmount(), money.getCurrency().getCurrencyCode());
    }

}
