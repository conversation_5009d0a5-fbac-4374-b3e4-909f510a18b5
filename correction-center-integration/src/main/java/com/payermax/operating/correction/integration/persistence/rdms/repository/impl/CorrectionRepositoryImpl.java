package com.payermax.operating.correction.integration.persistence.rdms.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageHelper;
import com.payermax.common.lang.model.dto.request.PageRequest;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.*;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.OrderDetailType;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.enums.ValidType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.core.dal.QueryCorrectionOrderInfo;
import com.payermax.operating.correction.core.dal.VoucherUniqueInfo;
import com.payermax.operating.correction.core.dal.dao.*;
import com.payermax.operating.correction.core.dal.po.*;
import com.payermax.operating.correction.integration.persistence.rdms.assembler.CorrectionRdmsAssembler;
import com.payermax.operating.correction.integration.persistence.rdms.dto.*;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionOperationRepository;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 差错运营资源实现类
 * @date 2022/9/20
 */
@Repository
@Slf4j
public class CorrectionRepositoryImpl implements CorrectionOperationRepository, CorrectionRepository {

    @Resource
    private CorrectionOperationStrategyInfoMapper strategyInfoMapper;

    @Resource
    private CorrectionBasicInfoMapper basicInfoMapper;

    @Resource
    private CorrectionRdmsAssembler assembler;

    @Resource
    private CorrectionOrderInfoMapper orderInfoMapper;

    @Resource
    private CorrectionOrderHandlerRecordMapper handlerRecordMapper;

    @Resource
    private CorrectionOperationStrategyMappingMapper strategyMappingMapper;

    @Override
    public void storeOperationStrategyInfo(CorrectionOperationStrategyInfoDTO info) {
        strategyInfoMapper.insertDupKeyUpdate(assembler.operationStrategyModelToPo(info));
    }

    @Override
    public CorrectionOperationStrategyInfoDTO loadOperationStrategy(String strategyCode) {

        CorrectionOperationStrategyInfo po = strategyInfoMapper.selectByPrimaryKey(strategyCode);
        if (Objects.isNull(po)) {
            return Nullable.getNullVal();
        }
        //运营策略数据填充
        return assembler.operationStrategyPoToModel(po);
    }

    @Override
    public Page<CorrectionOperationStrategyInfoDTO> loadOperationStrategyInfo(PageRequest pageRequest, ValidType valid) {
        //分页查询
        Page<CorrectionOperationStrategyInfo> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<CorrectionOperationStrategyInfo> operationStrategyInfos = strategyInfoMapper.selectPageRequest(page, Optional.ofNullable(valid).map(ValidType::getVal).orElse((byte) 0));
        //transfer
        List<CorrectionOperationStrategyInfoDTO> listRet = operationStrategyInfos.stream().map(assembler::operationStrategyPoToModel).collect(Collectors.toList());
        Page<CorrectionOperationStrategyInfoDTO> pageRet = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        BeanUtils.copyProperties(page, pageRet);
        pageRet.setRecords(listRet);
        return pageRet;
    }

    @Override
    public void storeCorrectionBasicInfo(CorrectionBasicInfoDTO info) {
        basicInfoMapper.insertDupKeyUpdate(assembler.basicModelToPo(info));
    }

    @Override
    public CorrectionBasicInfoDTO loadBasicInfo(String correctionCode) {
        CorrectionBasicInfo po = basicInfoMapper.selectByPrimaryKey(correctionCode);
        if (Objects.isNull(po)) {
            return Nullable.getNullVal();
        }
        return assembler.basicPoToModel(po);
    }

    @Override
    public List<CorrectionBasicInfoDTO> loadAllChildrenBasicInfo() {
        List<CorrectionBasicInfo> list = basicInfoMapper.selectAllChildrenValid();
        return list.stream().map(assembler::basicPoToModel).collect(Collectors.toList());
    }

    @Override
    public List<CorrectionBasicInfoDTO> loadAllParentBasicInfo() {
        List<CorrectionBasicInfo> list = basicInfoMapper.selectAllParentValid();
        return list.stream().map(assembler::basicPoToModel).collect(Collectors.toList());
    }

    @Override
    public List<CorrectionOperationStrategyInfoDTO> loadOperationStrategyAll() {
        List<CorrectionOperationStrategyInfo> list = strategyInfoMapper.selectAllValid();
        return list.stream().map(assembler::operationStrategyPoToModel).collect(Collectors.toList());
    }

    @Override
    public Page<CorrectionBasicInfoDTO> loadReasonBasicInfo(PageRequest pageRequest, ValidType validType) {
        //分页查询
        Page<CorrectionBasicInfo> page = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<CorrectionBasicInfo> correctionBasicInfos = basicInfoMapper.selectPageRequest(page, Optional.ofNullable(validType).map(ValidType::getVal).orElse((byte) 0));
        //transfer
        List<CorrectionBasicInfoDTO> listRet = correctionBasicInfos.stream().map(assembler::basicPoToModel).collect(Collectors.toList());
        Page<CorrectionBasicInfoDTO> pageRet = new Page<>(pageRequest.getPageNum(), pageRequest.getPageSize());
        BeanUtils.copyProperties(page, pageRet);
        pageRet.setRecords(listRet);
        return pageRet;
    }

    @Override
    public List<CorrectionBasicInfoDTO> loadChildrenReasonBasicByParent(String parentCorrectionCode, TradeType tradeType) {
        List<CorrectionBasicInfo> list = basicInfoMapper.selectValidByParentCorrectionCode(parentCorrectionCode, Optional.ofNullable(tradeType).map(TradeType::name).orElse(StringUtils.EMPTY));
        return list.stream().map(assembler::basicPoToModel).collect(Collectors.toList());
    }

    @Override
    public void storeCorrectionOrderInfo(CorrectionOrderInfoDTO correctionOrderInfo) {
        CorrectionOrderInfo po = assembler.orderInfoModelToPo(correctionOrderInfo);
        orderInfoMapper.insertSelective(po);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void storeOrderHandlerRecordInfo(CorrectionOrderHandlerRecordDTO handlerRecord) {
        CorrectionOrderHandlerRecord orderHandlerRecord = assembler.handleRecordModelToPo(handlerRecord);
        handlerRecordMapper.insertSelective(orderHandlerRecord);
    }

    @Override
    public void storeOrderHandlerRecordResultInfo(CorrectionHandlerUniInfo handlerInfo, BaseResProcess res) {
        CorrectionOrderHandlerRecordExample recordUniExample = this.fillRecordExample(handlerInfo);
        CorrectionOrderHandlerRecord handlerRecord = new CorrectionOrderHandlerRecord();
        handlerRecord.setProcessResult(JSONObject.toJSONString(res));
        handlerRecord.setProcessStatus(Optional.ofNullable(res.getStatus()).map(CommonStatusEnum::name).orElse(Nullable.getNullVal()));
        handlerRecordMapper.updateByExampleSelective(handlerRecord, recordUniExample);
    }

    @Override
    public Boolean patchModifyHandlerRecordDiscard(CorrectionOrderHandlerRecordDTO orderHandlerRecordDTO) {
        CorrectionOrderHandlerRecord orderHandlerRecord = assembler.handleRecordModelToPo(orderHandlerRecordDTO);
        int ret = handlerRecordMapper.patchUpdateFailedHandlerRecord(orderHandlerRecord, CommonStatusEnum.SUCCESS.name(), CommonStatusEnum.DISCARD.name());
        return ret > 0;
    }

    @Override
    public List<CorrectionOrderHandlerRecordDTO> loadCorrectionRecordList(CorrectionHandlerUniInfo handlerUniInfo, OrderDetailType queryType) {

        if (OrderDetailType.All_QUERY == queryType) {
            handlerUniInfo.setStrategyCode(Nullable.getNullVal());
            handlerUniInfo.setHandlerType(Nullable.getNullVal());
        }
        CorrectionOrderHandlerRecordExample recordUniExample = this.fillRecordExample(handlerUniInfo);
        recordUniExample.setOrderByClause("utc_create DESC");
        //查询db
        List<CorrectionOrderHandlerRecord> correctionOrderHandlerRecords = handlerRecordMapper.selectByExampleWithBLOBs(recordUniExample);
        return correctionOrderHandlerRecords.stream().map(assembler::handlerRecordPoToModel).collect(Collectors.toList());
    }

    @Override
    public CorrectionOrderHandlerRecordDTO loadCorrectionLastRecord(CorrectionHandlerUniInfo handlerUniInfo) {
        CorrectionOrderHandlerRecordExample recordUniExample = this.fillRecordExample(handlerUniInfo);
        recordUniExample.setOrderByClause("utc_create DESC");
        PageHelper.startPage(CorrectionConstant.NUM_ONE, CorrectionConstant.NUM_ONE);
        List<CorrectionOrderHandlerRecord> list = handlerRecordMapper.selectByExample(recordUniExample);
        if (CollectionUtils.isEmpty(list)) {
            return Nullable.getNullVal();
        }
        return assembler.handlerRecordPoToModel(list.get(0));//CHECKED 查询条件写死了 查询一条
    }

    @Override
    public void storeCorrectionResVoucherInfo(String correctionNo, VoucherInfo voucherInfo) {
        CorrectionOrderInfo correctionOrderInfo = new CorrectionOrderInfo();
        correctionOrderInfo.setResVoucherInfo(JSONObject.toJSONString(voucherInfo));
        correctionOrderInfo.setCorrectionNo(correctionNo);
        correctionOrderInfo.setResVoucherNo(voucherInfo.getVoucherNo());
        orderInfoMapper.updateByPrimaryKeySelective(correctionOrderInfo);
    }

    @Override
    public void storeCorrectionStrategyInfo(CorrectionOrderInfoDTO orderInfoModel) {
        CorrectionOrderInfo correctionOrderInfo = new CorrectionOrderInfo();
        correctionOrderInfo.setCorrectionNo(orderInfoModel.getBaseInfo().getCorrectionNo());
        correctionOrderInfo.setOperationStrategyCode(orderInfoModel.getStrategyCode());
        correctionOrderInfo.setOperationCorrectionCode(orderInfoModel.getOperationCorrectionCode());
        correctionOrderInfo.setOperator(Optional.ofNullable(orderInfoModel.getOpBasicInfo()).map(OperationBasicInfo::getOperator).orElse(Nullable.getNullVal()));
        correctionOrderInfo.setReviewer(orderInfoModel.getReviewer());
        if (Objects.nonNull(orderInfoModel.getOperationManual())) {
            correctionOrderInfo.setMemo(StringUtils.defaultIfBlank(orderInfoModel.getOperationManual().getMemo(), Nullable.getNullVal()));
            correctionOrderInfo.setExtendInfo(StringUtils.defaultIfBlank(orderInfoModel.getOperationManual().getExtendInfo(), Nullable.getNullVal()));
            correctionOrderInfo.setUserInfo(StringUtils.defaultIfBlank(orderInfoModel.getOperationManual().getExtraUserInfo(), Nullable.getNullVal()));
        }
        orderInfoMapper.updateByPrimaryKeySelective(correctionOrderInfo);
    }

    @Override
    public Boolean restoreStatus(String correctionNo, String expect, String target) {
        return this.restoreStatus(correctionNo, expect, target, Nullable.getNullVal());
    }

    @Override
    public Boolean restoreStatus(String correctionNo, String expect, String target, Date completeTime) {
        int updateNum = orderInfoMapper.updateByOptimisticLock(correctionNo, expect, target, completeTime);
        return updateNum > CorrectionConstant.NUM_ZERO;
    }

    @Override
    public Boolean restoreHandlerStatus(CorrectionHandlerUniInfo handlerUniInfo, String expect, String target) {
        if (StringUtils.isAnyBlank(handlerUniInfo.getHandlerType(), handlerUniInfo.getCorrectionNo(), handlerUniInfo.getStrategyCode())) {
            return Boolean.FALSE;
        }
        int updateNum = handlerRecordMapper.updateByOptimisticLock(handlerUniInfo.getCorrectionNo(), handlerUniInfo.getStrategyCode(), handlerUniInfo.getHandlerType(), expect, target, handlerUniInfo.getProcessRequest());
        return updateNum > CorrectionConstant.NUM_ZERO;
    }

    @Override
    public CorrectionOrderInfoDTO loadCorrectionOrderInfo(String correctionNo) {
        CorrectionOrderInfo correctionOrderInfo = orderInfoMapper.selectByPrimaryKey(correctionNo);
        if (Objects.isNull(correctionOrderInfo)) {
            return Nullable.getNullVal();
        }
        return assembler.orderInfoPoToModel(correctionOrderInfo);
    }

    @Override
    public CorrectionOrderInfoDTO loadCorrectionOrderInfo(String correctionCode, String voucherNo) {
        //差错code为空，则查请求Request
        VoucherUniqueInfo.QueryType type = StringUtils.isNotBlank(correctionCode) && CorrectionConstant.RESULT_VOUCHER_INFO.contains(correctionCode) ? VoucherUniqueInfo.QueryType.RESULT : VoucherUniqueInfo.QueryType.REQUEST;
        CorrectionOrderInfo correctionOrderInfo = orderInfoMapper.selectByUniqueKey(new VoucherUniqueInfo(correctionCode, voucherNo, type));
        if (Objects.isNull(correctionOrderInfo)) {
            return Nullable.getNullVal();
        }
        return assembler.orderInfoPoToModel(correctionOrderInfo);
    }

    @Override
    public List<CorrectionOperationUniqueDTO> getCorrectionCodeMappingValidList(String correctionCode, TradeType tradeType) {
        CorrectionOperationStrategyMappingExample strategyMappingExample = new CorrectionOperationStrategyMappingExample();
        CorrectionOperationStrategyMappingExample.Criteria criteria = strategyMappingExample.createCriteria();
        criteria.andCorrectionCodeEqualTo(correctionCode)
                .andIsValidEqualTo(ValidType.VALID.getVal());
        if (Objects.nonNull(tradeType)) {
            criteria.andTradeTypeEqualTo(tradeType.name());
        }
        List<CorrectionOperationStrategyMapping> strategyMappingList = strategyMappingMapper.selectByExample(strategyMappingExample);
        return strategyMappingList.stream().map(assembler::toOperationUni).collect(Collectors.toList());
    }

    @Override
    public void storeStrategyInfoReasonMapping(List<String> strategyList, final String correctionCode, final String operator, TradeType tradeType) {
        if (CollectionUtils.isEmpty(strategyList)) {
            return;
        }
        List<CorrectionOperationStrategyMapping> list = strategyList.stream().map(e -> {
            CorrectionOperationStrategyMapping strategyMapping = new CorrectionOperationStrategyMapping();
            strategyMapping.setOperationStrategyCode(e);
            strategyMapping.setCorrectionCode(correctionCode);
            strategyMapping.setTradeType(tradeType.name());
            strategyMapping.setOperator(operator);
            strategyMapping.setIsValid(ValidType.VALID.getVal());
            return strategyMapping;
        }).collect(Collectors.toList());
        strategyMappingMapper.batchInsert(list);
    }

    @Override
    public void invalidStrategyInfoReasonMapping(String correctionCode, TradeType tradeType) {
        CorrectionOperationStrategyMapping record = new CorrectionOperationStrategyMapping();
        record.setIsValid(ValidType.INVALID.getVal());
        CorrectionOperationStrategyMappingExample example = new CorrectionOperationStrategyMappingExample();
        example.createCriteria()
                .andTradeTypeEqualTo(tradeType.name())
                .andIsValidEqualTo(ValidType.VALID.getVal())
                .andCorrectionCodeEqualTo(correctionCode);
        strategyMappingMapper.updateByExampleSelective(record, example);
    }

    @Override
    public Page<CorrectionOrderInfoDTO> loadCorrectionOrderInfo(QueryCorrectionOrderPageDTO queryCorrectionPage) {
        //填充查询条件
        QueryCorrectionOrderInfo queryOrderInfo = QueryCorrectionOrderInfo.builder()
                .correctionNo(Optional.ofNullable(queryCorrectionPage.getBaseInfo()).map(CorrectionBaseInfo::getCorrectionNo).orElse(Nullable.getNullVal()))
                .correctionCode(Optional.ofNullable(queryCorrectionPage.getBaseInfo()).map(CorrectionBaseInfo::getCorrectionCode).orElse(Nullable.getNullVal()))
                .operationCorrectionCode(queryCorrectionPage.getOperationCorrectionCode())
                .processStatus(queryCorrectionPage.getStatus())
                .channelCode(queryCorrectionPage.getChannelCode())
                .merchantOrderNo(queryCorrectionPage.getMerchantOrderNo())
                .voucherNo(Optional.ofNullable(queryCorrectionPage.getBaseInfo()).map(CorrectionBaseInfo::getVoucherInfo).map(VoucherInfo::getVoucherNo).orElse(Nullable.getNullVal()))
                .sysSource(queryCorrectionPage.getSysSource())
                .reviewer(queryCorrectionPage.getReviewer())
                .tradeType(Optional.ofNullable(queryCorrectionPage.getTradeType()).map(TradeType::name).orElse(Nullable.getNullVal()))
                .redundantInfo(Optional.ofNullable(queryCorrectionPage.getRedundantInfo()).map(ReconcileRedundantDTO::queryRedundantInfoStr).orElse(Nullable.getNullVal()))
                .startTime(Optional.ofNullable(queryCorrectionPage.getPage().getStartTime()).map(Date::new).orElse(Nullable.getNullVal()))
                .endTime(Optional.ofNullable(queryCorrectionPage.getPage().getEndTime()).map(Date::new).orElse(Nullable.getNullVal()))
                .operator(queryCorrectionPage.getOperator())
                .build();
        //分页查询
        Page<CorrectionOrderInfo> page = new Page<>(queryCorrectionPage.getPage().getPageNum(), queryCorrectionPage.getPage().getPageSize());

        List<CorrectionOrderInfo> orderInfos = orderInfoMapper.selectPageRequest(page, queryOrderInfo);

        return this.transferCorrectionOrderInfo(orderInfos, page);
    }

    @Override
    public Page<CorrectionOrderInfoDTO> loadCorrectionOrderInfo(PageRequest page, List<String> correctionNos, List<String> voucherNos) {
        Page<CorrectionOrderInfo> pageOrder = new Page<>(page.getPageNum(), page.getPageSize());

        List<CorrectionOrderInfo> orderInfos = orderInfoMapper.selectPageRequestList(pageOrder, correctionNos, voucherNos);
        return this.transferCorrectionOrderInfo(orderInfos, pageOrder);
    }

    @Override
    public void storeManualFillInfo(String correctionNo, OperationManualFillIn operationManual) {
        CorrectionOrderInfo correctionOrderInfo = new CorrectionOrderInfo();
        correctionOrderInfo.setCorrectionNo(correctionNo);
        correctionOrderInfo.setMemo(operationManual.getMemo());
        correctionOrderInfo.setExtendInfo(operationManual.getExtendInfo());
        correctionOrderInfo.setUserInfo(operationManual.getExtraUserInfo());
        orderInfoMapper.updateByPrimaryKeySelective(correctionOrderInfo);
    }

    @Override
    public void refreshReasonBasic(CorrectionBasicInfoDTO basicInfoDTO) {
        basicInfoMapper.updateByPrimaryKeySelective(assembler.basicModelToPo(basicInfoDTO));
    }

    @Override
    public void updateIrrelevantInfo(CorrectionOrderInfoDTO dto) {
        orderInfoMapper.updateIrrelevantInfo(assembler.orderInfoModelToPo(dto));
    }

    @Override
    public void storeCorrectionReviewer(CorrectionOrderInfoDTO orderInfoDTO) {
        CorrectionOrderInfo correctionOrderInfo = new CorrectionOrderInfo();
        correctionOrderInfo.setCorrectionNo(orderInfoDTO.getBaseInfo().getCorrectionNo());
        correctionOrderInfo.setReviewer(orderInfoDTO.getReviewer());
        correctionOrderInfo.setOperator(orderInfoDTO.getOpBasicInfo().getOperator());
        orderInfoMapper.updateByPrimaryKeySelective(correctionOrderInfo);
    }

    @Override
    public void storeCorrectionOpBasicInfo(String correctionNo, OperationBasicInfo basicInfo) {
        CorrectionOrderInfo orderInfo = new CorrectionOrderInfo();
        orderInfo.setCorrectionNo(correctionNo);
        orderInfo.setOperator(basicInfo.getOperator());
        orderInfoMapper.updateByPrimaryKeySelective(orderInfo);
    }

    @Override
    public List<CorrectionOrderInfoDTO> loadCorrectionOrderInfo(QueryCheckingOrderDTO queryCheckingOrder) {
        CorrectionOrderInfo orderInfo = new CorrectionOrderInfo();
        orderInfo.setTradeType(queryCheckingOrder.getTradeType().name());
        orderInfo.setCorrectionCode(queryCheckingOrder.getCorrectionCode());
        orderInfo.setChannelCode(queryCheckingOrder.getChannelCode());
        orderInfo.setProcessStatus(queryCheckingOrder.getProcessStatus());
        orderInfo.setOriginalRedundantInfo(queryCheckingOrder.getRedundantInfoStr());
        List<CorrectionOrderInfo> orderInfoList = orderInfoMapper.selectByCheckingOrder(orderInfo);
        return orderInfoList.stream().map(assembler::orderInfoPoToModel).collect(Collectors.toList());
    }

    private CorrectionOrderHandlerRecordExample fillRecordExample(CorrectionHandlerUniInfo handlerUniInfo) {
        CorrectionOrderHandlerRecordExample recordExample = new CorrectionOrderHandlerRecordExample();

        CorrectionOrderHandlerRecordExample.Criteria criteria = recordExample.createCriteria();
        criteria.andCorrectionNoEqualTo(handlerUniInfo.getCorrectionNo());
        if (StringUtils.isNotBlank(handlerUniInfo.getStrategyCode())) {
            criteria.andStrategyCodeEqualTo(handlerUniInfo.getStrategyCode());
        }
        //这里是确定的处理器
        if (StringUtils.isNotBlank(handlerUniInfo.getHandlerType())) {
            criteria.andHandlerTypeEqualTo(handlerUniInfo.getHandlerType());
        }
        //这里判断是否需要查询处理请求
        if (StringUtils.isNotBlank(handlerUniInfo.getProcessRequest())) {
            criteria.andProcessRequestEqualTo(handlerUniInfo.getProcessRequest());
        }
        return recordExample;
    }

    private Page<CorrectionOrderInfoDTO> transferCorrectionOrderInfo(List<CorrectionOrderInfo> orderInfos, Page<CorrectionOrderInfo> page) {
        //transfer
        List<CorrectionOrderInfoDTO> listRet = orderInfos.stream().map(assembler::orderInfoPoToModel).collect(Collectors.toList());
        Page<CorrectionOrderInfoDTO> pageRet = new Page<>(page.getCurrent(), page.getSize());
        BeanUtils.copyProperties(page, pageRet);
        pageRet.setRecords(listRet);
        return pageRet;
    }

}
