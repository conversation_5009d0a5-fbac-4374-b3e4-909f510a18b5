package com.payermax.operating.correction.integration.persistence.kvstore.repository;

import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;

import java.util.concurrent.TimeUnit;

/**
 * The interface Kv repository.
 *
 * <AUTHOR>
 * @desc kv 资源库
 * @date 2023 /7/13
 */
public interface KVRepository {

    /**
     * Store global order.
     *
     * @param voucherInfo the voucher info
     * @param orderInfo   the order info
     */
    void storeGlobalOrder(VoucherInfo voucherInfo, GlobalOrderInfo orderInfo);

    /**
     * Query global order global order info.
     *
     * @param voucherInfo the voucher info
     * @return the global order info
     */
    GlobalOrderInfo queryGlobalOrder(VoucherInfo voucherInfo);

    /**
     * Remove global order global order info.
     *
     * @param voucherInfo the voucher info
     * @return the global order info
     */
    void removeGlobalOrder(VoucherInfo voucherInfo);

    Boolean applyDistributeLock(String key,String value,long second);

    Boolean releaseDistributeLock(String key,String expValue);

    Long incr(String key);

    Boolean expire(String key, Long timeOut, TimeUnit unit);

    Boolean remove(String key);

}
