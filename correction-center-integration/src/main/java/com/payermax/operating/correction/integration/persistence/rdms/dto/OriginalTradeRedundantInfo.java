package com.payermax.operating.correction.integration.persistence.rdms.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @desc 原交易冗余信息
 * @date 2023/4/14
 */
@Data
@NoArgsConstructor
public class OriginalTradeRedundantInfo {
    /**
     * 产品码
     */
    private String productCode;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 退款类型
     */
    private String refundTypeSource;

    /**
     * 重试类型
     */
    private String retryType;

    /**
     * 映射错误码code
     */
    private String errorCode;

    /**
     * 映射错误码描述
     */
    private String errorMsg;

    /**
     * 完成时间
     */
    private String channelCompleteTime;

    public OriginalTradeRedundantInfo(String productCode, String merchantNo, String refundTypeSource, String retryType, String errorCode, String errorMsg, String channelCompleteTime) {
        this.productCode = productCode;
        this.merchantNo = merchantNo;
        this.refundTypeSource = refundTypeSource;
        this.retryType = retryType;
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
        this.channelCompleteTime = channelCompleteTime;
    }

    public String queryMerchantNoStr() {
        if (StringUtils.isEmpty(merchantNo)){
            return StringUtils.EMPTY;
        }
        return " & +\"merchantNo\" & +\"" + merchantNo + "\"";
    }

}
