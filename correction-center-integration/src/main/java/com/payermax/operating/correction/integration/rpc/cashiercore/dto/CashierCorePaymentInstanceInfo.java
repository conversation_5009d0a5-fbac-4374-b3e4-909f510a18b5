package com.payermax.operating.correction.integration.rpc.cashiercore.dto;

import com.payermax.operating.correction.integration.dto.PaymentInstanceDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 支付实例对象信息
 * @date 2022/12/8
 */
@Data
@NoArgsConstructor
public class CashierCorePaymentInstanceInfo {

    private List<PaymentInstanceDTO> paymentInstanceList;

    /**
     * 出款10，收单20
     */
    private String paymentType;

    /**
     * 国家
     */
    private String country;

}
