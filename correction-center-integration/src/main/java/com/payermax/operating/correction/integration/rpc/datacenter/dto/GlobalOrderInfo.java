package com.payermax.operating.correction.integration.rpc.datacenter.dto;

import com.alibaba.fastjson.JSONObject;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.StringUtil;
import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.dto.MerchantInfo;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.OrderCenterPayOrderExtendField;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.OrderCenterRefundTradeExtendField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc 统一扩展抽象处理请求
 * @date 2022/9/22
 */
@Data
@NoArgsConstructor
@Slf4j
public class GlobalOrderInfo {

    /**
     * 原始商户信息
     */
    private MerchantInfo originalMerchantInfo;

    /**
     * 交易信息
     */
    private TradeInfo tradeInfo;

    /**
     * 资产交换信息
     */
    private AssetInfo assetInfo;

    /**
     * 渠道信息
     */
    private ChannelInfo channelInfo;

    /**
     * 产品码
     */
    private String productCode;

    /**
     * 业务身份
     */
    private String bizIdentify;


    /**
     * <AUTHOR>
     * @desc 交易信息
     * @date 2022/9/22
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradeInfo {

        /**
         * 交易单
         */
        private OrderInfo tradeOrder;

        /**
         * 支付请求单
         */
        private OrderInfo payRequest;

        /**
         * 退款单
         */
        private RefundOrderInfo refundInfo;

        /**
         * 出款单
         */
        private OrderInfo payoutsOrder;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PayRequestOrderInfo{

        private OrderInfo payOrder;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RefundOrderInfo{

        private OrderInfo refundOrder;

        private String refundType;

        private String createTime;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AssetInfo {
        /**
         * 资产交换订单
         */
        private OrderInfo assetOrder;

        /**
         * 支付金额
         */
        private Money payMoney;

        /**
         * 交易类型
         */
        private TradeType tradeType;
    }

    /**
     * <AUTHOR>
     * @desc 渠道信息
     * @date 2022/9/22
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChannelInfo {

        /**
         * 渠道提交单
         */
        private List<ChannelCommitInfo> channelCommit;

        /**
         * 最后一条提交单记录
         */
        private ChannelCommitInfo lastChannelCommit;

        /**
         * 渠道请求单
         */
        private OrderInfo channelRequest;

        /**
         * 渠道完成时间
         */
        private String completeTime;

        /**
         * 渠道响应code
         */
        private String respCode;

        /**
         * 渠道响应msg
         */
        private String respMsg;

        /**
         * 支付金额
         */
        private Money payAmount;

    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChannelCommitInfo{

        private OrderInfo commitInfo;

        private String channelCode;

        private String thirdOrderNo;

        /**
         * 渠道支付金额
         */
        private Money payAmount;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OrderInfo {
        /**
         * 订单号
         */
        private String orderNo;

        /**
         * 订单状态
         */
        private String status;
        /**
         * 支付完成时间（交易单上的支付完成时间）
         */
        private String payFinishTime;
        /**
         * 产品码
         */
        private String productCode;
        /**
         * 扩展字段信息（支付单）
         * 退款单扩展字段也是这个字段
         */
        private String extendField;
        /**
         * 差错原单单号
         */
        private String correctionOriginOrderNo ;
        /**
         * 差错原商户订单号
         */
        private String correctionOriginOutTradeNo;

        private String paymentMode; // 支付方式
        private String targetOrg; // 目标机构

        private boolean haveDiscount; // 是否有营销
        /**
         * 从退款扩展字段获取原支付单号
         * @return
         */
        public String getOriPayRequestNoFromRefundExtendedField(){
            OrderCenterRefundTradeExtendField orderCenterRefundTradeExtendField = JSONObject.parseObject(this.extendField, OrderCenterRefundTradeExtendField.class);
            return Objects.nonNull(orderCenterRefundTradeExtendField) ? orderCenterRefundTradeExtendField.getOriginPayRequestNo() : null;
        }


        public String getOriPayRequestNo(){
            OrderCenterPayOrderExtendField orderCenterPayOrderExtendField = null;
            try {
                orderCenterPayOrderExtendField = JSONObject.parseObject(this.extendField, OrderCenterPayOrderExtendField.class);
                //如果支付单扩展字段中的差错原支付单号不为空则返回（这里只有重复支付场景下才会上送原支付单号，其余场景还是保持现状不变）
                if (Objects.nonNull(orderCenterPayOrderExtendField) && StringUtils.isNotBlank(orderCenterPayOrderExtendField.getCorrectionRepeatOriPayRequestNo())) {
                    return orderCenterPayOrderExtendField.getCorrectionRepeatOriPayRequestNo();
                }
            } catch (Exception e) {
                log.error("getOriPayRequestNoError",e);
                return this.orderNo;
            }
            return this.orderNo;
        }

        public String getOriTradeOrderNo(){
            if (StringUtils.isNotBlank(this.correctionOriginOrderNo)){
                log.info("getOriTradeOrderNoFromCorrectionOrder correctionOriginOrderNo:{}",correctionOriginOrderNo);
                return this.correctionOriginOrderNo;
            }
            return this.orderNo;
        }
    }

    public void validation() {
        AssertUtil.isTrue(StringUtil.isNotBlank(productCode),"productCode is invalid");
        AssertUtil.isTrue(Objects.nonNull(originalMerchantInfo) && StringUtil.isNotBlank(originalMerchantInfo.getMerOrderNo()), ReturnCode.ILLEGAL_PARAMS.getCode(), "originalMerchantInfo is invalid");
        assetInfoValidation();
    }

    public String getOriginOutTradeNo(){
        String correctionOriOutTradeNo = Optional.ofNullable(this.tradeInfo)
                .map(TradeInfo::getTradeOrder)
                .map(OrderInfo::getCorrectionOriginOutTradeNo)
                .orElse(null);
        if (StringUtils.isNotBlank(correctionOriOutTradeNo)) {
            log.info("getOriginOutTradeNoFromCorrectionOrder correctionOriginOrderNo:{}",correctionOriOutTradeNo);
            return correctionOriOutTradeNo;
        }
        return Objects.nonNull(this.getOriginalMerchantInfo()) ? this.getOriginalMerchantInfo().getMerOrderNo() : null;
    }

    public void tradePayInfoValidation() {
        validation();
        AssertUtil.isTrue(Objects.nonNull(tradeInfo)
                        && Objects.nonNull(tradeInfo.getTradeOrder())
                        && Objects.nonNull(tradeInfo.getPayRequest())
                        && StringUtil.isNotBlank(tradeInfo.getPayRequest().getOrderNo())
                        && StringUtil.isNotBlank(tradeInfo.getTradeOrder().getOrderNo()),
                ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.INTERFACE_TRADE_INFO_INVALID);

    }

    public void tradeRefundInfoValidation() {
        validation();
        AssertUtil.isTrue(Objects.nonNull(tradeInfo)
                        && Objects.nonNull(tradeInfo.getRefundInfo())
                        && Objects.nonNull(tradeInfo.getRefundInfo().getRefundOrder())
                        && StringUtil.isNotBlank(tradeInfo.getRefundInfo().getRefundOrder().getOrderNo()),
                ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.INTERFACE_REFUND_INFO_INVALID);

    }

    public void assetInfoValidation() {
        AssertUtil.isTrue(Objects.nonNull(assetInfo)
                        && Objects.nonNull(assetInfo.getAssetOrder())
                        && StringUtil.isNotBlank(assetInfo.getAssetOrder().getOrderNo())
                        && Objects.nonNull(assetInfo.getTradeType()),
                ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.INTERFACE_ASSET_INFO_INVALID);

    }

    public void tradeTypeValidation() {
        assetInfoValidation();
        AssertUtil.isTrue(Objects.nonNull(assetInfo.getAssetOrder()), ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.INTERFACE_ASSET_INFO_INVALID);

    }

    public void channelCommitInfoValidation() {
        AssertUtil.isTrue(Objects.nonNull(channelInfo)
                        && Objects.nonNull(channelInfo.getChannelCommit())
                        && StringUtil.isNotBlank(channelInfo.getLastChannelCommit().getCommitInfo().getOrderNo()),
                ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.INTERFACE_CHANNEL_INFO_INVALID);

    }

    public void channelRequestInfoValidation() {
        AssertUtil.isTrue(Objects.nonNull(channelInfo)
                        && Objects.nonNull(channelInfo.getChannelRequest())
                        && StringUtil.isNotBlank(channelInfo.getChannelRequest().getStatus()),
                ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.INTERFACE_CHANNEL_INFO_INVALID);
    }

    public void channelCodeValidation() {
        channelCommitInfoValidation();
        AssertUtil.isTrue(StringUtil.isNotBlank(channelInfo.getLastChannelCommit().getChannelCode()),
                ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.INTERFACE_CHANNEL_INFO_INVALID);

    }

}
