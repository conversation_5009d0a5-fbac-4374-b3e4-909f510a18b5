package com.payermax.operating.correction.integration.persistence.rdms.repository;

import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.OrderDetailType;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyMapping;
import com.payermax.operating.correction.integration.persistence.rdms.dto.*;

import java.util.Date;
import java.util.List;

/**
 * The interface Correction repository.
 *
 * <AUTHOR>
 * @desc 非人为操作的资源操作
 * @date 2022/9/28
 */
public interface CorrectionRepository {

    /**
     * Store correction order info.
     *
     * @param correctionOrderInfo the correction order info
     */
    void storeCorrectionOrderInfo(CorrectionOrderInfoDTO correctionOrderInfo);

    /**
     * Store order handler record info.
     *
     * @param handlerRecord the handler record
     */
    void storeOrderHandlerRecordInfo(CorrectionOrderHandlerRecordDTO handlerRecord);

    /**
     * Store order handler record result info.
     *
     * @param handlerInfo the handler info
     * @param res         the res
     */
    void storeOrderHandlerRecordResultInfo(CorrectionHandlerUniInfo handlerInfo, BaseResProcess res);


    /**
     * Patch modify handler record failed.
     *
     * @param orderHandlerRecordDTO the order handler record dto
     * @return boolean
     */
    Boolean patchModifyHandlerRecordDiscard(CorrectionOrderHandlerRecordDTO orderHandlerRecordDTO);

    /**
     * Load correction record list list.
     *
     * @param handlerUniInfo the handler uni info
     * @param queryType      the query type
     * @return the list
     */
    List<CorrectionOrderHandlerRecordDTO> loadCorrectionRecordList(CorrectionHandlerUniInfo handlerUniInfo,OrderDetailType queryType);

    /**
     * Load correction last record correction order handler record dto.
     *
     * @param handlerUniInfo the handler uni info
     * @return the correction order handler record dto
     */
    CorrectionOrderHandlerRecordDTO loadCorrectionLastRecord(CorrectionHandlerUniInfo handlerUniInfo);

    /**
     * Store correction res voucher info.
     *
     * @param correctionNo the correction no
     * @param voucherInfo  the voucher info
     */
    void storeCorrectionResVoucherInfo(String correctionNo, VoucherInfo voucherInfo);

    /**
     * Store correction strategy info.
     *
     * @param correctionOrderInfo the correction order info
     */
    void storeCorrectionStrategyInfo(CorrectionOrderInfoDTO correctionOrderInfo);

    /**
     * 状态机更新差错单状态
     *
     * @param correctionNo the correction no
     * @param expect       the expect
     * @param target       the target
     * @return the boolean
     */
    Boolean restoreStatus(String correctionNo, String expect, String target);

    /**
     * Restore status boolean.
     *
     * @param correctionNo the correction no
     * @param expect       the expect
     * @param target       the target
     * @param completeTime the complete time
     * @return the boolean
     */
    Boolean restoreStatus(String correctionNo, String expect, String target, Date completeTime);

    /**
     * Restore handler status boolean.
     *
     * @param handlerUniInfo the handler uni info
     * @param expect         the expect
     * @param target         the target
     * @return the boolean
     */
    Boolean restoreHandlerStatus(CorrectionHandlerUniInfo handlerUniInfo, String expect, String target);

    /**
     * Load correction order info correction order info dto.
     *
     * @param correctionNo the correction no
     * @return the correction order info dto
     */
    CorrectionOrderInfoDTO loadCorrectionOrderInfo(String correctionNo);

    /**
     * Load correction order info correction order info dto.
     *
     * @param correctionCode the correction code
     * @param voucherNo      the voucher no
     * @return the correction order info dto
     */
    CorrectionOrderInfoDTO loadCorrectionOrderInfo(String correctionCode,String voucherNo);

    /**
     * Get correction code mapping valid list list.
     *
     * @param correctionCode the correction code
     * @param tradeType      the trade type
     * @return the list
     */
    List<CorrectionOperationUniqueDTO>getCorrectionCodeMappingValidList(String correctionCode, TradeType tradeType);

    /**
     * Update irrelevant info.
     * 更新无关信息
     *
     * @param correctionOrderInfo the correction order info
     */
    void updateIrrelevantInfo(CorrectionOrderInfoDTO correctionOrderInfo);

    /**
     * Store correction reviewer.
     *
     * @param orderInfoDTO the order info dto
     */
    void storeCorrectionReviewer(CorrectionOrderInfoDTO orderInfoDTO);

    List<CorrectionOrderInfoDTO>loadCorrectionOrderInfo(QueryCheckingOrderDTO queryCheckingOrder);

}
