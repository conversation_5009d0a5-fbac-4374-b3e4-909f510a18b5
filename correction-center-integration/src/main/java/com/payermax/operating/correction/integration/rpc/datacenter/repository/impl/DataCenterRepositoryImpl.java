package com.payermax.operating.correction.integration.rpc.datacenter.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.common.lang.util.money.Money;
import com.payermax.data.query.facade.QueryFacade;
import com.payermax.data.query.facade.QueryOrderFacade;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.MerchantInfo;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.core.common.utils.ObjectsUtils;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.DataCenterResponse;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.datacenter.repository.DataCenterRepository;
import com.payermax.operating.correction.integration.utils.ListSizeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 数据中心联合查询实现
 * @date 2022/9/27
 */
@Component
@Slf4j
public class DataCenterRepositoryImpl implements DataCenterRepository {

    private final String PAY_TYPE = "1";


    @DubboReference(retries = 2, timeout = 10000)
    private QueryOrderFacade queryOrderFacade;

    @DubboReference(version = "1.0")
    private QueryFacade queryFacade;

    @Override
    public GlobalOrderInfo dcGlobalQuery(DCVoucherType voucherType, String orderId) {
        Result<Map<String, List<Map<String, Object>>>> mapResult = null;
        try {
            mapResult = queryOrderFacade.queryForCascadingOrder(CorrectionConstant.CORRECTION_CENTER, voucherType.getDcType(),
                    DCVoucherType.getVoucherPair(orderId, voucherType).getLeft());
        } catch (Exception e) {
            log.error("DataCenterRepositoryImpl dcGlobalQuery rpc exception:,voucherType :{}, orderId:{}", voucherType.getDcType(), orderId, e);
            throw new BusinessException(ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_DATA_CENTER_EXCEPTION, e);
        }
        return this.convert(mapResult);
    }

    private GlobalOrderInfo convert(Result<Map<String, List<Map<String, Object>>>> result) {
        AssertUtil.isTrue(ResultUtil.isApplySuccess(result), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_DATA_CENTER_EXCEPTION);
        DataCenterResponse dataCenter = JSONObject.parseObject(JSONObject.toJSONString(result.getData()), new TypeReference<DataCenterResponse>() {
        });
        return buildCorrectionGlobalCorrection(dataCenter);
    }

    /**
     * 由差错中心屏蔽数据中心返回的差异信息
     */
    public GlobalOrderInfo buildCorrectionGlobalCorrection(DataCenterResponse dataCenterResponse) {
        //创建原始订单信息和差错订单信息
        GlobalOrderInfo globalOrderInfo = new GlobalOrderInfo();

        //填充资产交换信息
        Optional.ofNullable(dataCenterResponse.getAssetInfos()).ifPresent(assets -> {
            for (DataCenterResponse.DataCenterAssetInfoDTO assetInfoDTO : assets) {
                Money payAmount = ObjectsUtils.buildMoney(assetInfoDTO.getAmount(), assetInfoDTO.getCurrency());
                GlobalOrderInfo.AssetInfo assetInfo = new GlobalOrderInfo.AssetInfo(GlobalOrderInfo.OrderInfo.builder()
                        .orderNo(assetInfoDTO.getPayOrderNo())
                        .status(assetInfoDTO.getStatus())
                        .build(), payAmount, TradeType.getTradeType(assetInfoDTO.getPayType()));
                Optional.ofNullable(assetInfo.getAssetOrder()).ifPresent(e -> {
                    globalOrderInfo.setAssetInfo(assetInfo);
                    globalOrderInfo.setProductCode(assetInfoDTO.getProductCode());
                });
            }
        });

        //填充商户订单信息，交易订单信息
        this.wrapTradeOrderInfo(dataCenterResponse, globalOrderInfo);

        //填充支付请求单信息
        Optional.ofNullable(dataCenterResponse.getPayRequests()).ifPresent(payRequests -> {
            for (DataCenterResponse.DataCenterPayRequestInfoDTO payRequest : payRequests) {
                // 从收单-订单中心的数据模型来看，支付与退款在有营销时，paymentTool的模型是一致的，均有DISCOUNT的支付工具记录
                boolean haveDiscount = payRequest.haveDiscount();
                DataCenterResponse.DataCenterPaymentModeToolInfo paymentModeTool = payRequest.getPaymentModeTool();
                GlobalOrderInfo.OrderInfo orderInfo = GlobalOrderInfo.OrderInfo.builder()
                        .orderNo(payRequest.getPayRequestNo())
                        .status(payRequest.getStatus())
                        .productCode(payRequest.getProductCode())
                        .extendField(payRequest.getExtendField())
                        .paymentMode(Objects.nonNull(paymentModeTool) ? paymentModeTool.getPayTooPaymentMode() : null)
                        .targetOrg(Objects.nonNull(paymentModeTool) ? paymentModeTool.getPayTooTargetOrg() : null)
                        .haveDiscount(haveDiscount)
                        .build();
                GlobalOrderInfo.TradeInfo tradeInfo = Optional.ofNullable(globalOrderInfo.getTradeInfo()).orElse(new GlobalOrderInfo.TradeInfo());
                tradeInfo.setPayRequest(orderInfo);
                globalOrderInfo.setTradeInfo(tradeInfo);
            }
        });

        //填充退款单信息
        if (CollectionUtils.isNotEmpty(dataCenterResponse.getRefundInfos()) && ListSizeUtils.listSizeEqualOne(dataCenterResponse.getRefundInfos(),"matchRefundInfos")) {
            GlobalOrderInfo.TradeInfo tradeInfo = Optional.ofNullable(globalOrderInfo.getTradeInfo()).orElse(new GlobalOrderInfo.TradeInfo());
            DataCenterResponse.DataCenterRefundInfoDTO refundInfo = dataCenterResponse.getRefundInfos().get(0);//CHECKED
            GlobalOrderInfo.OrderInfo orderInfo = GlobalOrderInfo.OrderInfo.builder()
                    .orderNo(refundInfo.getRefundNo())
                    .extendField(refundInfo.getExtendField())
                    .status(refundInfo.getStatus())
                    .build();
            GlobalOrderInfo.RefundOrderInfo refundOrderInfo = new GlobalOrderInfo.RefundOrderInfo(orderInfo, refundInfo.getRefundType(), refundInfo.getCreateTime());
            tradeInfo.setRefundInfo(refundOrderInfo);
            globalOrderInfo.setTradeInfo(tradeInfo);
        }

        List<DataCenterResponse.DataCenterChannelInfoDTO> oriChannelInfoDTOList = dataCenterResponse.getChannelInfo();
        if (CollectionUtils.isNotEmpty(oriChannelInfoDTOList)) {
            List<DataCenterResponse.DataCenterChannelInfoDTO> channelInfoDTOList = oriChannelInfoDTOList.stream().sorted(Comparator.comparing(DataCenterResponse.DataCenterChannelInfoDTO::getCreateTime)).collect(Collectors.toList());
            //填充渠道信息
            for (DataCenterResponse.DataCenterChannelInfoDTO channelInfoDTO : channelInfoDTOList) {
                //渠道提交单信息
                GlobalOrderInfo.ChannelInfo channelInfo = new GlobalOrderInfo.ChannelInfo();
                channelInfo.setChannelRequest(GlobalOrderInfo.OrderInfo.builder()
                        .orderNo(channelInfoDTO.getChannelRequestNo())
                        .status(channelInfoDTO.getChlReqStatus())
                        .build());
                channelInfo.setCompleteTime(channelInfoDTO.getCompleteTime());
                channelInfo.setRespMsg(channelInfoDTO.getRespMsg());
                channelInfo.setRespCode(channelInfoDTO.getRespCode());
                channelInfo.setPayAmount(ObjectsUtils.buildMoney(channelInfoDTO.getReqAmount(), channelInfoDTO.getReqCurrency()));
                globalOrderInfo.setChannelInfo(channelInfo);

                Optional.ofNullable(channelInfoDTO.getChannelCommits()).ifPresent(channelCommit -> {
                    List<DataCenterResponse.DataCenterChannelCommitInfoDTO> channelCommitInfoList = channelCommit.stream()
                            .filter(e -> PAY_TYPE.equals(e.getType())).collect(Collectors.toList());
                    List<GlobalOrderInfo.ChannelCommitInfo> channelCommitList = Lists.newArrayList();
                    Optional.ofNullable(channelCommitInfoList).ifPresent(e -> {
                        for (DataCenterResponse.DataCenterChannelCommitInfoDTO channelCommitInfoDTO : e) {
                            Money payAmount = ObjectsUtils.buildMoney(channelCommitInfoDTO.getPayAmount(), channelCommitInfoDTO.getPayCurrency());
                            GlobalOrderInfo.ChannelCommitInfo channelCommitInfo = new GlobalOrderInfo.ChannelCommitInfo(
                                    GlobalOrderInfo.OrderInfo.builder()
                                            .orderNo(channelCommitInfoDTO.getChannelCommitNo())
                                            .status(channelCommitInfoDTO.getStatus())
                                            .payFinishTime(channelCommitInfoDTO.getCommitCompleteTime())
                                            .build(),
                                    channelCommitInfoDTO.getChannelCode(), channelCommitInfoDTO.getThirdOrderNo(), payAmount);
                            channelCommitList.add(channelCommitInfo);
                            channelInfo.setLastChannelCommit(channelCommitInfo);
                        }
                    });
                    channelInfo.setChannelCommit(channelCommitList);
                });
            }
        }
        return globalOrderInfo;
    }

    public void wrapTradeOrderInfo(DataCenterResponse dataCenterResponse, GlobalOrderInfo globalOrderInfo) {

        TradeType tradeType = Nullable.getNullVal();
        if (Objects.nonNull(globalOrderInfo.getAssetInfo())) {
            tradeType = globalOrderInfo.getAssetInfo().getTradeType();
        }
        if (TradeType.PAYMENT == tradeType) {
            //创建交易信息
            //填充商户信息以及交易单信息
            Optional.ofNullable(dataCenterResponse.getTradeOrders()).ifPresent(tradeOrders -> {
                for (DataCenterResponse.DataCenterOrderInfoDTO orderInfo : tradeOrders) {
                    GlobalOrderInfo.TradeInfo tradeInfo = new GlobalOrderInfo.TradeInfo();
                    //填充业务身份
                    globalOrderInfo.setBizIdentify(orderInfo.getBizIdentify());
                    MerchantInfo merchantInfo = new MerchantInfo(orderInfo.getMerchantNo(), orderInfo.getOutOrderNo(), orderInfo.getMerchantAppId());

                    String ouTradeNo = Optional.ofNullable(orderInfo.getOrderExtendField())
                            .map(DataCenterResponse.OrderExtendField::getCorrectionOriTradeOrder)
                            .map(DataCenterResponse.CorrectionOriTradeOrder::getOutTradeNo)
                            .orElse(null);
                    //填充交易信息
                    tradeInfo.setTradeOrder(GlobalOrderInfo.OrderInfo.builder()
                            .orderNo(orderInfo.getTradeOrderNo())
                            .status(orderInfo.getStatus())
                            .payFinishTime(orderInfo.getPayFinishTime())
                            .productCode(orderInfo.getProductCode())
                            .correctionOriginOrderNo(Objects.nonNull(orderInfo.getOrderExtendField()) ? orderInfo.getOrderExtendField().getCorrectionOriginOrderNo() : null)
                            .correctionOriginOutTradeNo(ouTradeNo)
                            .build());
                    if (StringUtils.defaultIfBlank(orderInfo.getTradeOrderNo(), StringUtils.EMPTY).contains(CorrectionConstant.CORRECTION_FLAG)) {
                        globalOrderInfo.setTradeInfo(tradeInfo);
                        //封装商户信息
                        globalOrderInfo.setOriginalMerchantInfo(merchantInfo);
                    } else {
                        globalOrderInfo.setTradeInfo(tradeInfo);
                        //封装商户信息
                        globalOrderInfo.setOriginalMerchantInfo(merchantInfo);
                    }
                }
            });
        } else if (TradeType.REFUND == tradeType) {
            if (CollectionUtils.isNotEmpty(dataCenterResponse.getOriTradeOrders()) && ListSizeUtils.listSizeEqualOne(dataCenterResponse.getOriTradeOrders(),"matchOriTradeOrders")) {
                GlobalOrderInfo.TradeInfo tradeInfo = new GlobalOrderInfo.TradeInfo();
                DataCenterResponse.DataCenterOrderInfoDTO orderInfo = dataCenterResponse.getOriTradeOrders().get(0);//CHECKED
                //填充业务身份
                globalOrderInfo.setBizIdentify(orderInfo.getBizIdentify());
                //封装商户信息
                globalOrderInfo.setOriginalMerchantInfo(new MerchantInfo(orderInfo.getMerchantNo(), orderInfo.getOutOrderNo(), orderInfo.getMerchantAppId()));
                //填充交易信息
                tradeInfo.setTradeOrder(GlobalOrderInfo.OrderInfo.builder()
                        .orderNo(orderInfo.getTradeOrderNo())
                        .status(orderInfo.getStatus())
                        .payFinishTime(orderInfo.getPayFinishTime())
                        .productCode(orderInfo.getProductCode())
                        .build());
                globalOrderInfo.setTradeInfo(tradeInfo);
            }
        } else if (TradeType.PAYOUTS == tradeType) {
            if (CollectionUtils.isNotEmpty(dataCenterResponse.getPayoutsOrders())) {
                GlobalOrderInfo.TradeInfo tradeInfo = new GlobalOrderInfo.TradeInfo();
                //在外部三方单号重复的情况下，PM内部查询就会查询多条出来，无法唯一匹配出一条
                //由于这里最终筛选出来只是在OMC页面上做数据展示，即使取值错误就只是页面展示错误，没有真正的差错处理能力，因此该场景可以忽略不做处理
                DataCenterResponse.DataCenterPayoutsInfoDTO payoutsInfoDTO = dataCenterResponse.getPayoutsOrders().get(0);//CHECKED
                ListSizeUtils.log(dataCenterResponse.getPayoutsOrders(),"matchPayoutsOrders");
                //填充业务身份
                globalOrderInfo.setBizIdentify(payoutsInfoDTO.getBizIdentify());
                //封装商户信息
                globalOrderInfo.setOriginalMerchantInfo(new MerchantInfo(payoutsInfoDTO.getMerchantNo(), payoutsInfoDTO.getOutOrderNo(), Nullable.getNullVal()));
                //填充交易信息
                GlobalOrderInfo.OrderInfo orderInfo = GlobalOrderInfo.OrderInfo.builder()
                        .orderNo(payoutsInfoDTO.getTradeOrderNo())
                        .status(payoutsInfoDTO.getStatus())
                        .payFinishTime(Objects.nonNull(payoutsInfoDTO.getDataCenterPayOutExtendFieldInfoDTO())
                                ? payoutsInfoDTO.getDataCenterPayOutExtendFieldInfoDTO().getPayFinishTime() : null)
                        .productCode(payoutsInfoDTO.getProductCode())
                        .build();
                tradeInfo.setTradeOrder(orderInfo);
                tradeInfo.setPayoutsOrder(orderInfo);
                globalOrderInfo.setTradeInfo(tradeInfo);
            }
        }
    }

    /**
     * 从doris捞取数据
     * @param templateId
     * @param params
     * @return
     */
    public List<Map<String, Object>> queryDoris(String templateId, Map<String, Object> params) {

        try {
            Result<List<Map<String, Object>>> listResult = queryFacade.queryForList(templateId, params);

            if (Objects.isNull(listResult.getData())) {
                return Lists.newArrayList();
            }

            return Lists.newArrayList(listResult.getData());
        } catch (Exception e) {
            log.error("queryDorisError template:{}", templateId);
            return Lists.newArrayList();
        }
    }
}
