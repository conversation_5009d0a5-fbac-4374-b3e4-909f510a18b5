package com.payermax.operating.correction.integration.rpc.payouts.dto;

import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @desc 出款规则对象
 * @date 2022/12/9
 */
@Getter
@Setter
@NoArgsConstructor
public class PayoutsRuleInfo {
    /**
     * 取款码有效天数
     */
    private String expiryDays;
    /**
     * 收款方姓名
     */
    private String payeeName;
    /**
     * 收款方账号
     */
    private String payeeAccount;
    /**
     * 付款方名称
     */
    private String payer;
    /**
     * 收款人银行网点号/分行号
     */
    private String payeeBankBranch;
    /**
     * 收款方关联账号
     */
    private String payeeCorAccount;
    /**
     * 款方所在城市
     */
    private String city;
    /**
     * 收款方个人识别号码
     */
    private String payeeDocumentId;
    /**
     * 备注
     */
    private String transactionNote;
    /**
     * 地址
     */
    private String address;
    /**
     * 收款行所在城市
     */
    private String payeeBankCity;
    /**
     * 收款方银行代码
     */
    private String payeeBankCode;
    /**
     * 证据类型
     */
    private String payeeDocumentType;
    /**
     * 收款账户类型
     */
    private String payeeAccountType;
    /**
     * 通知用户地址邮箱
     */
    private String notifyAddress;
    /**
     * 收款方出生日期
     */
    private String payeeBirthDate;
    /**
     * 收款方移动电话号码
     */
    private String payeePhone;
    /**
     * 收款行名称
     */
    private String payeeBankName;
    /**
     * 收款方银行编码
     */
    private String payeePostCode;
    /**
     * 收款方邮箱
     */
    private String email;
    /**
     * 收款方个人识别号码相关时间
     */
    private String payeeDocumentDa;
    /**
     * 付款金额
     */
    private String amount;
    /**
     * 收款方账户验证数字
     */
    private String checkDigit;
    /**
     * 收款方账号
     */
    private String payeeBankBIC;
}
