package com.payermax.operating.correction.integration.persistence.rdms.dto;

import com.payermax.operating.correction.core.common.enums.TradeType;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class QueryCheckingOrderDTO {

    private TradeType tradeType;

    private String correctionCode;

    private String channelCode;

    private String processStatus;

    private String redundantInfoStr;

}
