package com.payermax.operating.correction.integration.config.nacos.model;

import com.payermax.operating.correction.core.common.enums.TradeType;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 自动策略映射
 * @date 2023/4/20
 */
@Data
public class AutomaticStrategyInfo {

    /**
     * 差错code
     */
    private String correctionCode;

    /**
     * 交易类型
     */
    private TradeType tradeType;

    /**
     * 策略规则
     */
    private StrategyRule rule;
}
