package com.payermax.operating.correction.integration.rpc.asset.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 通知资产交换请求入参
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-12-24 14:28
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AssetNotifyInfo {
    /**
     * 原支付单号
     */
    private String paymentOrderNo;
    /**
     * 原交易单号
     */
    private String tradeOrderNo;
    /**
     * 差错交易单号
     */
    private String correctTradeOrderNo;
    /**
     * 产品码
     */
    private String productCode;
    /**
     * 业务身份主体
     */
    private String bizIdentify;
    /**
     * 商户号
     */
    private String merchantNo;

}
