package com.payermax.operating.correction.integration.persistence.rdms.dto;

import com.payermax.operating.correction.core.common.enums.TradeType;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 差错映射唯一对象
 * @date 2022/11/16
 */
@Data
@NoArgsConstructor
public class CorrectionOperationUniqueDTO {

    private String strategyCode;

    private String correctionCode;

    private TradeType tradeType;
}
