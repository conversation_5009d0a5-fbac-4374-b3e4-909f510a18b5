package com.payermax.operating.correction.integration.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 退款类型
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum RefundTypeSource {
    //退款来源
    CLOSE_PAY("0", "超时关单退款","超时关单退款"),
    MERCHANT("1", "商户退款","商户退款"),
    OMC("2", "运营平台退款","商户退款"),
    MMC("3", "商户后台退款","商户退款"),
    MANNER("4", "手动退款","商户退款"),
    REPEAT_PAY("5", "重复支付退款","重复支付退款"),
    CONFIRMATION_FAILED("6", "商户确认失败退款","商户确认失败退款"),
    CB_ALERT("7","拒付预警退款","拒付预警退款"),

    ;

    private final String code;
    private final String name;
    private final String type;

    private static final Map<String, RefundTypeSource> CODE_STATUS_MAPPING =
            Arrays.stream(values()).collect(Collectors.toMap(RefundTypeSource::getCode, Function.identity()));

    public static RefundTypeSource getByCode(String code) {
        return CODE_STATUS_MAPPING.get(code);
    }
}
