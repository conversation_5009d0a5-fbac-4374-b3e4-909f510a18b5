package com.payermax.operating.correction.integration.persistence.rdms.dto;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.dto.CorrectionBaseInfo;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.dto.OperationManualFillIn;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 差错订单信息
 * @date 2022/9/28
 */
@Data
@NoArgsConstructor
public class CorrectionOrderInfoDTO {

    /**
     * 基础信息
     */
    private CorrectionBaseInfo baseInfo;

    /**
     * 运营选择策略
     */
    private String operationCorrectionCode;

    /**
     * 商户订单号
     */
    private String merchantOrderNo;

    /**
     * 数据来源,1：人工录入，2：系统录入
     */
    private Byte eventSource;

    /**
     * 来源系统
     */
    private String sysSource;

    /**
     * 目标状态
     */
    private String processStatus;

    /**
     * 运营执行策略
     */
    private String strategyCode;

    /**
     * 原始凭证信息
     */
    private VoucherInfo oriVoucherInfo;

    /**
     * 响应凭证信息
     */
    private VoucherInfo resVoucherInfo;

    /**
     * 支付总金额
     */
    private Money payTotalMoney;

    /**
     * 人为填写信息
     */
    private OperationManualFillIn operationManual;

    /**
     * 操作基础信息
     */
    private OperationBasicInfo opBasicInfo;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 推送差错描述信息
     */
    private String detailDesc;

    /**
     * 原始交易冗余展示信息
     */
    private ReconcileRedundantDTO originalTradeRedundantInfo;

    /**
     * 审核人
     */
    private String reviewer;

    public CorrectionOrderInfoDTO(String correctionNo) {
        this.baseInfo = new CorrectionBaseInfo(correctionNo);
    }
}
