package com.payermax.operating.correction.integration.persistence.rdms.dto;

import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.enums.ExtendEnum;
import com.payermax.operating.correction.core.common.enums.UserInfoEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @desc 差错运营策略
 * @date 2022/9/20
 */
@Data
public class CorrectionOperationStrategyInfoDTO {

    /**
     * 策略Code
     */
    private String strategyCode;

    /**
     * 策略名
     */
    private String strategyName;


    private ExtendEnum extendShow;

    private UserInfoEnum extraUserInfo;

    /**
     * 操作基础信息
     */
    private OperationBasicInfo opBasicInfo;
}
