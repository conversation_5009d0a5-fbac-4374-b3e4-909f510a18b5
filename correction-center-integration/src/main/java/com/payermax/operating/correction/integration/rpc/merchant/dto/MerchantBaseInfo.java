package com.payermax.operating.correction.integration.rpc.merchant.dto;

import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * @description: 商户基本信息
 * @author: WangTao
 * @create: 2024-11-26 11:29
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MerchantBaseInfo {
    /**
     * 商户业务类型
     */
    private String bizType;

    private String innerName;

    private String merchantNo;

    /**
     * 返回code码
     */
    private String code;

    /**
     * 返回消息体
     */
    private String msg;

    public void defence() {
        AssertUtil.isTrue(Symbols.RES_SUCCESS.equals(this.code), this.code, ReturnMsg.RPC_MERCHANT_EXCEPTION);
        AssertUtil.isTrue(StringUtils.isNotBlank(this.bizType) , ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_MERCHANT_EXCEPTION);
    }

}
