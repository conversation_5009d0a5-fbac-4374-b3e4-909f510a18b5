package com.payermax.operating.correction.integration.queue.impl;

import com.alibaba.fastjson.JSONObject;
import com.payermax.infra.ionia.rocketmq.handler.IoniaRocketMqTemplate;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.integration.config.nacos.constant.NacosGlobalConfigProperties;
import com.payermax.operating.correction.integration.queue.MqResultNotifyRepository;
import com.payermax.operating.correction.integration.queue.dto.CorrectionDelayHandlerMessage;
import com.payermax.operating.correction.integration.queue.dto.CorrectionEventMessageInfo;
import com.payermax.operating.correction.integration.queue.dto.ExternalResultInfoMessageInfo;
import com.payermax.operating.correction.integration.queue.dto.ResultNotifyDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 处理结果通知
 * @date 2022/10/8
 */
@Component
@Slf4j
public class HandlerNotifyRepositoryImpl extends IoniaRocketMqTemplate implements MqResultNotifyRepository {

    @Resource
    private NacosGlobalConfigProperties globalConfigProperties;

    private final Integer delayEventPushLevel = 9;

    @Override
    public void handlerResultNotify(ResultNotifyDTO refundNotify) {
        try {
            SendResult sendResult = this.getTemplate().syncSend(this.buildDestination(globalConfigProperties.getNotifyTopic(), refundNotify.getMessageBody().getTag()), refundNotify);
            log.info("handlerResultNotify request:[{}] msgId: [{}]", JSONObject.toJSONString(refundNotify), sendResult.getMsgId());
        } catch (Exception e) {
            log.error("handlerResultNotify operation base system exception:", e);
        }
    }

    @Override
    public void delayHandlerNotify(String correctionNo, Integer retryNum) {
        //超过最大次数或者为前端发起，则不扔入延迟队列
        if (Objects.isNull(retryNum) || retryNum < CorrectionConstant.NUM_ZERO) {
            return;
        }
        if (retryNum > CorrectionConstant.NUM_TEN) {
            log.info("delayHandlerNotify,correctionNo : [{}], retryNum : [{}]", correctionNo, retryNum);
            return;
        }
        try {
            CorrectionDelayHandlerMessage correctionDelayHandlerMessage = new CorrectionDelayHandlerMessage(correctionNo, retryNum + 1);
            correctionDelayHandlerMessage.setSource(CorrectionConstant.CORRECTION_CENTER);
            SendResult sendResult = this.sendMsgSyncDelay(globalConfigProperties.getDelayHandlerTopic(), correctionDelayHandlerMessage, globalConfigProperties.getDelayLevel());
            log.info("delayHandlerNotify,msgId : [{}]", sendResult.getMsgId());
        } catch (Exception e) {
            log.error("delayHandlerNotify operation base system exception:", e);
        }
    }

    @Override
    public void delayPushEvent(CorrectionEventMessageInfo eventMessageInfo) {
        try {
            eventMessageInfo.getMessageBody().setRetryNum(eventMessageInfo.getMessageBody().getRetryNum() + 1);
            eventMessageInfo.setSource(CorrectionConstant.CORRECTION_CENTER);
            SendResult sendResult = this.sendMsgSyncDelay(globalConfigProperties.getDelayEventPush(), eventMessageInfo, delayEventPushLevel);
            log.info("delayPushEvent,msgId : [{}]", sendResult.getMsgId());
        } catch (Exception e) {
            log.error("add correctionEvent exception:,eventInfo :{} ,error", JSONObject.toJSONString(eventMessageInfo), e);
        }

    }


    @Override
    public void delayPushExternalResultEvent(ExternalResultInfoMessageInfo msg) {
        int retryNum = ObjectUtils.defaultIfNull(msg.getMessageBody().getRetryNum(), CorrectionConstant.NUM_ZERO) + 1;
        if (retryNum > CorrectionConstant.NUM_FIVE) {
            log.warn("ExternalResultNotifyConsumerImpl handleMessage exception msgInfo:{}", JSONObject.toJSONString(msg));
            return;
        }
        msg.getMessageBody().setRetryNum(retryNum);
        msg.setSource(CorrectionConstant.CORRECTION_CENTER);
        try {
            SendResult sendResult = this.sendMsgSyncDelay(globalConfigProperties.getDelayHandlerTopic(), msg, globalConfigProperties.getDelayLevel());
            log.info("delayPushExternalResultEvent,msgId : [{}]", sendResult.getMsgId());
        } catch (Exception e) {
            log.error("delayPushExternalResultEvent operation base system exception:", e);
        }
    }

    private String buildDestination(String notifyTopic, String tag) {
        return notifyTopic + Symbols.COLON + tag;
    }
}
