package com.payermax.operating.correction.integration.rpc.asset.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.integration.config.nacos.repository.ConfigRepository;
import com.payermax.operating.correction.integration.dto.CorrectionPayoutsInfo;
import com.payermax.operating.correction.integration.rpc.asset.assembler.AssetAssembler;
import com.payermax.operating.correction.integration.rpc.asset.dto.AssetNotifyInfo;
import com.payermax.operating.correction.integration.rpc.asset.repository.AssetRepository;
import com.payermax.payment.assetsx.correction.facade.CorrectionFacade;
import com.payermax.payment.assetsx.service.response.PayResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 资产交换资源实现类
 * @date 2022/12/13
 */
@Repository
@Slf4j
public class AssetRepositoryImpl implements AssetRepository {

    @DubboReference(retries = 2, timeout = 5000,version = "1.0")
    private CorrectionFacade payoutsPaymentFacade;

    @Resource
    private AssetAssembler assembler;

    @Resource
    private ConfigRepository configRepository;

    @Override
    public BaseResProcess correctionPayouts(CorrectionPayoutsInfo payoutsInfo) {
        String channelPayMethod = configRepository.getPayMethod(payoutsInfo.getPayPaymentInstanceInfo().getPaymentInstanceDTO().getPaymentMethodNo());
        Result<PayResp> result = payoutsPaymentFacade.correctionPayouts(assembler.toCorrectionPayouts(payoutsInfo,channelPayMethod));
        log.info("correctionPayouts,payoutsInfo : [{}]",JSONObject.toJSONString(result));
        AssertUtil.isTrue(ResultUtil.isApplySuccess(result), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_ASSET_EXCHANGE_EXCEPTION);
        return new BaseResProcess(new VoucherInfo(payoutsInfo.getPayoutNo(), DCVoucherType.ASSET_PAY_ORDER), JSONObject.toJSONString(result.getCode()));
    }

    @Override
    public BaseResProcess correctionRepeatPay(AssetNotifyInfo assetNotifyInfo) {
        Result<PayResp> payRespResult = payoutsPaymentFacade.fixRepeatPay(assembler.toTradeOperationReq(assetNotifyInfo));
        log.info("correctionRepeatPay,fixRepeatPay : [{}]",JSONObject.toJSONString(payRespResult));
        AssertUtil.isTrue(Objects.nonNull(payRespResult) && payRespResult.isSuccess(), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_ASSET_EXCEPTION);
        return new BaseResProcess(new VoucherInfo(assetNotifyInfo.getPaymentOrderNo(), DCVoucherType.ASSET_PAY_ORDER),JSONObject.toJSONString(payRespResult.getCode()));
    }
}
