package com.payermax.operating.correction.integration.persistence.rdms.dto;

import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.dto.ValidationConditionInfo;
import com.payermax.operating.correction.core.common.enums.TradeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 差错原因基础信息
 * @date 2022/9/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CorrectionBasicInfoDTO {

    /**
     * 差错code
     */
    private String correctionCode;

    /**
     * 交易类型
     */
    private TradeType tradeType;

    /**
     * 差错code名
     */
    private String correctionName;

    /**
     * 描述
     */
    private String desc;

    /**
     * 执行规则
     */
    private ValidationRuleConditionDTO execValidateRules;

    /**
     * 操作基础信息
     */
    private OperationBasicInfo opBasicInfo;

    /**
     * 策略Code列表
     */
    private List<String> strategyCodeList;

    /**
     * 父差错code
     */
    private String parentCorrectionCode;


    public boolean validationRule(ValidationConditionInfo conditionInfo) {
        //校验规则为空，则试为无校验规则
        if (Objects.isNull(execValidateRules)) {
            return Boolean.TRUE;
        }
        return execValidateRules.isMatch(conditionInfo);
    }

}
