package com.payermax.operating.correction.integration.rpc.ordercenter.assembler;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderRequest;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderResponse;
import com.payermax.order.dto.request.*;
import com.payermax.order.dto.response.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Optional;

/**
 * The interface Order center assembler.
 *
 * <AUTHOR>
 * @desc 订单中心转换类
 * @date 2022 /10/10
 */
@Mapper(componentModel = "spring", imports = {Optional.class, Nullable.class, PatchPaymentResponse.class, DCVoucherType.class
        , PatchTradeOrderResponse.class, PatchTradeOrderAndPaymentResponse.class})
public interface OrderCenterAssembler {
    /**
     * To patch trade order patch trade order request.
     *
     * @param orderRequest the order request
     * @return the patch trade order request
     */
    @Mappings({
            @Mapping(target = "originOutTradeNo", source = "originalOrderInfo.outTradeNo"),
            @Mapping(target = "merchantNo", source = "originalOrderInfo.merchantNo"),
            @Mapping(target = "merchantAppId", source = "originalOrderInfo.merchantAppId"),
            @Mapping(target = "originPayRequestNo", source = "originalOrderInfo.payRequestNo"),
    })
    PatchTradeOrderRequest toPatchTradeOrder(TradeOrderPatchOrderRequest orderRequest);

    /**
     * To patch pay order patch pay request.
     *
     * @param orderRequest the order request
     * @return the patch pay request
     */
    @Mappings({
            @Mapping(target = "originPayRequestNo", source = "originalOrderInfo.payRequestNo"),
    })
    PatchPayRequest toPatchPayOrder(TradeOrderPatchOrderRequest orderRequest);

    /**
     * To patch trade pay order patch trade and pay order request.
     *
     * @param orderRequest the order request
     * @return the patch trade and pay order request
     */
    @Mappings({
            @Mapping(target = "originOutTradeNo", source = "originalOrderInfo.outTradeNo"),
            @Mapping(target = "merchantNo", source = "originalOrderInfo.merchantNo"),
            @Mapping(target = "merchantAppId", source = "originalOrderInfo.merchantAppId"),
            @Mapping(target = "originPayRequestNo", source = "originalOrderInfo.payRequestNo"),
    })
    PatchTradeAndPayOrderRequest toPatchTradePayOrder(TradeOrderPatchOrderRequest orderRequest);


    /**
     * To patch order response trade order patch order response.
     *
     * @param result   the result
     * @param response the response
     * @return the trade order patch order response
     */
    @Mappings({
            @Mapping(target = "code", source = "result.code"),
            @Mapping(target = "msg", source = "result.msg"),
            @Mapping(target = "voucherNo", expression = "java(Optional.ofNullable(response).map(PatchPaymentResponse::getPayRequestNo).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "voucherType", expression = "java(DCVoucherType.PAY_REQUEST_NO)"),
    })
    TradeOrderPatchOrderResponse toPatchOrderResponse(Result<PatchPaymentResponse> result, PatchPaymentResponse response);

    /**
     * To patch order response trade order patch order response.
     *
     * @param result   the result
     * @param response the response
     * @return the trade order patch order response
     */
    @Mappings({
            @Mapping(target = "code", source = "result.code"),
            @Mapping(target = "msg", source = "result.msg"),
            @Mapping(target = "voucherNo", expression = "java(Optional.ofNullable(response).map(PatchTradeOrderResponse::getTradeOrderNo).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "voucherType", expression = "java(DCVoucherType.R_TRADE_ORDER_NO)"),
    })
    TradeOrderPatchOrderResponse toPatchOrderResponse(Result<PatchTradeOrderResponse> result, PatchTradeOrderResponse response);

    /**
     * To patch order response trade order patch order response.
     *
     * @param result   the result
     * @param response the data
     * @return the trade order patch order response
     */
    @Mappings({
            @Mapping(target = "code", source = "result.code"),
            @Mapping(target = "msg", source = "result.msg"),
            @Mapping(target = "voucherNo", expression = "java(Optional.ofNullable(response).map(PatchTradeOrderAndPaymentResponse::getTradeOrderNo).orElse(Nullable.getNullVal()))"),
            @Mapping(target = "voucherType", expression = "java(DCVoucherType.R_TRADE_ORDER_NO)"),
    })
    TradeOrderPatchOrderResponse toPatchOrderResponse(Result<PatchTradeOrderAndPaymentResponse> result, PatchTradeOrderAndPaymentResponse response);

    /**
     * To repeat trade patch repeat trade and pay request.
     *
     * @param patchOrderRequest the patch order request
     * @return the patch repeat trade and pay request
     */
    @Mappings({
            @Mapping(target = "merchantNo", source = "originalOrderInfo.merchantNo"),
            @Mapping(target = "originPayRequestNo", source = "originalOrderInfo.payRequestNo"),
            @Mapping(target = "originTradeOrderNo", source = "originalOrderInfo.tradeOrderNo"),
            @Mapping(target = "correctOutTradeNo", source = "patchOrderInfo.outTradeNo"),
            @Mapping(target = "correctTradeOrderNo", source = "patchOrderInfo.tradeOrderNo"),
            @Mapping(target = "correctPayRequestNo", source = "patchOrderInfo.payRequestNo"),
            @Mapping(target = "paymentType", expression = "java(patchOrderRequest.getPatchType().getType())"),
            @Mapping(target = "duplicateSuffix", source = "correctionSuffix"),
    })
    PatchRepeatTradeAndPayRequest toRepeatTrade(TradeOrderPatchOrderRequest patchOrderRequest);

    /**
     * 重复支付退款失败补交易入参转化
     * @param patchOrderRequest
     * @return
     */
    @Mappings({
            @Mapping(target = "payRequestNo", source = "originalOrderInfo.payRequestNo"),
            @Mapping(target = "tradeOrderNo", source = "originalOrderInfo.tradeOrderNo"),
            @Mapping(target = "correctSuffix", source = "correctionSuffix"),
    })
    PatchRepeatPayRequest toPatchRepeatPay(TradeOrderPatchOrderRequest patchOrderRequest);

    /**
     * To patch order response trade order patch order response.
     *
     * @param result         the result
     * @param res PatchRepeatTradeOrderAndPayResponse
     * @return the trade order patch order response
     */
    @Mappings({
            @Mapping(target = "code", source = "result.code"),
            @Mapping(target = "msg", source = "result.msg"),
            @Mapping(target = "voucherNo", source = "res.payRequestNo"),
            @Mapping(target = "voucherType", expression = "java(DCVoucherType.PAY_REQUEST_NO)"),
    })
    TradeOrderPatchOrderResponse toPatchOrderResponse(Result<PatchRepeatTradeOrderAndPayResponse> result, PatchRepeatTradeOrderAndPayResponse res);

    /**
     * To patch order response trade order patch order response.
     *
     * @param result         the result
     * @param res PatchRepeatTradeOrderAndPayResponse
     * @return the trade order patch order response
     */
    @Mappings({
            @Mapping(target = "code", source = "result.code"),
            @Mapping(target = "msg", source = "result.msg"),
            @Mapping(target = "voucherNo", source = "res.tradeOrderNo"),
            @Mapping(target = "voucherType", expression = "java(DCVoucherType.R_TRADE_ORDER_NO)"),
    })
    TradeOrderPatchOrderResponse toPatchTradeOrderResponse(Result<PatchRepeatTradeOrderAndPayResponse> result, PatchRepeatTradeOrderAndPayResponse res);

    @Mappings({
            @Mapping(target = "code", source = "result.code"),
            @Mapping(target = "msg", source = "result.msg"),
            @Mapping(target = "voucherNo", source = "res.tradeOrderNo"),
            @Mapping(target = "voucherType", expression = "java(DCVoucherType.R_TRADE_ORDER_NO)"),
    })
    TradeOrderPatchOrderResponse toPatchRepeatPayResponse(Result<PatchRepeatPayResponse> result, PatchRepeatPayResponse res);
}
