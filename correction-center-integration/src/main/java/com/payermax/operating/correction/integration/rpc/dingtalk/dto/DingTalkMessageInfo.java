package com.payermax.operating.correction.integration.rpc.dingtalk.dto;

import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 钉钉消息通知
 * @date 2023/9/25
 */
@Data
@NoArgsConstructor
public class DingTalkMessageInfo {

    @Getter
    private String msg;

    private String token;

    private String title;

    private List<String> atMobiles;

    private String color = "#000000";

    private Integer timeOut = 3000;

    public DingTalkMessageInfo(String msg, String token, String title, List<String> atMobiles) {
        this.msg = msg;
        this.token = token;
        this.title = title;
        this.atMobiles = atMobiles;
        this.color = "#000000";
        this.timeOut = 3000;
    }

    public void setMsg(String msg) {
        this.msg = CorrectionConstant.TRANSFER_ALERT_MSG_KEY +msg;
    }
}
