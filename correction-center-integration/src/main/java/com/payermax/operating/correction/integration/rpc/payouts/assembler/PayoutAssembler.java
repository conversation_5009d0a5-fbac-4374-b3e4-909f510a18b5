package com.payermax.operating.correction.integration.rpc.payouts.assembler;

import com.payermax.funds.order.disbursement.dto.request.PayoutRuleCheckRequest;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsCoreInfo;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsRuleInfo;
import com.payermax.operating.correction.integration.utils.IntegrationUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Optional;

/**
 * The interface Order center assembler.
 *
 * <AUTHOR>
 * @desc 出款服务转换类
 * @date 2022 /10/10
 */
@Mapper(componentModel = "spring", imports = {Optional.class, Nullable.class, IntegrationUtils.class})
public interface PayoutAssembler {

    /**
     * To rule check new com . payermax . funds . order . disbursement . dto . request . payout rule check request.
     *
     * @param ruleInfo the rule info
     * @param coreInfo the core info
     * @return the com . payermax . funds . order . disbursement . dto . request . payout rule check request
     */
    @Mappings({
            @Mapping(target = "expiryDays", source = "ruleInfo.expiryDays"),
            @Mapping(target = "country", source = "coreInfo.cashierProductInfo.country"),
            @Mapping(target = "cashierProductNo", source = "coreInfo.cashierProductInfo.cashierProductNo"),
            @Mapping(target = "remark", source = "ruleInfo.transactionNote"),
            @Mapping(target = "outTradeNo", source = "coreInfo.correctionNo"),
            @Mapping(target = "payeeInfo", expression = "java(com.payermax.operating.correction.integration.utils.IntegrationUtils.getPayoutsPayeeInfoNew(ruleInfo,coreInfo.getCashierProductInfo()))"),
            @Mapping(target = "trade", expression = "java(IntegrationUtils.getPayoutsTradeNew(coreInfo.getPayOutsMoney()))"),

    })
    PayoutRuleCheckRequest toRuleCheck(PayoutsRuleInfo ruleInfo, PayoutsCoreInfo coreInfo);
}

