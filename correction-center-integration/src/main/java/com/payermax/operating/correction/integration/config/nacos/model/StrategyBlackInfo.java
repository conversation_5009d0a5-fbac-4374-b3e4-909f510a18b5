package com.payermax.operating.correction.integration.config.nacos.model;

import lombok.Data;

import java.util.List;

/**
 * @description: 策略黑名单配置
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-06-05 17:38
 **/
@Data
public class StrategyBlackInfo {
    /**
     * 需要拦截的策略code
     */
    private String strategyCode;
    /**
     * 拦截策略code需要的条件
     * 支持配置多个，命中任意一个条件都需要拦截
     */
    List<Expression> exps;
}
