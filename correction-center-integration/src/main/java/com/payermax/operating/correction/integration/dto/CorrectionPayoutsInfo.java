package com.payermax.operating.correction.integration.dto;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.integration.rpc.cashiercore.dto.CashierCorePaymentInstanceInfo;
import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc 差错出款信息
 * @date 2022/12/13
 */
@Getter
@Builder
public class CorrectionPayoutsInfo {

    private Money payAmount;

    private String payoutNo;

    private String bizIdentify;

    private String merchantNo;

    private PayPaymentInstanceInfoDTO payPaymentInstanceInfo;


}
