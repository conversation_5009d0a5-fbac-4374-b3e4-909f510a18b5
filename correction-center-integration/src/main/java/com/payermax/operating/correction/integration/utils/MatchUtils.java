package com.payermax.operating.correction.integration.utils;

import com.payermax.operating.correction.core.common.enums.ConditionEnum;

/**
 * <AUTHOR>
 * @desc 匹配工具类
 * @date 2023/4/20
 */
public class MatchUtils {

    public static boolean conditionRule(ConditionEnum condition, String conditionVal,String actualVal) {
        switch (condition) {
            case EQUAL:
                return conditionVal.equals(actualVal);
            case CONTAIN:
                return conditionVal.contains(actualVal);
            case NOT_CONTAIN:
                return !conditionVal.contains(actualVal);
            case ALL:
                return Boolean.TRUE;
            default:
                return Boolean.FALSE;
        }
    }
}
