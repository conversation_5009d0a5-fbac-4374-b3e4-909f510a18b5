package com.payermax.operating.correction.integration.persistence.rdms.dto;

import com.payermax.common.lang.model.dto.request.PageRequest;
import com.payermax.operating.correction.core.common.dto.CorrectionBaseInfo;
import com.payermax.operating.correction.core.common.enums.TradeType;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 差错订单分页查询
 * @date 2023/2/22
 */
@Builder
@Getter
@Setter
@ToString
public class QueryCorrectionOrderPageDTO {

    /**
     * 翻页信息
     */
    private PageRequest page;

    /**
     * 差错基础信息
     */
    private CorrectionBaseInfo baseInfo;

    /**
     * 运营选择差错原因
     */
    private String operationCorrectionCode;

    /**
     * 差错状态
     */
    private List<String> status;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 商户订单号
     */
    private String merchantOrderNo;

    /**
     * 来源系统
     */
    private List<String> sysSource;

    /**
     * 复核人
     */
    private String reviewer;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 交易类型
     */
    private TradeType tradeType;

    /**
     * 冗余信息
     */
    private ReconcileRedundantDTO redundantInfo;

}
