package com.payermax.operating.correction.integration.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 支付支付实例信息
 * @date 2022/12/14
 */
@Data
@NoArgsConstructor
public class PayPaymentInstanceInfoDTO {


    /**
     * 出款10，收单20
     */
    private String paymentType;

    /**
     * 国家
     */
    private String country;

    /**
     * 支付实例信息
     */
    private PaymentInstanceDTO paymentInstanceDTO;

    public PayPaymentInstanceInfoDTO(String country, String paymentMethodNo, String targetOrg) {
        this.country = country;
        this.paymentInstanceDTO = new PaymentInstanceDTO(paymentMethodNo);
        this.paymentInstanceDTO.setTargetOrg(targetOrg);
    }
}
