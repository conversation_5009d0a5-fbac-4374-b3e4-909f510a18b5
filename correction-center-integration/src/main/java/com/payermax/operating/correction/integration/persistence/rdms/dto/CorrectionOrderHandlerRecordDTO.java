package com.payermax.operating.correction.integration.persistence.rdms.dto;

import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 差错订单操作记录
 * @date 2022/10/10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CorrectionOrderHandlerRecordDTO {

    private CorrectionHandlerUniInfo handlerInfo;

    private CommonStatusEnum status;

    private String processResult;

    private OperationBasicInfo opBasicInfo;
}
