package com.payermax.operating.correction.integration.rpc.risk.repository;

import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsFiledInfo;
import com.payermax.operating.correction.integration.rpc.risk.dto.RiskEventInfo;

import java.util.List;

/**
 * The interface Order center repository.
 *
 * <AUTHOR>
 * @desc 出款服务资源类
 * @date 2022 /10/10
 */
public interface RiskRepository {

    /**
     * Risk event report list.
     *
     * @param riskEventInfo the risk event info
     */
    Boolean riskEventReport(RiskEventInfo riskEventInfo);
}
