package com.payermax.operating.correction.integration.rpc.datacenter.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.integration.utils.ListSizeUtils;
import lombok.*;

import javax.annotation.Nullable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 数据中心返回信息
 * @date 2022/10/17
 */
@Data
@NoArgsConstructor
public class DataCenterResponse {

    @JSONField(name = "funds_r_trade_order_no")
    private List<DataCenterOrderInfoDTO> tradeOrders;

    @JSONField(name = "funds_d_payment_trade_no")
    private List<DataCenterPayoutsInfoDTO> payoutsOrders;

    @JSONField(name = "funds_r_pay_request_no")
    private List<DataCenterPayRequestInfoDTO> payRequests;

    @JSONField(name = "funds_r_refund_trade_no")
    private List<DataCenterRefundInfoDTO> refundInfos;

    @JSONField(name = "funds_r_pay_order_no")
    private List<DataCenterAssetInfoDTO> assetInfos;

    @JSONField(name = "funds_r_channel_pay_request_no")
    private List<DataCenterChannelInfoDTO> channelInfo;

    @JSONField(name = "funds_r_original_trade_order_no")
    private List<DataCenterOrderInfoDTO> oriTradeOrders;


    @Data
    @NoArgsConstructor
    public static class DataCenterOrderInfoDTO {

        @JSONField(name = "txnOrdMerchantNo")
        private String merchantNo;

        @JSONField(name = "txnOrdOutTradeNo")
        private String outOrderNo;

        @JSONField(name = "txnOrdTradeOrderNo")
        private String tradeOrderNo;

        @JSONField(name = "txnOrdMerchantAppId")
        private String merchantAppId;

        @JSONField(name = "txnOrdBizIdentify")
        private String bizIdentify;

        @JSONField(name = "txnOrdStatus")
        private String status;

        @JSONField(name = "txnOrdPayFinishTime")
        private String payFinishTime;
        @JSONField(name = "txnOrdProductCode")
        private String productCode;

        @JSONField(name = "txnOrdExtendFieldObject")
        private OrderExtendField orderExtendField;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class OrderExtendField {
        /**
         * 差错原单单号（只有当前订单是一笔通过订单中心补交易产生的新交易单，才会该字段）（不应该带有任何:C信息）
         */
        private String correctionOriginOrderNo;
        /**
         * 差错原单（只有当前订单是一笔通过订单中心补交易产生的新交易单,才会记录原订单的信息）
         */
        private CorrectionOriTradeOrder correctionOriTradeOrder;
    }
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static  class CorrectionOriTradeOrder{
        /**
         * 商户订单号
         */
        private String outTradeNo;
    }

    @Data
    @NoArgsConstructor
    public static class DataCenterPayoutsInfoDTO {

        @JSONField(name = "txnMerchantId")
        private String merchantNo;

        @JSONField(name = "txnOrderId")
        private String outOrderNo;

        @JSONField(name = "txnTradeNo")
        private String tradeOrderNo;

        @JSONField(name = "txnBizIdentify")
        private String bizIdentify;

        @JSONField(name = "txnStatus")
        private String status;

        @JSONField(name = "txnBizNewProductCode")
        private String productCode;

        @JSONField(name = "txnReserved2")
        private DataCenterPayOutExtendFieldInfoDTO dataCenterPayOutExtendFieldInfoDTO;
    }
    @Data
    @NoArgsConstructor
    @ToString
    public static class DataCenterPayOutExtendFieldInfoDTO {
        /**
         * 支付完成时间
         */
        private String payFinishTime;
    }

    @Data
    @NoArgsConstructor
    @ToString
    public static class DataCenterRefundInfoDTO {

        @JSONField(name = "refundTradeNo")
        private String refundNo;

        private String status;

        private String refundType;

        private String currency;

        private String amount;

        private String createTime;
        @JSONField(name = "extendField")
        private String extendField;

    }


    @Data
    @NoArgsConstructor
    public static class DataCenterPayRequestInfoDTO {

        @JSONField(name = "payReqPayRequestNo")
        private String payRequestNo;

        @JSONField(name = "payReqStatus")
        private String status;
        @JSONField(name = "payReqProductCode")
        private String productCode;
        @JSONField(name = "payReqExtendField")
        private String extendField;
        @JSONField(name ="payReqPaymentModeToolList")
        private List<DataCenterPaymentModeToolInfo> paymentModeToolInfoList;

        /**
         * 营销在支付方式维度, 故逻辑判断就在支付单
         * @return 是否有营销
         */
        public boolean haveDiscount() {
            if (paymentModeToolInfoList == null) {
                return false;
            }
            return paymentModeToolInfoList
                    .stream()
                    .filter(Objects::nonNull).anyMatch(x -> Objects.equals(CorrectionConstant.PAYMENT_METHOD_DISCOUNT, x.getPayTooPaymentMode()));
        }

        /**
         * 排除营销后的支付方式
         *
         * @return
         */
        public @Nullable DataCenterPaymentModeToolInfo getPaymentModeTool() {
            if (paymentModeToolInfoList == null) {
                return null;
            }

            List<DataCenterPaymentModeToolInfo> nonDiscount = paymentModeToolInfoList.stream()
                    .filter(Objects::nonNull)
                    .filter(x -> !Objects.equals(CorrectionConstant.PAYMENT_METHOD_DISCOUNT,
                            x.getPayTooPaymentMode()))
                    .collect(Collectors.toList());

            if (nonDiscount.size() != 1) {
                ListSizeUtils.log(nonDiscount, "支付方式不是1个");
                return null;
            }

            return nonDiscount.stream()
                    .findFirst()
                    .orElse(null); // 安全返回 null
        }
    }

    /**
     * 支付工具拆分表信息（订单中心）
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DataCenterPaymentModeToolInfo {
        /**
         * 支付方式
         */
        private String payTooPaymentMode;
        /**
         * 目标机构
         */
        private String payTooTargetOrg;
    }


    @Data
    @NoArgsConstructor
    public static class DataCenterAssetInfoDTO {

        private String payOrderNo;

        private String status;

        private String payType;

        private String currency;

        private String amount;

        private String productCode;

    }

    @Data
    @NoArgsConstructor
    public static class DataCenterChannelInfoDTO {

        @JSONField(name = "chlReqChannelPayRequestNo")
        private String channelRequestNo;

        private String chlReqStatus;

        @JSONField(name = "chlReqPaymentChannelCommitOrderObject")
        private List<DataCenterChannelCommitInfoDTO> channelCommits;

        @JSONField(name = "chlReqCompleteTime")
        private String completeTime;

        @JSONField(name = "chlReqMappingCode")
        private String respCode;

        @JSONField(name = "chlReqMappingMsg")
        private String respMsg;

        @JSONField(name = "chlReqCreateTime",format = "yyyy-MM-dd HH:mm:ss")
        private Date createTime;

        @JSONField(name = "chlReqCurrency")
        private String reqCurrency;

        @JSONField(name = "chlReqAmount")
        private String reqAmount;


    }

    @Data
    @NoArgsConstructor
    public static class DataCenterChannelCommitInfoDTO {

        @JSONField(name = "chlCmtChannelPayCommitNo")
        private String channelCommitNo;

        @JSONField(name = "chlCmtStatus")
        private String status;

        @JSONField(name = "chlCmtMainRequestType")
        private String type;

        @JSONField(name = "chlCmtChannelCode")
        private String channelCode;

        @JSONField(name = "chlCmtThirdOrgOrderNo")
        private String thirdOrderNo;

        @JSONField(name = "chlCmtAmount")
        private String payAmount;

        @JSONField(name = "chlCmtCurrency")
        private String payCurrency;
        @JSONField(name = "chlCmtCompleteTime")
        private String commitCompleteTime;

    }

}
