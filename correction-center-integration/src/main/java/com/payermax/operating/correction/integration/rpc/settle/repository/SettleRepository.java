package com.payermax.operating.correction.integration.rpc.settle.repository;

import com.payermax.operating.correction.core.common.dto.BaseResProcess;

/**
 * The interface Settle repository.
 *
 * <AUTHOR>
 * @desc 结算资源类
 * @date 2023 /4/17
 */
public interface SettleRepository {

    /**
     * Patch trade clearing base res process.
     *
     * @param correctionNo  the correction no
     * @param refundTradeNo the refund trade no
     * @return the base res process
     */
    BaseResProcess patchTradeClearing(String correctionNo, String refundTradeNo);
}
