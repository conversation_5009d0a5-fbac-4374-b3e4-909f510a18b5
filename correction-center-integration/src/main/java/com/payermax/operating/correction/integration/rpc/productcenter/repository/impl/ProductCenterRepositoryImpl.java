package com.payermax.operating.correction.integration.rpc.productcenter.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.dto.PaymentInstanceDTO;
import com.payermax.operating.correction.integration.rpc.cashiercore.dto.CashierCorePaymentInstanceInfo;
import com.payermax.operating.correction.integration.rpc.productcenter.dto.CashierProductInfo;
import com.payermax.operating.correction.integration.rpc.productcenter.repository.ProductCenterRepository;
import com.payermax.sales.productsolution.facade.api.SolutionCashierProductFacade;
import com.payermax.sales.productsolution.facade.dto.CashierProductConfigDTO;
import com.payermax.sales.productsolution.facade.request.cashierPorduct.ExchangeCashierProductRequest;
import com.payermax.sales.productsolution.facade.response.cashierProduct.ExchangeCashierProductResp;
import com.ushareit.fintech.product.productcentre.CashierProductFacade;
import com.ushareit.fintech.product.productcentre.dto.request.ExchangeCashierProductNoRequest;
import com.ushareit.fintech.product.productcentre.dto.response.ExchangeCashierProductNoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 产品中心资源实现
 * @date 2022/12/8
 */
@Repository
@Slf4j
public class ProductCenterRepositoryImpl implements ProductCenterRepository {

    @DubboReference(version = "1.0", timeout = 5000)
    private CashierProductFacade cashierProductFacade;

    @DubboReference(version = "1.0", timeout = 5000)
    private SolutionCashierProductFacade solutionCashierProductFacade;

    private final String CUSTOMER_TYPE_IGNORE="toB";
    private final String CUSTOMER_TYPE="CUSTOMER_TYPE";
    @Override
    public List<CashierProductInfo> getCashierProduct(CashierCorePaymentInstanceInfo cashierCorePaymentInstanceInfo) {
        ExchangeCashierProductNoRequest productNoRequest = new ExchangeCashierProductNoRequest();
        productNoRequest.setQueryItems(cashierCorePaymentInstanceInfo.getPaymentInstanceList().stream().map(e -> this.buildExchangeCashierProduct(e, cashierCorePaymentInstanceInfo.getPaymentType(), cashierCorePaymentInstanceInfo.getCountry(),e.getTargetOrg())).collect(Collectors.toList()));
        Result<List<ExchangeCashierProductNoResponse>> ret = cashierProductFacade.queryCashierProductListByPaymentType(productNoRequest);
        AssertUtil.isTrue(ResultUtil.isApplySuccess(ret) && CollectionUtils.isNotEmpty(ret.getData()), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_PRODUCT_CENTER_EXCEPTION);
        return ret.getData().stream().map(e -> this.convertCashierProductInfo(e, cashierCorePaymentInstanceInfo.getPaymentType(), cashierCorePaymentInstanceInfo.getCountry()))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public List<CashierProductInfo> getCashierProductV2(CashierCorePaymentInstanceInfo cashierCorePaymentInstanceInfoRequest) {
        ExchangeCashierProductRequest exchangeCashierProductRequest = new ExchangeCashierProductRequest();
        exchangeCashierProductRequest.setQueryItems(cashierCorePaymentInstanceInfoRequest.getPaymentInstanceList().stream().map(e -> this.buildExchangeCashierProductV2(e, cashierCorePaymentInstanceInfoRequest.getPaymentType(), cashierCorePaymentInstanceInfoRequest.getCountry(),e.getTargetOrg())).collect(Collectors.toList()));
        Result<List<ExchangeCashierProductResp>> ret = solutionCashierProductFacade.queryCashierProductListByPaymentType(exchangeCashierProductRequest);
        AssertUtil.isTrue(ResultUtil.isApplySuccess(ret) && CollectionUtils.isNotEmpty(ret.getData()), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_PRODUCT_CENTER_EXCEPTION);
        return ret.getData().stream().map(e -> this.convertCashierProductInfoV2(e, cashierCorePaymentInstanceInfoRequest.getPaymentType(), cashierCorePaymentInstanceInfoRequest.getCountry()))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private ExchangeCashierProductRequest.QueryItem buildExchangeCashierProductV2(PaymentInstanceDTO paymentInstance, String paymentType, String country,String targetOrg) {
        ExchangeCashierProductRequest.QueryItem productNoParamItem = new ExchangeCashierProductRequest.QueryItem();
        productNoParamItem.setCountryCode(country);
        productNoParamItem.setPaymentMethod(paymentInstance.getPaymentMethodNo());
        productNoParamItem.setPaymentType(paymentType);
        productNoParamItem.setTargetInstit(targetOrg);
        return productNoParamItem;
    }

    private ExchangeCashierProductNoRequest.ExchangeCashierProductNoParamItem buildExchangeCashierProduct(PaymentInstanceDTO paymentInstance, String paymentType, String country,String targetOrg) {
        ExchangeCashierProductNoRequest.ExchangeCashierProductNoParamItem productNoParamItem = new ExchangeCashierProductNoRequest.ExchangeCashierProductNoParamItem();
        productNoParamItem.setCountry(country);
        productNoParamItem.setPaymentMethodNo(paymentInstance.getPaymentMethodNo());
        productNoParamItem.setPaymentType(paymentType);
        productNoParamItem.setTargetOrgNo(targetOrg);
        return productNoParamItem;
    }

    private CashierProductInfo convertCashierProductInfo(ExchangeCashierProductNoResponse productNoResponse, String paymentType, String country) {
        if (StringUtils.equalsIgnoreCase(productNoResponse.getCustomerType(),CUSTOMER_TYPE_IGNORE)){
            return Nullable.getNullVal();
        }
        CashierProductInfo cashierProductInfo = new CashierProductInfo();
        cashierProductInfo.setCashierProductNo(productNoResponse.getCashierProductNo());
        cashierProductInfo.setPaymentInstance(new PaymentInstanceDTO(productNoResponse.getPaymentMethodNo()));
        cashierProductInfo.setPaymentType(paymentType);
        cashierProductInfo.setCountry(country);
        return cashierProductInfo;
    }

    private CashierProductInfo convertCashierProductInfoV2(ExchangeCashierProductResp productNoResponse, String paymentType, String country) {
        if (CollectionUtils.isNotEmpty(productNoResponse.getCashierProductConfigs())) {
            String customerTypeValue = null;
            for (CashierProductConfigDTO cashierProductConfig : productNoResponse.getCashierProductConfigs()) {
                if (CUSTOMER_TYPE.equals(cashierProductConfig.getConfigKey())) {
                    customerTypeValue = cashierProductConfig.getConfigValue();
                    break;
                }
            }
            if (StringUtils.equalsIgnoreCase(customerTypeValue, CUSTOMER_TYPE_IGNORE)) {
                return Nullable.getNullVal();
            }
        }
        CashierProductInfo cashierProductInfo = new CashierProductInfo();
        cashierProductInfo.setCashierProductNo(productNoResponse.getCashierProductCode());
        cashierProductInfo.setPaymentInstance(new PaymentInstanceDTO(productNoResponse.getPaymentMethod()));
        cashierProductInfo.setPaymentType(paymentType);
        cashierProductInfo.setCountry(country);
        return cashierProductInfo;
    }
}
