package com.payermax.operating.correction.integration.config.nacos.model;

import com.payermax.operating.correction.core.common.enums.StrategyMode;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc 匹配规则
 * @date 2023/4/20
 */
@Data
public class StrategyRule {

    /**
     * 子策略匹配的字段（el表达式获取）
     */
    private String spel;

    /**
     * 策略方案
     */
    private StrategyMode strategyPlan;

    /**
     * key: 子策略匹配的字段所需的值
     * value ：策略表达式所需对象
     */
    private Map<String, List<StrategyExpressionInfo>> strategyExpressions;

    /**
     * 处理bean对象
     */
    private String beanInstance;
}
