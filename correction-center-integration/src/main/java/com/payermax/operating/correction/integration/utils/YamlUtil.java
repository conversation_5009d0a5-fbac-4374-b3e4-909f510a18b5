package com.payermax.operating.correction.integration.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import com.fasterxml.jackson.dataformat.yaml.YAMLGenerator;
import lombok.SneakyThrows;

import java.io.StringWriter;

/**
 * <AUTHOR>
 * @desc yaml工具类
 * @date 2022/12/12
 */
public class YamlUtil {

    /**
     * 将yaml字符串转成类对象
     *
     * @param yamlStr 字符串
     * @param reference 目标类
     * @param <T>     泛型
     * @return 目标类
     */
    @SneakyThrows
    public static <T> T toObject(String yamlStr, TypeReference<T> reference) {
        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        mapper.findAndRegisterModules();
        return mapper.readValue(yamlStr, reference);
    }

    /**
     * 将类对象转yaml字符串
     *
     * @param object 对象
     * @return yaml字符串
     */
    @SneakyThrows
    public static String toYaml(Object object) {
        ObjectMapper mapper = new ObjectMapper(new YAMLFactory());
        mapper.findAndRegisterModules();
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        mapper = new ObjectMapper(new YAMLFactory().disable(YAMLGenerator.Feature.WRITE_DOC_START_MARKER));
        StringWriter stringWriter = new StringWriter();
        mapper.writeValue(stringWriter, object);
        return stringWriter.toString();
    }
}
