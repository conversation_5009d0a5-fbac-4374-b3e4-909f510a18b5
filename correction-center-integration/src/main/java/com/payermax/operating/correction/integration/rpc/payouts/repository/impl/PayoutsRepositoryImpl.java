package com.payermax.operating.correction.integration.rpc.payouts.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.funds.order.disbursement.PayoutFacade;
import com.payermax.funds.order.disbursement.PayoutRuleFacade;
import com.payermax.funds.order.disbursement.dto.request.PayoutFiledRequest;
import com.payermax.funds.order.disbursement.dto.request.PayoutRuleCheckRequest;
import com.payermax.funds.order.disbursement.dto.request.TradeQueryRequest;
import com.payermax.funds.order.disbursement.dto.response.PayoutFiledResponse;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.integration.rpc.payouts.assembler.PayoutAssembler;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsCoreInfo;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsFiledInfo;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsRuleInfo;
import com.payermax.operating.correction.integration.rpc.payouts.repository.PayoutsRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 出款资源实现类
 * @date 2022/12/9
 */
@Repository
@Slf4j
public class PayoutsRepositoryImpl implements PayoutsRepository {

    @DubboReference(version = "1.0", timeout = 3000)
    private PayoutRuleFacade payoutRuleFacade;

    @DubboReference(version = "1.0", timeout = 3000)
    private PayoutFacade payoutFacade;

    @Resource
    private PayoutAssembler assembler;

    @Override
    public List<PayoutsFiledInfo> queryPayoutsFieldInfo(String country, String cashierProductNo) {

        PayoutFiledRequest filedRequest=new PayoutFiledRequest();
        filedRequest.setCountry(country);
        filedRequest.setCashierProductNo(cashierProductNo);
        filedRequest.setVersion(CorrectionConstant.RPC_VERSION);
        Result<PayoutFiledResponse> ret = payoutRuleFacade.fieldList(filedRequest);
        return ret.getData().getFiledList().stream().map(e -> new PayoutsFiledInfo(e.getFiledName(), e.getFiledMsg())).collect(Collectors.toList());
    }

    @Override
    public Boolean payoutsRuleCheck(PayoutsRuleInfo payoutsRuleInfo, PayoutsCoreInfo coreInfo) {
        PayoutRuleCheckRequest payoutRuleCheckRequest = assembler.toRuleCheck(payoutsRuleInfo, coreInfo);
        payoutRuleCheckRequest.setVersion(CorrectionConstant.RPC_VERSION);
        Result ret = payoutRuleFacade.ruleCheck(payoutRuleCheckRequest);
        log.info("payoutsRuleCheck,payoutsRuleInfo : [{}], coreInfo : [{}],ret:[{}]", JSONObject.toJSONString(payoutsRuleInfo), JSONObject.toJSONString(coreInfo), JSONObject.toJSONString(ret));
        return ResultUtil.isApplySuccess(ret);
    }

    @Override
    public String orderQueryStatus(String merchantNo, String outTradeNo) {
        TradeQueryRequest tradeQueryRequest = new TradeQueryRequest();
        tradeQueryRequest.setMerchantNo(merchantNo);
        tradeQueryRequest.setOutTradeNo(outTradeNo);
        Result<Map<String, Object>> result = payoutFacade.orderQuery(tradeQueryRequest);
        Map<String, Object> data = result.getData();

        return (String) data.get("status");
    }
}
