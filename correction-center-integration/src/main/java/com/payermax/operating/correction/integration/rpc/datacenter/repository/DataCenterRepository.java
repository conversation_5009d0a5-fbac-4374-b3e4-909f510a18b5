package com.payermax.operating.correction.integration.rpc.datacenter.repository;

import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;

import java.util.List;
import java.util.Map;

/**
 * The interface Data center query repository.
 *
 * <AUTHOR>
 * @desc 数据中心联合查询
 * @date 2022 /9/27
 */
public interface DataCenterRepository {

    /**
     * Dc global query global correction order info.
     *
     * @param voucherType the voucher type
     * @param orderId     the order id
     * @return the global correction order info
     */
    GlobalOrderInfo dcGlobalQuery(DCVoucherType voucherType, String orderId);

    /**
     * 查询doris数据
     *
     * @param templateId 模板id
     * @param params 参数
     * @return
     */
    List<Map<String, Object>> queryDoris(String templateId, Map<String, Object> params);
}
