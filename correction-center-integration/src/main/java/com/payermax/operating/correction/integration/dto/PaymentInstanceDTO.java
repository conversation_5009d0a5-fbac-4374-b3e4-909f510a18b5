package com.payermax.operating.correction.integration.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 支付实例对象
 * @date 2022/12/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentInstanceDTO {

    /**
     * BANK_TRANSFER
     */
    private String paymentMethodNo;

    /**
     * 业务身份
     */
    private String bizIdentify;

    /**
     * 目标机构
     */
    private String targetOrg;

    /**
     * 目标机构名称
     */
    private String targetOrgName;

    /**
     * 扩展信息
     */
    private List<ExtendProperty> extendPropertyList;

    public PaymentInstanceDTO(String paymentMethodNo) {
        this.paymentMethodNo = paymentMethodNo;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExtendProperty {

        private String key;

        private String value;

        private String logicKey;

    }
}
