package com.payermax.operating.correction.integration.rpc.merchant.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.merchant.omc.facade.MerchantQueryFacade;
import com.payermax.merchant.omc.facade.req.MerchantBaseReq;
import com.payermax.merchant.omc.facade.req.MerchantSupportArrayReq;
import com.payermax.merchant.omc.facade.resp.MerchantBaseResp;
import com.payermax.merchant.omc.facade.resp.MerchantSupportArrayResp;
import com.payermax.merchant.omc.facade.resp.MerchantSupportResp;
import com.payermax.operating.correction.integration.rpc.merchant.MerchantReository;
import com.payermax.operating.correction.integration.rpc.merchant.assembler.MerchantAssembler;
import com.payermax.operating.correction.integration.rpc.merchant.dto.MerchantBaseInfo;
import com.payermax.operating.correction.integration.rpc.merchant.dto.MerchantBaseInfoRequest;
import com.payermax.operating.correction.integration.rpc.merchant.dto.BatchQueryMerchantBaseInfoReps;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 商服服务接口实现类
 * @author: WangTao
 * @create: 2024-11-26 11:12
 **/
@Repository
public class MerchantRepositoryImpl implements MerchantReository {

    @DubboReference(timeout = 5000, version = "1.0")
    private MerchantQueryFacade merchantQueryFacade;

    @Resource
    private MerchantAssembler merchantAssembler;



    @Override
    public MerchantBaseInfo getMerchantBaseInfo(MerchantBaseInfoRequest request) {

        MerchantBaseReq merchantBaseReq = merchantAssembler.toMerchantBaseReq(request);

        Result<MerchantBaseResp> total = merchantQueryFacade.total(merchantBaseReq);

        return merchantAssembler.toMerchantInfo(total,total.getData());
    }

    @Override
    public BatchQueryMerchantBaseInfoReps batchQueryMerchantBaseInfo(List<String> merchantNoList) {
        BatchQueryMerchantBaseInfoReps batchQueryMerchantBaseInfoReps = new BatchQueryMerchantBaseInfoReps();

        if (CollectionUtil.isEmpty(merchantNoList)) {
            return batchQueryMerchantBaseInfoReps;
        }
        // 过滤"null" 返序列化可能会在"null", 故过滤
        merchantNoList = merchantNoList.stream().filter(x -> !"null".equals(x)).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(merchantNoList)) {
            return batchQueryMerchantBaseInfoReps;
        }

        List<String> listWithoutDuplicates = merchantNoList.stream()
                .distinct()
                .collect(Collectors.toList()); // 去重
        MerchantSupportArrayReq merchantSupportArrayReq = new MerchantSupportArrayReq();
        merchantSupportArrayReq.setMerchantNos(listWithoutDuplicates);
        Result<MerchantSupportArrayResp> merchantSupportArrayRespResult = merchantQueryFacade.merchantSupport(merchantSupportArrayReq);

        List<MerchantSupportResp> merchantSupportResps = merchantSupportArrayRespResult.getData().getMerchantSupportResps();

        if (merchantSupportResps == null) {
           return batchQueryMerchantBaseInfoReps;
        }

        batchQueryMerchantBaseInfoReps.setMerchantBaseInfos(
                merchantSupportResps.stream().map(merchantSupportResp -> MerchantBaseInfo.builder()
                        .bizType(merchantSupportResp.getMerchantBase().getBizType())
                        .merchantNo(merchantSupportResp.getMerchantBase().getMerchantNo()) // 用于批量匹配
                        .innerName(merchantSupportResp.getMerchantBase().getInnerName())
                        .merchantNo(merchantSupportResp.getMerchantBase().getMerchantNo())
                        .build()).collect(Collectors.toList()));
        return batchQueryMerchantBaseInfoReps;
    }
}
