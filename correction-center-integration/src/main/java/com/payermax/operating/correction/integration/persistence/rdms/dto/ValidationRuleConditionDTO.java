package com.payermax.operating.correction.integration.persistence.rdms.dto;

import com.payermax.operating.correction.core.common.dto.ConditionInfo;
import com.payermax.operating.correction.core.common.dto.ValidationConditionInfo;
import com.payermax.operating.correction.integration.utils.MatchUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 差错原因执行规则
 * @date 2022/9/20
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ValidationRuleConditionDTO {

    private ConditionInfo channelCodeCon;

    private ConditionInfo merchantNoCon;

    private ConditionInfo productCodeCon;


    /**
     * @param validationCon
     * @desc 匹配规则
     * <AUTHOR>
     */
    public boolean isMatch(ValidationConditionInfo validationCon) {
        return match(channelCodeCon, validationCon.getChannelCode())
                && match(merchantNoCon, validationCon.getMerchantNo())
                && match(productCodeCon, validationCon.getProductCode());
    }

    private boolean match(ConditionInfo condition, String value) {
        if (Objects.nonNull(condition) && Objects.nonNull(condition.getCondition())) {
            return MatchUtils.conditionRule(condition.getCondition(), condition.getValue(), value);
        }
        return Boolean.TRUE;
    }

}
