package com.payermax.operating.correction.integration.rpc.merchant.assembler;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.merchant.omc.facade.req.MerchantBaseReq;
import com.payermax.merchant.omc.facade.resp.MerchantBaseResp;
import com.payermax.merchant.omc.facade.resp.item.merchant.MerchantBase;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.rpc.merchant.dto.MerchantBaseInfo;
import com.payermax.operating.correction.integration.rpc.merchant.dto.MerchantBaseInfoRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Optional;

/**
 * The interface Order center assembler.
 *
 * <AUTHOR>
 * @desc 出款服务转换类
 * @date 2022 /10/10
 */
@Mapper(componentModel = "spring", imports = {Optional.class, Nullable.class, CorrectionConstant.class, MerchantBase.class})
public interface MerchantAssembler {

    @Mappings({
            @Mapping(target = "typeList", expression = "java(java.util.Collections.singletonList(CorrectionConstant.MERCHNAT_CODE))"),
    })
    MerchantBaseReq toMerchantBaseReq(MerchantBaseInfoRequest request);
    @Mappings({
            @Mapping(target = "code", source = "result.code"),
            @Mapping(target = "msg", source = "result.msg"),
            @Mapping(target = "bizType", expression = "java(Optional.ofNullable(response).map(MerchantBaseResp::getMerchantBase).map(MerchantBase::getBizType).orElse(Nullable.getNullVal()))"),
    })
    MerchantBaseInfo toMerchantInfo(Result<MerchantBaseResp> result,MerchantBaseResp response);
}

