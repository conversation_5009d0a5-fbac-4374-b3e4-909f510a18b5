package com.payermax.operating.correction.integration.enums;


import com.payermax.operating.correction.core.common.utils.Nullable;
import lombok.Getter;

import java.util.Arrays;

public enum ChannelOrderStatus {
    INITIATE("9", "初始化"),
    PENDING("0", "进行中"),
    SUCCESS("1", "成功"),
    FAILED("2", "失败"),
    BOUNCEBACK("4", "退票");

    ChannelOrderStatus(String val, String name) {
        this.val = val;
        this.name = name;
    }

    @Getter
    private String val;

    @Getter
    private String name;

    public static ChannelOrderStatus getChannelByVal(String val) {
        return Arrays.stream(values()).filter(e -> e.getVal().equals(val)).findFirst().orElse(Nullable.getNullVal());//CHECKED 枚举匹配出来必然只有一个
    }
}
