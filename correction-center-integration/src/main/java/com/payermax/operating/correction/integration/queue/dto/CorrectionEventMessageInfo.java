package com.payermax.operating.correction.integration.queue.dto;

import com.payermax.common.lang.util.money.Money;
import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc 差错推送事件消息
 * @date 2022/9/27
 */
@Data
@NoArgsConstructor
public class CorrectionEventMessageInfo extends BaseMqMessage {

    private EventMessageInfo messageBody;

    @Data
    @NoArgsConstructor
    public static class EventMessageInfo {
        private String sysSource;

        /**
         * 差错信息
         */
        private EventInfo correctionInfo;
        /**
         * 差错金额信息
         */
        private AmountInfo amountInfo;

        /**
         * 渠道对账冗余信息
         */
        private ChannelReconcileRedundantInfo reconcileRedundantInfo;

        /**
         * 重试类型
         */
        private String retryType;

        /**
         * 重试次数
         */
        private Integer retryNum;
    }

    @Data
    @NoArgsConstructor
    public static class EventInfo {

        private String voucherNo;

        private String voucherType;

        private String correctionCode;

        private String detailCode;

        private String detailDesc;

    }


    @Data
    public static class AmountInfo {

        private Money payTotalMoney;

    }

    @Data
    public static class ChannelReconcileRedundantInfo{
        /**
         * 主体
         */
        private String entity;

        /**
         * 交易类型
         */
        private String tradeType;

        /**
         * 机构名称
         */
        private String orgName;

        /**
         * 渠道编码
         */
        private String channelCode;

        /**
         * 完成时间(外部完成时间)
         */
        private String completeTime;

        /**
         * 提交单完成时间(金融交换)
         */
        private String commitCompleteTime;

        /**
         * 外部状态
         */
        private String channelStatus;

        /**
         * 提交单状态(金融交换)
         */
        private String commitStatus;

        /**
         * 批次信息
         */
        private String batchNo;

        /**
         * 提交单号
         */
        private String channelCommitNo;

        /**
         * 三方单号
         */
        private String thirdOrderNo;

        /**
         * 支付币种
         */
        private String currency;

        /**
         * 金额
         */
        private BigDecimal amount;


        /**
         * 对账规则Id
         */
        private String reconcileRuleId;

        /**
         * 交易对账差错凭证号
         */
        private String errorOrderNo;

        /**
         * 交易类型(外部)
         */
        private String channelTradeType;
    }

}
