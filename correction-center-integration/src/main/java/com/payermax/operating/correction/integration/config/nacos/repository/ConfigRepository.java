package com.payermax.operating.correction.integration.config.nacos.repository;

import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.integration.config.nacos.model.*;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * The interface Config repository.
 */
public interface ConfigRepository {

    /**
     * Gets strategy processor.
     *
     * @param strategyCode the strategy code
     * @return the strategy processor
     */
    StrategyProcessorInfo getStrategyProcessor(String strategyCode);

    /**
     * Gets correction sys.
     *
     * @param correctionCode the correction code
     * @param sysSource      the sys source
     * @return the correction sys
     */
    CorrectionSystemInfo getCorrectionSys(String correctionCode, String sysSource);

    /**
     * Gets parent reason basic info.
     *
     * @return the parent reason basic info
     */
    Map<String, CorrectionBasicInfoDTO> getParentReasonBasicMap();

    /**
     * Get parent reason basic info by memory list.
     *
     * @return the list
     */
    List<CorrectionBasicInfoDTO> getParentReasonBasicInfoByMemory();

    /**
     * 获取匹配规则
     *
     * @param correctionCode the correction code
     * @param tradeType      the trade type
     * @return the rule info
     */
    List<MatcherRule> getRuleInfo(String correctionCode, TradeType tradeType);


    /**
     * Get strategy info list.
     *
     * @param correctionCode the correction code
     * @param tradeType      the trade type
     * @return the list
     */
    StrategyRule getStrategyInfo(String correctionCode,TradeType tradeType);

    /**
     * Get pay outs country list.
     *
     * @return the list
     */
    List<CorrectionPayoutsCountryInfo>getPayOutsCountry();

    ConcurrentHashMap<String, List<Expression>> getStrategyBlacklist();

    /**
     * Gets pay method.
     *
     * @param payMethod the pay method
     * @return the pay method
     */
    String getPayMethod(String payMethod);

    /**
     * Gets match unique key.
     *
     * @param correctionCode the correction code
     * @param tradeType      the trade type
     * @return the match unique key
     */
    String getMatchUniqueKey(String correctionCode,TradeType tradeType);

    /**
     * Gets all register handler list.
     *
     * @return the all register handler list
     */
    List<String> getAllRegisterHandlerList();


    /**
     * refresh All Parent Basic Info
     */
    void refreshAllParentBasicInfo();

    DingTalkNotifyMappingInfo getNotifyInfo(String shareId);
}
