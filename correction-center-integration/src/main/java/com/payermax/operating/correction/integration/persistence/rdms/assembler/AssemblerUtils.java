package com.payermax.operating.correction.integration.persistence.rdms.assembler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.base.Joiner;
import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.dto.CorrectionBaseInfo;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.dto.OperationManualFillIn;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.enums.ValidType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.persistence.rdms.dto.ReconcileRedundantDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Currency;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 转换器工具类
 * @date 2022/9/20
 */
public class AssemblerUtils {

    private final static Logger logger = LoggerFactory.getLogger(AssemblerUtils.class);

    public static String listToStr(List list) {
        return Joiner.on(Symbols.COMMA).join(list);
    }

    public static OperationBasicInfo getOpBasicInfo(String operator, Byte val, Date utcCreate, Date utcModify) {
        return new OperationBasicInfo(operator, ValidType.getValid(val), utcCreate.getTime(), utcModify.getTime());
    }

    public static OperationBasicInfo getOpBasicInfo(String operator, Date utcCreate, Date utcModify) {
        return new OperationBasicInfo(operator, utcCreate.getTime(), utcModify.getTime());
    }

    public static VoucherInfo getVoucherInfo(String str) {
        if (StringUtils.isBlank(str)) {
            return Nullable.getNullVal();
        }
        return JSONObject.parseObject(str, new TypeReference<VoucherInfo>() {
        });
    }

    public static Money getMoney(BigDecimal decimal, String cur) {
        if (Objects.isNull(decimal) || StringUtils.isBlank(cur)) {
            return Nullable.getNullVal();
        }
        return new Money(decimal, Currency.getInstance(cur));
    }

    public static OperationManualFillIn getOrderInfoOperation(String memo, String proofInfo, String extraUserInfo) {
        return new OperationManualFillIn(memo, proofInfo, extraUserInfo);
    }

    public static CorrectionBaseInfo getBaseInfo(String correctionNo, String correctionCode, String voucherNo, String voucherType, String correctionRemark, String originalRedundantInfoJson, String tradeType) {
        ReconcileRedundantDTO originalRedundantInfo = getOriginalRedundantInfo(originalRedundantInfoJson);
        return new CorrectionBaseInfo(correctionNo, correctionCode, new VoucherInfo(voucherNo, DCVoucherType.getVoucherTypeByName(voucherType)),
                TradeType.getTradeTypeByName(tradeType), correctionRemark, originalRedundantInfo.getErrorCode());
    }

    public static ReconcileRedundantDTO getOriginalRedundantInfo(String json) {
        String defaultJson = StringUtils.defaultIfBlank(json, Symbols.PARANTHESES_STR);
        ReconcileRedundantDTO reconcileRedundantDTO = null;
        try {
            reconcileRedundantDTO = JSONObject.parseObject(defaultJson, new TypeReference<ReconcileRedundantDTO>() {
            });
        } catch (Exception e) {
            logger.info("AssemblerUtils getOriginalRedundantInfo is warn ,json :{}", defaultJson);
        }
        return reconcileRedundantDTO;
    }

}
