package com.payermax.operating.correction.integration.rpc.risk.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @desc 风控类型
 * @date 2022/12/19
 */
public enum RiskType {
    REFUND_TO_PAYOUTS_EVENT_BEFORE("D0005", "D00050001"),
    REFUND_TO_PAYOUTS_EVENT_AFTER("D0006", "D00060001"),
    ;

    RiskType(String point, String eventId) {
        this.point = point;
        this.eventId = eventId;
    }

    @Getter
    private String point;

    @Getter
    private String eventId;
}
