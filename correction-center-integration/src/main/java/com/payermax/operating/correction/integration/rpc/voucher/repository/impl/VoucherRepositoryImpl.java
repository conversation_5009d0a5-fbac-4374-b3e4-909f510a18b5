package com.payermax.operating.correction.integration.rpc.voucher.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.payermax.basic.voucher.common.enums.PaymentScene;
import com.payermax.basic.voucher.facade.VoucherFacade;
import com.payermax.basic.voucher.facade.request.CreatePaymentVoucherRequest;
import com.payermax.basic.voucher.facade.request.CreateTradeVoucherRequest;
import com.payermax.basic.voucher.facade.response.CreatePaymentVoucherResponse;
import com.payermax.basic.voucher.facade.response.CreateTradeVoucherResponse;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.integration.rpc.voucher.repository.VoucherRepository;
import com.payermax.operating.correction.integration.rpc.voucher.repository.assembler.VoucherAssembler;
import com.payermax.operating.correction.integration.rpc.voucher.repository.dto.VoucherRequestDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 凭证服务rpc资源实现类
 * @date 2023/3/6
 */
@Repository
@Slf4j
public class VoucherRepositoryImpl implements VoucherRepository {

    @DubboReference(version = "1.0", timeout = 5000)
    private VoucherFacade voucherFacade;

    private final static String CORRECTION_BIZ_IDENTIFY = "P0101";

    private final static String CORRECTION_COLLECTION = "1001";

    private final static String CORRECTION_PAYOUTS = "2001";

    @Resource
    private VoucherAssembler voucherAssembler;


    @Override
    public String getCorrectionVoucherNo(VoucherRequestDTO voucherInfo) {
        CreatePaymentVoucherRequest voucherRequest = voucherAssembler.toPaymentVoucherRequest(voucherInfo);
        this.wrapCorrectionVoucherInfo(voucherInfo, voucherRequest);
        Result<CreatePaymentVoucherResponse> result = voucherFacade.createPaymentVoucher(voucherRequest);
        AssertUtil.isTrue(ResultUtil.isApplySuccess(result), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_VOUCHER_EXCEPTION);
        return result.getData().getPaymentVoucherNo();
    }

    /**
     * @desc 暂不使用
     * <AUTHOR>
     * @date 2023/3/22
     */
    @Override
    public String getPaymentVoucherNo(VoucherRequestDTO voucherInfo) {
        Result<CreatePaymentVoucherResponse> result = voucherFacade.createPaymentVoucher(voucherAssembler.toPaymentVoucherRequest(voucherInfo));
        AssertUtil.isTrue(ResultUtil.isApplySuccess(result), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_VOUCHER_EXCEPTION);
        return result.getData().getPaymentVoucherNo();
    }
    /**
     * @desc 暂不使用
     * <AUTHOR>
     * @date 2023/3/22
     */
    @Override
    public String getTradeVoucherNo(VoucherRequestDTO voucherInfo) {
        Result<CreateTradeVoucherResponse> result = voucherFacade.createTradeVoucher(voucherAssembler.toTradeVoucherRequest(voucherInfo));
        AssertUtil.isTrue(ResultUtil.isApplySuccess(result), ReturnCode.RPC_EXCEPTION.getCode(), ReturnMsg.RPC_VOUCHER_EXCEPTION);
        return result.getData().getTradeVoucherNo();
    }

    private void wrapCorrectionVoucherInfo(VoucherRequestDTO voucherRequestDTO, CreatePaymentVoucherRequest voucherRequest) {
        switch (voucherRequestDTO.getTradeType()) {
            case REFUND:
                voucherRequest.setProductCode(CORRECTION_COLLECTION);
                voucherRequest.setPaymentScene(PaymentScene.ERROR_REFUND.getCode());
                break;
            case PAYMENT:
                voucherRequest.setProductCode(CORRECTION_COLLECTION);
                voucherRequest.setPaymentScene(PaymentScene.ERROR.getCode());
                break;
            case PAYOUTS:
                voucherRequest.setProductCode(CORRECTION_PAYOUTS);
                voucherRequest.setPaymentScene(PaymentScene.ERROR_OUTWARD.getCode());
                if (Objects.nonNull(voucherRequestDTO.getPayAmount())){
                    JSONObject json=new JSONObject();
                    json.put("amount",voucherRequestDTO.getPayAmount().getAmount().toString());
                    json.put("currency",voucherRequestDTO.getPayAmount().getCurrency().getCurrencyCode());
                    json.put("accountNo",voucherRequestDTO.getAccountNo());
                    voucherRequest.setAmount(voucherRequestDTO.getPayAmount().getAmount());
                    voucherRequest.setCurrency(voucherRequestDTO.getPayAmount().getCurrency().getCurrencyCode());
                    voucherRequest.setInfo(json.toJSONString());
                }
                break;
            default:
                throw new BusinessException(ReturnCode.BUSINESS_EXCEPTION.getCode());
        }
        voucherRequest.setBizIdentity(CORRECTION_BIZ_IDENTIFY);
    }
}
