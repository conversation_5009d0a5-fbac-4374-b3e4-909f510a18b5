package com.payermax.operating.correction.integration.rpc.paylink.impl;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.correction.integration.rpc.paylink.PayLinkReository;
import com.payermax.operating.correction.integration.rpc.paylink.assembler.PayLinkAssembler;
import com.payermax.operating.correction.integration.rpc.paylink.dto.PayLinkPatchNoticeRequest;
import com.payermax.operating.correction.integration.rpc.paylink.dto.PayLinkPatchNoticeResponse;
import com.payermax.trade.paylink.facade.api.NoticeFacade;
import com.payermax.trade.paylink.facade.response.PatchPayLinkNoticeResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * @description: 商服服务接口实现类
 * @author: Wang<PERSON>ao
 * @create: 2024-11-26 11:12
 **/
@Repository
public class PayLinkRepositoryImpl implements PayLinkReository {

    @DubboReference(timeout = 5000, version = "1.0")
    private NoticeFacade noticeFacade;

    @Autowired
    private PayLinkAssembler assembler;


    @Override
    public PayLinkPatchNoticeResponse patchNotice(PayLinkPatchNoticeRequest request) {
        Result<PatchPayLinkNoticeResponse> patchPayLinkNoticeResponseResult = noticeFacade.patchPayLinkNotice(assembler.toPatchPayLinkNotice(request));
        return assembler.toPayLinkPatchNoticeResponse(patchPayLinkNoticeResponseResult,patchPayLinkNoticeResponseResult.getData());
    }
}
