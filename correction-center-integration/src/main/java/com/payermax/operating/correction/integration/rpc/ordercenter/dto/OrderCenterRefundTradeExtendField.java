package com.payermax.operating.correction.integration.rpc.ordercenter.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 退款单扩展字段
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2024-12-24 17:26
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OrderCenterRefundTradeExtendField {
    /**
     * 原支付单号
     */
    private String originPayRequestNo;
}
