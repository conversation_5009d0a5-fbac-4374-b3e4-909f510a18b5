package com.payermax.operating.correction.core.dal.po;

import java.util.Date;

public class CorrectionOrderHandlerRecord extends CorrectionOrderHandlerRecordKey {
    private String processStatus;

    private String operator;

    private Date utcCreate;

    private Date utcModified;

    private String processResult;

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus == null ? null : processStatus.trim();
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator == null ? null : operator.trim();
    }

    public Date getUtcCreate() {
        return utcCreate;
    }

    public void setUtcCreate(Date utcCreate) {
        this.utcCreate = utcCreate;
    }

    public Date getUtcModified() {
        return utcModified;
    }

    public void setUtcModified(Date utcModified) {
        this.utcModified = utcModified;
    }

    public String getProcessResult() {
        return processResult;
    }

    public void setProcessResult(String processResult) {
        this.processResult = processResult == null ? null : processResult.trim();
    }
}