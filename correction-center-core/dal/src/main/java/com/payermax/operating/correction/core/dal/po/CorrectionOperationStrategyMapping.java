package com.payermax.operating.correction.core.dal.po;

import java.util.Date;

public class CorrectionOperationStrategyMapping extends CorrectionOperationStrategyMappingKey {
    private Byte isValid;

    private Date utcCreate;

    private Date utcModified;

    private String operator;

    public Byte getIsValid() {
        return isValid;
    }

    public void setIsValid(Byte isValid) {
        this.isValid = isValid;
    }

    public Date getUtcCreate() {
        return utcCreate;
    }

    public void setUtcCreate(Date utcCreate) {
        this.utcCreate = utcCreate;
    }

    public Date getUtcModified() {
        return utcModified;
    }

    public void setUtcModified(Date utcModified) {
        this.utcModified = utcModified;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator == null ? null : operator.trim();
    }
}