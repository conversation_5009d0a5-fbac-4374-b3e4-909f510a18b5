package com.payermax.operating.correction.core.dal.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.payermax.operating.correction.core.dal.po.CorrectionBasicInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface CorrectionBasicInfoMapper {

    int insertDupKeyUpdate(CorrectionBasicInfo record);

    CorrectionBasicInfo selectByPrimaryKey(String correctionCode);

    int updateByPrimaryKeySelective(CorrectionBasicInfo record);

    List<CorrectionBasicInfo> selectAllChildrenValid();

    List<CorrectionBasicInfo> selectAllParentValid();

    List<CorrectionBasicInfo> selectPageRequest(Page<CorrectionBasicInfo> page, @Param("isValid")Byte isValid);

    List<CorrectionBasicInfo> selectValidByParentCorrectionCode(@Param("parentCorrectionCode")String parentCorrectionCode,@Param("tradeType")String tradeType);
}