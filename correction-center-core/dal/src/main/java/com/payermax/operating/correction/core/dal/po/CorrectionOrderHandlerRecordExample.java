package com.payermax.operating.correction.core.dal.po;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CorrectionOrderHandlerRecordExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CorrectionOrderHandlerRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andCorrectionNoIsNull() {
            addCriterion("correction_no is null");
            return (Criteria) this;
        }

        public Criteria andCorrectionNoIsNotNull() {
            addCriterion("correction_no is not null");
            return (Criteria) this;
        }

        public Criteria andCorrectionNoEqualTo(String value) {
            addCriterion("correction_no =", value, "correctionNo");
            return (Criteria) this;
        }

        public Criteria andCorrectionNoNotEqualTo(String value) {
            addCriterion("correction_no <>", value, "correctionNo");
            return (Criteria) this;
        }

        public Criteria andCorrectionNoGreaterThan(String value) {
            addCriterion("correction_no >", value, "correctionNo");
            return (Criteria) this;
        }

        public Criteria andCorrectionNoGreaterThanOrEqualTo(String value) {
            addCriterion("correction_no >=", value, "correctionNo");
            return (Criteria) this;
        }

        public Criteria andCorrectionNoLessThan(String value) {
            addCriterion("correction_no <", value, "correctionNo");
            return (Criteria) this;
        }

        public Criteria andCorrectionNoLessThanOrEqualTo(String value) {
            addCriterion("correction_no <=", value, "correctionNo");
            return (Criteria) this;
        }

        public Criteria andCorrectionNoLike(String value) {
            addCriterion("correction_no like", value, "correctionNo");
            return (Criteria) this;
        }

        public Criteria andCorrectionNoNotLike(String value) {
            addCriterion("correction_no not like", value, "correctionNo");
            return (Criteria) this;
        }

        public Criteria andCorrectionNoIn(List<String> values) {
            addCriterion("correction_no in", values, "correctionNo");
            return (Criteria) this;
        }

        public Criteria andCorrectionNoNotIn(List<String> values) {
            addCriterion("correction_no not in", values, "correctionNo");
            return (Criteria) this;
        }

        public Criteria andCorrectionNoBetween(String value1, String value2) {
            addCriterion("correction_no between", value1, value2, "correctionNo");
            return (Criteria) this;
        }

        public Criteria andCorrectionNoNotBetween(String value1, String value2) {
            addCriterion("correction_no not between", value1, value2, "correctionNo");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeIsNull() {
            addCriterion("strategy_code is null");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeIsNotNull() {
            addCriterion("strategy_code is not null");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeEqualTo(String value) {
            addCriterion("strategy_code =", value, "strategyCode");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeNotEqualTo(String value) {
            addCriterion("strategy_code <>", value, "strategyCode");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeGreaterThan(String value) {
            addCriterion("strategy_code >", value, "strategyCode");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("strategy_code >=", value, "strategyCode");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeLessThan(String value) {
            addCriterion("strategy_code <", value, "strategyCode");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeLessThanOrEqualTo(String value) {
            addCriterion("strategy_code <=", value, "strategyCode");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeLike(String value) {
            addCriterion("strategy_code like", value, "strategyCode");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeNotLike(String value) {
            addCriterion("strategy_code not like", value, "strategyCode");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeIn(List<String> values) {
            addCriterion("strategy_code in", values, "strategyCode");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeNotIn(List<String> values) {
            addCriterion("strategy_code not in", values, "strategyCode");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeBetween(String value1, String value2) {
            addCriterion("strategy_code between", value1, value2, "strategyCode");
            return (Criteria) this;
        }

        public Criteria andStrategyCodeNotBetween(String value1, String value2) {
            addCriterion("strategy_code not between", value1, value2, "strategyCode");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeIsNull() {
            addCriterion("handler_type is null");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeIsNotNull() {
            addCriterion("handler_type is not null");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeEqualTo(String value) {
            addCriterion("handler_type =", value, "handlerType");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeNotEqualTo(String value) {
            addCriterion("handler_type <>", value, "handlerType");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeGreaterThan(String value) {
            addCriterion("handler_type >", value, "handlerType");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeGreaterThanOrEqualTo(String value) {
            addCriterion("handler_type >=", value, "handlerType");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeLessThan(String value) {
            addCriterion("handler_type <", value, "handlerType");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeLessThanOrEqualTo(String value) {
            addCriterion("handler_type <=", value, "handlerType");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeLike(String value) {
            addCriterion("handler_type like", value, "handlerType");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeNotLike(String value) {
            addCriterion("handler_type not like", value, "handlerType");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeIn(List<String> values) {
            addCriterion("handler_type in", values, "handlerType");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeNotIn(List<String> values) {
            addCriterion("handler_type not in", values, "handlerType");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeBetween(String value1, String value2) {
            addCriterion("handler_type between", value1, value2, "handlerType");
            return (Criteria) this;
        }

        public Criteria andHandlerTypeNotBetween(String value1, String value2) {
            addCriterion("handler_type not between", value1, value2, "handlerType");
            return (Criteria) this;
        }

        public Criteria andProcessStatusIsNull() {
            addCriterion("process_status is null");
            return (Criteria) this;
        }

        public Criteria andProcessStatusIsNotNull() {
            addCriterion("process_status is not null");
            return (Criteria) this;
        }

        public Criteria andProcessStatusEqualTo(String value) {
            addCriterion("process_status =", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusNotEqualTo(String value) {
            addCriterion("process_status <>", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusGreaterThan(String value) {
            addCriterion("process_status >", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusGreaterThanOrEqualTo(String value) {
            addCriterion("process_status >=", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusLessThan(String value) {
            addCriterion("process_status <", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusLessThanOrEqualTo(String value) {
            addCriterion("process_status <=", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusLike(String value) {
            addCriterion("process_status like", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusNotLike(String value) {
            addCriterion("process_status not like", value, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusIn(List<String> values) {
            addCriterion("process_status in", values, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusNotIn(List<String> values) {
            addCriterion("process_status not in", values, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusBetween(String value1, String value2) {
            addCriterion("process_status between", value1, value2, "processStatus");
            return (Criteria) this;
        }

        public Criteria andProcessStatusNotBetween(String value1, String value2) {
            addCriterion("process_status not between", value1, value2, "processStatus");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("operator is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("operator is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("operator =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("operator <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("operator >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("operator >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("operator <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("operator <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("operator like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("operator not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("operator in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("operator not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("operator between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("operator not between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andUtcCreateIsNull() {
            addCriterion("utc_create is null");
            return (Criteria) this;
        }

        public Criteria andUtcCreateIsNotNull() {
            addCriterion("utc_create is not null");
            return (Criteria) this;
        }

        public Criteria andUtcCreateEqualTo(Date value) {
            addCriterion("utc_create =", value, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateNotEqualTo(Date value) {
            addCriterion("utc_create <>", value, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateGreaterThan(Date value) {
            addCriterion("utc_create >", value, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("utc_create >=", value, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateLessThan(Date value) {
            addCriterion("utc_create <", value, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateLessThanOrEqualTo(Date value) {
            addCriterion("utc_create <=", value, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateIn(List<Date> values) {
            addCriterion("utc_create in", values, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateNotIn(List<Date> values) {
            addCriterion("utc_create not in", values, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateBetween(Date value1, Date value2) {
            addCriterion("utc_create between", value1, value2, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateNotBetween(Date value1, Date value2) {
            addCriterion("utc_create not between", value1, value2, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedIsNull() {
            addCriterion("utc_modified is null");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedIsNotNull() {
            addCriterion("utc_modified is not null");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedEqualTo(Date value) {
            addCriterion("utc_modified =", value, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedNotEqualTo(Date value) {
            addCriterion("utc_modified <>", value, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedGreaterThan(Date value) {
            addCriterion("utc_modified >", value, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("utc_modified >=", value, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedLessThan(Date value) {
            addCriterion("utc_modified <", value, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedLessThanOrEqualTo(Date value) {
            addCriterion("utc_modified <=", value, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedIn(List<Date> values) {
            addCriterion("utc_modified in", values, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedNotIn(List<Date> values) {
            addCriterion("utc_modified not in", values, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedBetween(Date value1, Date value2) {
            addCriterion("utc_modified between", value1, value2, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedNotBetween(Date value1, Date value2) {
            addCriterion("utc_modified not between", value1, value2, "utcModified");
            return (Criteria) this;
        }

        public Criteria andProcessRequestEqualTo(String value) {
            addCriterion("process_request =", value, "processRequest");
            return (Criteria) this;
        }

        public Criteria andProcessRequestNotEqualTo(String value) {
            addCriterion("process_request <>", value, "processRequest");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}