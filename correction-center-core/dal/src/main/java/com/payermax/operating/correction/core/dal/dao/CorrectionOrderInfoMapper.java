package com.payermax.operating.correction.core.dal.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.payermax.operating.correction.core.dal.QueryCorrectionOrderInfo;
import com.payermax.operating.correction.core.dal.VoucherUniqueInfo;
import com.payermax.operating.correction.core.dal.po.CorrectionOrderInfo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface CorrectionOrderInfoMapper {

    int insertSelective(CorrectionOrderInfo record);

    CorrectionOrderInfo selectByPrimaryKey(String correctionNo);

    int updateByPrimaryKeySelective(CorrectionOrderInfo record);

    int updateByPrimaryKeyWithBLOBs(CorrectionOrderInfo record);

    int updateByOptimisticLock(@Param("correctionNo") String correctionNo, @Param("expect") String expect, @Param("target") String target, @Param("completeTime") Date completeTime);

    List<CorrectionOrderInfo> selectPageRequest(Page<CorrectionOrderInfo> page, @Param("queryOrderInfo") QueryCorrectionOrderInfo queryOrderInfo);

    List<CorrectionOrderInfo> selectPageRequestList(Page<CorrectionOrderInfo> page, @Param("correctionNos") List<String> correctionNos, @Param("voucherNos") List<String> voucherNos);

    CorrectionOrderInfo selectByUniqueKey(@Param("voucherUniqueInfo") VoucherUniqueInfo voucherUniqueInfo);

    int updateIrrelevantInfo(CorrectionOrderInfo record);

    List<CorrectionOrderInfo> selectByCheckingOrder(CorrectionOrderInfo orderInfo);
}