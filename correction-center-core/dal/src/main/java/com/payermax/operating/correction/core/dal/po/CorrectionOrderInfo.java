package com.payermax.operating.correction.core.dal.po;

import java.math.BigDecimal;
import java.util.Date;

public class CorrectionOrderInfo {
    private String correctionNo;

    private String merchantOrderNo;

    private String voucherNo;

    private String voucherType;

    private String reqVoucherInfo;

    private Byte eventSource;

    private String sysSource;

    private String correctionCode;

    private String processStatus;

    private String operationStrategyCode;

    private String resVoucherInfo;

    private String currency;

    private BigDecimal totalAmount;

    private String memo;

    private Date utcCreate;

    private Date utcModified;

    private String operator;

    private String extendInfo;

    private Date completeTime;

    private String operationCorrectionCode;

    private String userInfo;

    private String channelCode;

    private String resVoucherNo;

    /**
     * 仅做展示使用，无需更新
     */
    private String correctionRemark;
    /**
     * 仅做展示使用，无需更新
     */
    private String originalRedundantInfo;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 复核者
     */
    private String reviewer;

    public String getCorrectionNo() {
        return correctionNo;
    }

    public void setCorrectionNo(String correctionNo) {
        this.correctionNo = correctionNo == null ? null : correctionNo.trim();
    }

    public String getMerchantOrderNo() {
        return merchantOrderNo;
    }

    public void setMerchantOrderNo(String merchantOrderNo) {
        this.merchantOrderNo = merchantOrderNo == null ? null : merchantOrderNo.trim();
    }

    public String getVoucherNo() {
        return voucherNo;
    }

    public void setVoucherNo(String voucherNo) {
        this.voucherNo = voucherNo == null ? null : voucherNo.trim();
    }

    public String getReqVoucherInfo() {
        return reqVoucherInfo;
    }

    public void setReqVoucherInfo(String reqVoucherInfo) {
        this.reqVoucherInfo = reqVoucherInfo == null ? null : reqVoucherInfo.trim();
    }

    public Byte getEventSource() {
        return eventSource;
    }

    public void setEventSource(Byte eventSource) {
        this.eventSource = eventSource;
    }

    public String getSysSource() {
        return sysSource;
    }

    public void setSysSource(String sysSource) {
        this.sysSource = sysSource == null ? null : sysSource.trim();
    }

    public String getCorrectionCode() {
        return correctionCode;
    }

    public void setCorrectionCode(String correctionCode) {
        this.correctionCode = correctionCode == null ? null : correctionCode.trim();
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus == null ? null : processStatus.trim();
    }

    public String getOperationStrategyCode() {
        return operationStrategyCode;
    }

    public void setOperationStrategyCode(String operationStrategyCode) {
        this.operationStrategyCode = operationStrategyCode == null ? null : operationStrategyCode.trim();
    }

    public String getResVoucherInfo() {
        return resVoucherInfo;
    }

    public void setResVoucherInfo(String resVoucherInfo) {
        this.resVoucherInfo = resVoucherInfo == null ? null : resVoucherInfo.trim();
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency == null ? null : currency.trim();
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo == null ? null : memo.trim();
    }

    public Date getUtcCreate() {
        return utcCreate;
    }

    public void setUtcCreate(Date utcCreate) {
        this.utcCreate = utcCreate;
    }

    public Date getUtcModified() {
        return utcModified;
    }

    public void setUtcModified(Date utcModified) {
        this.utcModified = utcModified;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator == null ? null : operator.trim();
    }

    public String getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(String extendInfo) {
        this.extendInfo = extendInfo == null ? null : extendInfo.trim();
    }

    public String getVoucherType() {
        return voucherType;
    }

    public void setVoucherType(String voucherType) {
        this.voucherType = voucherType;
    }

    public Date getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(Date completeTime) {
        this.completeTime = completeTime;
    }

    public String getOperationCorrectionCode() {
        return operationCorrectionCode;
    }

    public void setOperationCorrectionCode(String operationCorrectionCode) {
        this.operationCorrectionCode = operationCorrectionCode;
    }

    public String getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(String userInfo) {
        this.userInfo = userInfo;
    }

    public String getChannelCode() {
        return channelCode;
    }

    public void setChannelCode(String channelCode) {
        this.channelCode = channelCode;
    }

    public String getResVoucherNo() {
        return resVoucherNo;
    }

    public void setResVoucherNo(String resVoucherNo) {
        this.resVoucherNo = resVoucherNo;
    }

    public String getCorrectionRemark() {
        return correctionRemark;
    }

    public void setCorrectionRemark(String correctionRemark) {
        this.correctionRemark = correctionRemark;
    }

    public String getOriginalRedundantInfo() {
        return originalRedundantInfo;
    }

    public void setOriginalRedundantInfo(String originalRedundantInfo) {
        this.originalRedundantInfo = originalRedundantInfo;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }
}