package com.payermax.operating.correction.core.dal.po;

public class CorrectionOperationStrategyMappingKey {
    private String operationStrategyCode;

    private String correctionCode;

    private String tradeType;

    public String getOperationStrategyCode() {
        return operationStrategyCode;
    }

    public void setOperationStrategyCode(String operationStrategyCode) {
        this.operationStrategyCode = operationStrategyCode == null ? null : operationStrategyCode.trim();
    }

    public String getCorrectionCode() {
        return correctionCode;
    }

    public void setCorrectionCode(String correctionCode) {
        this.correctionCode = correctionCode == null ? null : correctionCode.trim();
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }
}