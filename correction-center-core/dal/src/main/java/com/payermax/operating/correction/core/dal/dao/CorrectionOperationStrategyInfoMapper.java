package com.payermax.operating.correction.core.dal.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CorrectionOperationStrategyInfoMapper {

    int insertDupKeyUpdate(CorrectionOperationStrategyInfo record);

    CorrectionOperationStrategyInfo selectByPrimaryKey(String operationStrategyCode);

    List<CorrectionOperationStrategyInfo> selectAllValid();

    List<CorrectionOperationStrategyInfo>selectPageRequest(Page<CorrectionOperationStrategyInfo> page,@Param("isValid") Byte isValid);
}