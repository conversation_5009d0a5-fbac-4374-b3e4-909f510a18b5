package com.payermax.operating.correction.core.dal.dao;

import com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyMapping;
import com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyMappingExample;
import com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyMappingKey;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CorrectionOperationStrategyMappingMapper {
    int deleteByPrimaryKey(CorrectionOperationStrategyMappingKey key);

    int insert(CorrectionOperationStrategyMapping record);

    int insertSelective(CorrectionOperationStrategyMapping record);

    List<CorrectionOperationStrategyMapping> selectByExample(CorrectionOperationStrategyMappingExample example);

    CorrectionOperationStrategyMapping selectByPrimaryKey(CorrectionOperationStrategyMappingKey key);

    int updateByExampleSelective(@Param("record") CorrectionOperationStrategyMapping record, @Param("example") CorrectionOperationStrategyMappingExample example);

    int updateByExample(@Param("record") CorrectionOperationStrategyMapping record, @Param("example") CorrectionOperationStrategyMappingExample example);

    int updateByPrimaryKeySelective(CorrectionOperationStrategyMapping record);

    int updateByPrimaryKey(CorrectionOperationStrategyMapping record);

    void batchInsert(@Param("list") List<CorrectionOperationStrategyMapping> list);
}