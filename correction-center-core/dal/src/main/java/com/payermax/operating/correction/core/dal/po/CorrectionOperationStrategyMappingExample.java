package com.payermax.operating.correction.core.dal.po;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CorrectionOperationStrategyMappingExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public CorrectionOperationStrategyMappingExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andOperationStrategyCodeIsNull() {
            addCriterion("operation_strategy_code is null");
            return (Criteria) this;
        }

        public Criteria andOperationStrategyCodeIsNotNull() {
            addCriterion("operation_strategy_code is not null");
            return (Criteria) this;
        }

        public Criteria andOperationStrategyCodeEqualTo(String value) {
            addCriterion("operation_strategy_code =", value, "operationStrategyCode");
            return (Criteria) this;
        }

        public Criteria andOperationStrategyCodeNotEqualTo(String value) {
            addCriterion("operation_strategy_code <>", value, "operationStrategyCode");
            return (Criteria) this;
        }

        public Criteria andOperationStrategyCodeGreaterThan(String value) {
            addCriterion("operation_strategy_code >", value, "operationStrategyCode");
            return (Criteria) this;
        }

        public Criteria andOperationStrategyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("operation_strategy_code >=", value, "operationStrategyCode");
            return (Criteria) this;
        }

        public Criteria andOperationStrategyCodeLessThan(String value) {
            addCriterion("operation_strategy_code <", value, "operationStrategyCode");
            return (Criteria) this;
        }

        public Criteria andOperationStrategyCodeLessThanOrEqualTo(String value) {
            addCriterion("operation_strategy_code <=", value, "operationStrategyCode");
            return (Criteria) this;
        }

        public Criteria andOperationStrategyCodeLike(String value) {
            addCriterion("operation_strategy_code like", value, "operationStrategyCode");
            return (Criteria) this;
        }

        public Criteria andOperationStrategyCodeNotLike(String value) {
            addCriterion("operation_strategy_code not like", value, "operationStrategyCode");
            return (Criteria) this;
        }

        public Criteria andOperationStrategyCodeIn(List<String> values) {
            addCriterion("operation_strategy_code in", values, "operationStrategyCode");
            return (Criteria) this;
        }

        public Criteria andOperationStrategyCodeNotIn(List<String> values) {
            addCriterion("operation_strategy_code not in", values, "operationStrategyCode");
            return (Criteria) this;
        }

        public Criteria andOperationStrategyCodeBetween(String value1, String value2) {
            addCriterion("operation_strategy_code between", value1, value2, "operationStrategyCode");
            return (Criteria) this;
        }

        public Criteria andOperationStrategyCodeNotBetween(String value1, String value2) {
            addCriterion("operation_strategy_code not between", value1, value2, "operationStrategyCode");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeIsNull() {
            addCriterion("correction_code is null");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeIsNotNull() {
            addCriterion("correction_code is not null");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeEqualTo(String value) {
            addCriterion("correction_code =", value, "correctionCode");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeNotEqualTo(String value) {
            addCriterion("correction_code <>", value, "correctionCode");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeGreaterThan(String value) {
            addCriterion("correction_code >", value, "correctionCode");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeGreaterThanOrEqualTo(String value) {
            addCriterion("correction_code >=", value, "correctionCode");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeLessThan(String value) {
            addCriterion("correction_code <", value, "correctionCode");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeLessThanOrEqualTo(String value) {
            addCriterion("correction_code <=", value, "correctionCode");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeLike(String value) {
            addCriterion("correction_code like", value, "correctionCode");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeNotLike(String value) {
            addCriterion("correction_code not like", value, "correctionCode");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeIn(List<String> values) {
            addCriterion("correction_code in", values, "correctionCode");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeNotIn(List<String> values) {
            addCriterion("correction_code not in", values, "correctionCode");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeBetween(String value1, String value2) {
            addCriterion("correction_code between", value1, value2, "correctionCode");
            return (Criteria) this;
        }

        public Criteria andCorrectionCodeNotBetween(String value1, String value2) {
            addCriterion("correction_code not between", value1, value2, "correctionCode");
            return (Criteria) this;
        }

        public Criteria andTradeTypeIsNull() {
            addCriterion("trade_type is null");
            return (Criteria) this;
        }

        public Criteria andTradeTypeIsNotNull() {
            addCriterion("trade_type is not null");
            return (Criteria) this;
        }

        public Criteria andTradeTypeEqualTo(String value) {
            addCriterion("trade_type =", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeNotEqualTo(String value) {
            addCriterion("trade_type <>", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeGreaterThan(String value) {
            addCriterion("trade_type >", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeGreaterThanOrEqualTo(String value) {
            addCriterion("trade_type >=", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeLessThan(String value) {
            addCriterion("trade_type <", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeLessThanOrEqualTo(String value) {
            addCriterion("trade_type <=", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeLike(String value) {
            addCriterion("trade_type like", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeNotLike(String value) {
            addCriterion("trade_type not like", value, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeIn(List<String> values) {
            addCriterion("trade_type in", values, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeNotIn(List<String> values) {
            addCriterion("trade_type not in", values, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeBetween(String value1, String value2) {
            addCriterion("trade_type between", value1, value2, "tradeType");
            return (Criteria) this;
        }

        public Criteria andTradeTypeNotBetween(String value1, String value2) {
            addCriterion("trade_type not between", value1, value2, "tradeType");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNull() {
            addCriterion("is_valid is null");
            return (Criteria) this;
        }

        public Criteria andIsValidIsNotNull() {
            addCriterion("is_valid is not null");
            return (Criteria) this;
        }

        public Criteria andIsValidEqualTo(Byte value) {
            addCriterion("is_valid =", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotEqualTo(Byte value) {
            addCriterion("is_valid <>", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThan(Byte value) {
            addCriterion("is_valid >", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_valid >=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThan(Byte value) {
            addCriterion("is_valid <", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidLessThanOrEqualTo(Byte value) {
            addCriterion("is_valid <=", value, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidIn(List<Byte> values) {
            addCriterion("is_valid in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotIn(List<Byte> values) {
            addCriterion("is_valid not in", values, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidBetween(Byte value1, Byte value2) {
            addCriterion("is_valid between", value1, value2, "isValid");
            return (Criteria) this;
        }

        public Criteria andIsValidNotBetween(Byte value1, Byte value2) {
            addCriterion("is_valid not between", value1, value2, "isValid");
            return (Criteria) this;
        }

        public Criteria andUtcCreateIsNull() {
            addCriterion("utc_create is null");
            return (Criteria) this;
        }

        public Criteria andUtcCreateIsNotNull() {
            addCriterion("utc_create is not null");
            return (Criteria) this;
        }

        public Criteria andUtcCreateEqualTo(Date value) {
            addCriterion("utc_create =", value, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateNotEqualTo(Date value) {
            addCriterion("utc_create <>", value, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateGreaterThan(Date value) {
            addCriterion("utc_create >", value, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateGreaterThanOrEqualTo(Date value) {
            addCriterion("utc_create >=", value, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateLessThan(Date value) {
            addCriterion("utc_create <", value, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateLessThanOrEqualTo(Date value) {
            addCriterion("utc_create <=", value, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateIn(List<Date> values) {
            addCriterion("utc_create in", values, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateNotIn(List<Date> values) {
            addCriterion("utc_create not in", values, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateBetween(Date value1, Date value2) {
            addCriterion("utc_create between", value1, value2, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcCreateNotBetween(Date value1, Date value2) {
            addCriterion("utc_create not between", value1, value2, "utcCreate");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedIsNull() {
            addCriterion("utc_modified is null");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedIsNotNull() {
            addCriterion("utc_modified is not null");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedEqualTo(Date value) {
            addCriterion("utc_modified =", value, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedNotEqualTo(Date value) {
            addCriterion("utc_modified <>", value, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedGreaterThan(Date value) {
            addCriterion("utc_modified >", value, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedGreaterThanOrEqualTo(Date value) {
            addCriterion("utc_modified >=", value, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedLessThan(Date value) {
            addCriterion("utc_modified <", value, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedLessThanOrEqualTo(Date value) {
            addCriterion("utc_modified <=", value, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedIn(List<Date> values) {
            addCriterion("utc_modified in", values, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedNotIn(List<Date> values) {
            addCriterion("utc_modified not in", values, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedBetween(Date value1, Date value2) {
            addCriterion("utc_modified between", value1, value2, "utcModified");
            return (Criteria) this;
        }

        public Criteria andUtcModifiedNotBetween(Date value1, Date value2) {
            addCriterion("utc_modified not between", value1, value2, "utcModified");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNull() {
            addCriterion("operator is null");
            return (Criteria) this;
        }

        public Criteria andOperatorIsNotNull() {
            addCriterion("operator is not null");
            return (Criteria) this;
        }

        public Criteria andOperatorEqualTo(String value) {
            addCriterion("operator =", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotEqualTo(String value) {
            addCriterion("operator <>", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThan(String value) {
            addCriterion("operator >", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorGreaterThanOrEqualTo(String value) {
            addCriterion("operator >=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThan(String value) {
            addCriterion("operator <", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLessThanOrEqualTo(String value) {
            addCriterion("operator <=", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorLike(String value) {
            addCriterion("operator like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotLike(String value) {
            addCriterion("operator not like", value, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorIn(List<String> values) {
            addCriterion("operator in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotIn(List<String> values) {
            addCriterion("operator not in", values, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorBetween(String value1, String value2) {
            addCriterion("operator between", value1, value2, "operator");
            return (Criteria) this;
        }

        public Criteria andOperatorNotBetween(String value1, String value2) {
            addCriterion("operator not between", value1, value2, "operator");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}