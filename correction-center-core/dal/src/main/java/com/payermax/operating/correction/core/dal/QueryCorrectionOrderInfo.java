package com.payermax.operating.correction.core.dal;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 差错差错订单信息对象
 * @date 2022/11/8
 */
@Getter
@Setter
@Builder
public class QueryCorrectionOrderInfo {
    private String voucherNo;

    private String merchantOrderNo;

    private String correctionCode;

    private List<String> processStatus;

    private String operationCorrectionCode;

    private String correctionNo;

    private String channelCode;

    private String redundantInfo;

    private List<String> sysSource;

    private String reviewer;

    private String operator;

    private String tradeType;

    private Date startTime;

    private Date endTime;
}
