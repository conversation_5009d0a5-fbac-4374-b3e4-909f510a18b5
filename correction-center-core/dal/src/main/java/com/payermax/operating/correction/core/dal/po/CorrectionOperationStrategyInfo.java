package com.payermax.operating.correction.core.dal.po;

import java.util.Date;

public class CorrectionOperationStrategyInfo {
    private String operationStrategyCode;

    private String operationStrategyName;

    private String extendShow;

    private Byte isValid;

    private Date utcCreate;

    private Date utcModified;

    private String operator;

    private String collectionShow;

    public String getOperationStrategyCode() {
        return operationStrategyCode;
    }

    public void setOperationStrategyCode(String operationStrategyCode) {
        this.operationStrategyCode = operationStrategyCode == null ? null : operationStrategyCode.trim();
    }

    public String getOperationStrategyName() {
        return operationStrategyName;
    }

    public void setOperationStrategyName(String operationStrategyName) {
        this.operationStrategyName = operationStrategyName == null ? null : operationStrategyName.trim();
    }

    public Byte getIsValid() {
        return isValid;
    }

    public void setIsValid(Byte isValid) {
        this.isValid = isValid;
    }

    public Date getUtcCreate() {
        return utcCreate;
    }

    public void setUtcCreate(Date utcCreate) {
        this.utcCreate = utcCreate;
    }

    public Date getUtcModified() {
        return utcModified;
    }

    public void setUtcModified(Date utcModified) {
        this.utcModified = utcModified;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator == null ? null : operator.trim();
    }

    public String getExtendShow() {
        return extendShow;
    }

    public void setExtendShow(String extendShow) {
        this.extendShow = extendShow;
    }

    public String getCollectionShow() {
        return collectionShow;
    }

    public void setCollectionShow(String collectionShow) {
        this.collectionShow = collectionShow;
    }
}