package com.payermax.operating.correction.core.dal;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 凭证唯一信息
 * @date 2023/3/3
 */
@Data
@NoArgsConstructor
public class VoucherUniqueInfo {

    private String correctionCode;

    private String voucherNo;

    private String resVoucherNo;

    private QueryType type;

    public VoucherUniqueInfo(String correctionCode, String voucherNo, QueryType type) {
        this.correctionCode = correctionCode;
        if (QueryType.REQUEST == type) {
            this.voucherNo = voucherNo;
        } else {
            this.resVoucherNo = voucherNo;
        }
    }

    public enum QueryType {
        REQUEST,
        RESULT;
    }
}
