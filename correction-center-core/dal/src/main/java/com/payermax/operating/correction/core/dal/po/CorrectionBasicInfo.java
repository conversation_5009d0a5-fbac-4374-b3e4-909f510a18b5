package com.payermax.operating.correction.core.dal.po;

import java.util.Date;

public class CorrectionBasicInfo {
    private String correctionCode;

    private String tradeType;

    private String correctionName;

    private String desc;

    private String execValidateRule;

    private Byte isValid;

    private Date utcCreate;

    private Date utcModified;

    private String operator;

    private String parentCorrectionCode;

    public String getCorrectionCode() {
        return correctionCode;
    }

    public void setCorrectionCode(String correctionCode) {
        this.correctionCode = correctionCode == null ? null : correctionCode.trim();
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType == null ? null : tradeType.trim();
    }

    public String getCorrectionName() {
        return correctionName;
    }

    public void setCorrectionName(String correctionName) {
        this.correctionName = correctionName == null ? null : correctionName.trim();
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc == null ? null : desc.trim();
    }

    public String getExecValidateRule() {
        return execValidateRule;
    }

    public void setExecValidateRule(String execValidateRule) {
        this.execValidateRule = execValidateRule == null ? null : execValidateRule.trim();
    }

    public Byte getIsValid() {
        return isValid;
    }

    public void setIsValid(Byte isValid) {
        this.isValid = isValid;
    }

    public Date getUtcCreate() {
        return utcCreate;
    }

    public void setUtcCreate(Date utcCreate) {
        this.utcCreate = utcCreate;
    }

    public Date getUtcModified() {
        return utcModified;
    }

    public void setUtcModified(Date utcModified) {
        this.utcModified = utcModified;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator == null ? null : operator.trim();
    }

    public String getParentCorrectionCode() {
        return parentCorrectionCode;
    }

    public void setParentCorrectionCode(String parentCorrectionCode) {
        this.parentCorrectionCode = parentCorrectionCode;
    }
}