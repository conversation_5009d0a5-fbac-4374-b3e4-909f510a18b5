package com.payermax.operating.correction.core.dal.dao;

import com.payermax.operating.correction.core.dal.po.CorrectionOrderHandlerRecord;
import com.payermax.operating.correction.core.dal.po.CorrectionOrderHandlerRecordExample;
import com.payermax.operating.correction.core.dal.po.CorrectionOrderHandlerRecordKey;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CorrectionOrderHandlerRecordMapper {
    int deleteByPrimaryKey(CorrectionOrderHandlerRecordKey key);

    int insert(CorrectionOrderHandlerRecord record);

    int insertSelective(CorrectionOrderHandlerRecord record);

    List<CorrectionOrderHandlerRecord> selectByExampleWithBLOBs(CorrectionOrderHandlerRecordExample example);

    List<CorrectionOrderHandlerRecord> selectByExample(CorrectionOrderHandlerRecordExample example);

    CorrectionOrderHandlerRecord selectByPrimaryKey(CorrectionOrderHandlerRecordKey key);

    int updateByExampleSelective(@Param("record") CorrectionOrderHandlerRecord record, @Param("example") CorrectionOrderHandlerRecordExample example);

    int updateByExampleWithBLOBs(@Param("record") CorrectionOrderHandlerRecord record, @Param("example") CorrectionOrderHandlerRecordExample example);

    int updateByExample(@Param("record") CorrectionOrderHandlerRecord record, @Param("example") CorrectionOrderHandlerRecordExample example);

    int updateByPrimaryKeySelective(CorrectionOrderHandlerRecord record);

    int updateByPrimaryKeyWithBLOBs(CorrectionOrderHandlerRecord record);

    int updateByOptimisticLock(@Param("correctionNo") String correctionNo, @Param("strategyCode") String strategyCode,
                               @Param("handlerType") String handlerType, @Param("expect") String expect,
                               @Param("target") String target, @Param("processRequest") String processRequest);

    int patchUpdateFailedHandlerRecord(@Param("record") CorrectionOrderHandlerRecord correctionOrderHandlerRecord,@Param("expect") String expect,@Param("target") String target);

}