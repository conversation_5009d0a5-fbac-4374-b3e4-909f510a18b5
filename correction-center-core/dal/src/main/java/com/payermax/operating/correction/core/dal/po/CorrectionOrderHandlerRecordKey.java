package com.payermax.operating.correction.core.dal.po;

public class CorrectionOrderHandlerRecordKey {
    private String correctionNo;

    private String strategyCode;

    private String handlerType;

    private String processRequest;

    public String getCorrectionNo() {
        return correctionNo;
    }

    public void setCorrectionNo(String correctionNo) {
        this.correctionNo = correctionNo == null ? null : correctionNo.trim();
    }

    public String getStrategyCode() {
        return strategyCode;
    }

    public void setStrategyCode(String strategyCode) {
        this.strategyCode = strategyCode == null ? null : strategyCode.trim();
    }

    public String getHandlerType() {
        return handlerType;
    }

    public void setHandlerType(String handlerType) {
        this.handlerType = handlerType == null ? null : handlerType.trim();
    }

    public String getProcessRequest() {
        return processRequest;
    }

    public void setProcessRequest(String processRequest) {
        this.processRequest = processRequest;
    }
}