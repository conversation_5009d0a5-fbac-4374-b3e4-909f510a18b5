<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.operating.correction.core.dal.dao.CorrectionOperationStrategyInfoMapper">
    <resultMap id="BaseResultMap" type="com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyInfo">
        <id column="operation_strategy_code" jdbcType="VARCHAR" property="operationStrategyCode"/>
        <result column="operation_strategy_name" jdbcType="VARCHAR" property="operationStrategyName"/>
        <result column="extend_show" jdbcType="VARCHAR" property="extendShow"/>
        <result column="is_valid" jdbcType="TINYINT" property="isValid"/>
        <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate"/>
        <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="collection_show" jdbcType="VARCHAR" property="collectionShow"/>
    </resultMap>
    <sql id="Base_Column_List">
        operation_strategy_code, operation_strategy_name, extend_show, is_valid, utc_create,
    utc_modified, operator,collection_show
    </sql>

    <insert id="insertDupKeyUpdate"
            parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyInfo">
        insert into tb_correction_operation_strategy_info (operation_strategy_code, operation_strategy_name,
                                                           extend_show, is_valid, operator,collection_show)
        values (#{operationStrategyCode,jdbcType=VARCHAR}, #{operationStrategyName,jdbcType=VARCHAR},
                #{extendShow,jdbcType=VARCHAR}, #{isValid,jdbcType=TINYINT},
                #{operator,jdbcType=VARCHAR},#{collectionShow,jdbcType=VARCHAR}) ON DUPLICATE KEY
        UPDATE
                operation_strategy_name=values (operation_strategy_name),
                extend_show=values (extend_show),
                is_valid=values (is_valid),
                operator=values (operator),
                collection_show=values (collection_show)

    </insert>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_correction_operation_strategy_info
        where operation_strategy_code = #{operationStrategyCode,jdbcType=VARCHAR}
    </select>

    <select id="selectAllValid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_correction_operation_strategy_info
        where is_valid=1
    </select>

    <select id="selectPageRequest" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_correction_operation_strategy_info
        <where>
            <if test="isValid!=null and isValid !=0">
                is_valid=#{isValid,jdbcType=TINYINT}
            </if>
        </where>
        order by utc_create desc
    </select>
</mapper>