<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.operating.correction.core.dal.dao.CorrectionOrderInfoMapper">
    <resultMap id="BaseResultMap" type="com.payermax.operating.correction.core.dal.po.CorrectionOrderInfo">
        <id column="correction_no" jdbcType="VARCHAR" property="correctionNo"/>
        <result column="merchant_order_no" jdbcType="VARCHAR" property="merchantOrderNo"/>
        <result column="voucher_no" jdbcType="VARCHAR" property="voucherNo"/>
        <result column="voucher_type" jdbcType="VARCHAR" property="voucherType"/>
        <result column="req_voucher_info" jdbcType="VARCHAR" property="reqVoucherInfo"/>
        <result column="event_source" jdbcType="TINYINT" property="eventSource"/>
        <result column="sys_source" jdbcType="VARCHAR" property="sysSource"/>
        <result column="correction_code" jdbcType="VARCHAR" property="correctionCode"/>
        <result column="process_status" jdbcType="VARCHAR" property="processStatus"/>
        <result column="operation_strategy_code" jdbcType="VARCHAR" property="operationStrategyCode"/>
        <result column="res_voucher_info" jdbcType="VARCHAR" property="resVoucherInfo"/>
        <result column="currency" jdbcType="CHAR" property="currency"/>
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="memo" jdbcType="VARCHAR" property="memo"/>
        <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate"/>
        <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime"/>
        <result column="operation_correction_code" jdbcType="VARCHAR" property="operationCorrectionCode"/>
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode"/>
        <result column="res_voucher_no" jdbcType="VARCHAR" property="resVoucherNo"/>
        <result column="correction_remark" jdbcType="VARCHAR" property="correctionRemark"/>
        <result column="trade_type" jdbcType="VARCHAR" property="tradeType"/>
        <result column="reviewer" jdbcType="VARCHAR" property="reviewer"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.payermax.operating.correction.core.dal.po.CorrectionOrderInfo">
        <result column="extend_info" jdbcType="LONGVARCHAR" property="extendInfo"/>
        <result column="user_info" jdbcType="LONGVARCHAR" property="userInfo"/>
        <result column="original_redundant_info" jdbcType="LONGVARCHAR" property="originalRedundantInfo"/>
    </resultMap>
    <sql id="Base_Column_List">
        correction_no, merchant_order_no, voucher_no,voucher_type, req_voucher_info, event_source, sys_source,
    correction_code, process_status, operation_strategy_code, res_voucher_info, currency, 
    total_amount, memo, utc_create, utc_modified, operator,complete_time,operation_correction_code,channel_code,
    res_voucher_no,correction_remark,trade_type,reviewer
    </sql>
    <sql id="Blob_Column_List">
        extend_info,user_info,original_redundant_info
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from tb_correction_order_info
        where correction_no = #{correctionNo,jdbcType=VARCHAR}
    </select>
    <insert id="insertSelective" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOrderInfo">
        insert into tb_correction_order_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="correctionNo != null">
                correction_no,
            </if>
            <if test="merchantOrderNo != null">
                merchant_order_no,
            </if>
            <if test="voucherNo != null">
                voucher_no,
            </if>
            <if test="voucherType != null">
                voucher_type,
            </if>
            <if test="reqVoucherInfo != null">
                req_voucher_info,
            </if>
            <if test="eventSource != null">
                event_source,
            </if>
            <if test="sysSource != null">
                sys_source,
            </if>
            <if test="correctionCode != null">
                correction_code,
            </if>
            <if test="processStatus != null">
                process_status,
            </if>
            <if test="operationStrategyCode != null">
                operation_strategy_code,
            </if>
            <if test="resVoucherInfo != null">
                res_voucher_info,
            </if>
            <if test="currency != null">
                currency,
            </if>
            <if test="totalAmount != null">
                total_amount,
            </if>
            <if test="memo != null">
                memo,
            </if>
            <if test="utcCreate != null">
                utc_create,
            </if>
            <if test="utcModified != null">
                utc_modified,
            </if>
            <if test="operator != null">
                operator,
            </if>
            <if test="extendInfo != null">
                extend_info,
            </if>
            <if test="completeTime != null">
                complete_time,
            </if>
            <if test="operationCorrectionCode != null">
                operation_correction_code,
            </if>
            <if test="userInfo != null">
                user_info,
            </if>
            <if test="channelCode != null">
                channel_code,
            </if>
            <if test="resVoucherNo != null">
                res_voucher_no,
            </if>
            <if test="correctionRemark != null">
                correction_remark,
            </if>
            <if test="originalRedundantInfo != null">
                original_redundant_info,
            </if>
            <if test="tradeType != null">
                trade_type,
            </if>
            <if test="reviewer != null">
                reviewer,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="correctionNo != null">
                #{correctionNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantOrderNo != null">
                #{merchantOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="voucherNo != null">
                #{voucherNo,jdbcType=VARCHAR},
            </if>
            <if test="voucherType != null">
                #{voucherType,jdbcType=VARCHAR},
            </if>
            <if test="reqVoucherInfo != null">
                #{reqVoucherInfo,jdbcType=VARCHAR},
            </if>
            <if test="eventSource != null">
                #{eventSource,jdbcType=TINYINT},
            </if>
            <if test="sysSource != null">
                #{sysSource,jdbcType=VARCHAR},
            </if>
            <if test="correctionCode != null">
                #{correctionCode,jdbcType=VARCHAR},
            </if>
            <if test="processStatus != null">
                #{processStatus,jdbcType=VARCHAR},
            </if>
            <if test="operationStrategyCode != null">
                #{operationStrategyCode,jdbcType=VARCHAR},
            </if>
            <if test="resVoucherInfo != null">
                #{resVoucherInfo,jdbcType=VARCHAR},
            </if>
            <if test="currency != null">
                #{currency,jdbcType=CHAR},
            </if>
            <if test="totalAmount != null">
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="memo != null">
                #{memo,jdbcType=VARCHAR},
            </if>
            <if test="utcCreate != null">
                #{utcCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="utcModified != null">
                #{utcModified,jdbcType=TIMESTAMP},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
            <if test="extendInfo != null">
                #{extendInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="completeTime != null">
                #{completeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operationCorrectionCode != null">
                #{operationCorrectionCode,jdbcType=VARCHAR},
            </if>
            <if test="userInfo != null">
                #{userInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="channelCode != null">
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="resVoucherNo != null">
                #{resVoucherNo,jdbcType=VARCHAR},
            </if>
            <if test="correctionRemark != null">
                #{correctionRemark,jdbcType=VARCHAR},
            </if>
            <if test="originalRedundantInfo != null">
                #{originalRedundantInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="tradeType != null">
                #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="reviewer != null">
                #{reviewer,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOrderInfo">
        update tb_correction_order_info
        <set>
            <if test="merchantOrderNo != null">
                merchant_order_no = #{merchantOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="voucherNo != null">
                voucher_no = #{voucherNo,jdbcType=VARCHAR},
            </if>
            <if test="voucherType != null">
                voucher_type = #{voucherType,jdbcType=VARCHAR},
            </if>
            <if test="reqVoucherInfo != null">
                req_voucher_info = #{reqVoucherInfo,jdbcType=VARCHAR},
            </if>
            <if test="eventSource != null">
                event_source = #{eventSource,jdbcType=TINYINT},
            </if>
            <if test="sysSource != null">
                sys_source = #{sysSource,jdbcType=VARCHAR},
            </if>
            <if test="correctionCode != null">
                correction_code = #{correctionCode,jdbcType=VARCHAR},
            </if>
            <if test="processStatus != null">
                process_status = #{processStatus,jdbcType=VARCHAR},
            </if>
            <if test="operationStrategyCode != null">
                operation_strategy_code = #{operationStrategyCode,jdbcType=VARCHAR},
            </if>
            <if test="resVoucherInfo != null">
                res_voucher_info = #{resVoucherInfo,jdbcType=VARCHAR},
            </if>
            <if test="currency != null">
                currency = #{currency,jdbcType=CHAR},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="memo != null">
                memo = #{memo,jdbcType=VARCHAR},
            </if>
            <if test="utcCreate != null">
                utc_create = #{utcCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="utcModified != null">
                utc_modified = #{utcModified,jdbcType=TIMESTAMP},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="extendInfo != null">
                extend_info = #{extendInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="completeTime != null">
                complete_time = #{completeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="operationCorrectionCode != null">
                operation_correction_code = #{operationCorrectionCode,jdbcType=VARCHAR},
            </if>
            <if test="userInfo != null">
                user_info = #{userInfo,jdbcType=LONGVARCHAR},
            </if>
            <if test="channelCode != null">
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="resVoucherNo != null">
                res_voucher_no = #{resVoucherNo,jdbcType=VARCHAR},
            </if>
            <if test="tradeType != null">
                trade_type = #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="reviewer != null">
                reviewer = #{reviewer,jdbcType=VARCHAR},
            </if>
        </set>
        where correction_no = #{correctionNo,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs"
            parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOrderInfo">
        update tb_correction_order_info
        set merchant_order_no       = #{merchantOrderNo,jdbcType=VARCHAR},
            voucher_no              = #{voucherNo,jdbcType=VARCHAR},
            voucher_type            = #{voucherType,jdbcType=VARCHAR},
            req_voucher_info        = #{reqVoucherInfo,jdbcType=VARCHAR},
            event_source            = #{eventSource,jdbcType=TINYINT},
            sys_source              = #{sysSource,jdbcType=VARCHAR},
            correction_code         = #{correctionCode,jdbcType=VARCHAR},
            process_status          = #{processStatus,jdbcType=VARCHAR},
            operation_strategy_code = #{operationStrategyCode,jdbcType=VARCHAR},
            res_voucher_info        = #{resVoucherInfo,jdbcType=VARCHAR},
            currency                = #{currency,jdbcType=CHAR},
            total_amount            = #{totalAmount,jdbcType=DECIMAL},
            memo                    = #{memo,jdbcType=VARCHAR},
            utc_create              = #{utcCreate,jdbcType=TIMESTAMP},
            utc_modified            = #{utcModified,jdbcType=TIMESTAMP},
            operator                = #{operator,jdbcType=VARCHAR},
            extend_info             = #{extendInfo,jdbcType=LONGVARCHAR},
            complete_time           = #{completeTime,jdbcType=TIMESTAMP},
            operation_correction_code = #{operationCorrectionCode,jdbcType=VARCHAR},
            user_info               = #{userInfo,jdbcType=LONGVARCHAR},
            channel_code            = #{channelCode,jdbcType=VARCHAR},
            res_voucher_no          = #{resVoucherNo,jdbcType=VARCHAR},
            trade_type              = #{tradeType,jdbcType=VARCHAR},
            reviewer                = #{reviewer,jdbcType=VARCHAR}
            where correction_no = #{correctionNo,jdbcType=VARCHAR}
    </update>
    <update id="updateIrrelevantInfo"
            parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOrderInfo">
        update tb_correction_order_info
        set merchant_order_no       = #{merchantOrderNo,jdbcType=VARCHAR},
            sys_source              = #{sysSource,jdbcType=VARCHAR},
            channel_code            = #{channelCode,jdbcType=VARCHAR},
            correction_remark       = #{correctionRemark,jdbcType=VARCHAR},
            original_redundant_info = #{originalRedundantInfo,jdbcType=LONGVARCHAR}
        where correction_no = #{correctionNo,jdbcType=VARCHAR}
    </update>

    <update id="updateByOptimisticLock">
        update tb_correction_order_info
        <set>
            <if test="target != null">
                process_status=#{target,jdbcType=VARCHAR},
            </if>
            <if test="completeTime != null">
                complete_time = #{completeTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        where correction_no=#{correctionNo,jdbcType=VARCHAR} and process_status=#{expect,jdbcType=VARCHAR}
    </update>

    <select id="selectPageRequest" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from tb_correction_order_info
        <where>
            <if test="queryOrderInfo.correctionNo !=null and queryOrderInfo.correctionNo!=''">
                and correction_no=#{queryOrderInfo.correctionNo,jdbcType=VARCHAR}
            </if>
            <if test="queryOrderInfo.correctionCode !=null and queryOrderInfo.correctionCode!=''">
                and correction_code=#{queryOrderInfo.correctionCode,jdbcType=VARCHAR}
            </if>
            <if test="queryOrderInfo.startTime != null">
                and utc_create &gt;=#{queryOrderInfo.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="queryOrderInfo.endTime != null">
                and utc_create &lt;=#{queryOrderInfo.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="queryOrderInfo.voucherNo !=null and queryOrderInfo.voucherNo!=''">
                and voucher_no like concat(#{queryOrderInfo.voucherNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="queryOrderInfo.merchantOrderNo !=null and queryOrderInfo.merchantOrderNo!=''">
                and merchant_order_no=#{queryOrderInfo.merchantOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="queryOrderInfo.processStatus !=null and queryOrderInfo.processStatus.size>0">
                and process_status in
                <foreach collection="queryOrderInfo.processStatus" item="item" index="index" separator="," open=" (" close=" )">
                    #{item}
                </foreach>
            </if>
            <if test="queryOrderInfo.operationCorrectionCode !=null and queryOrderInfo.operationCorrectionCode!=''">
                and operation_correction_code=#{queryOrderInfo.operationCorrectionCode,jdbcType=VARCHAR}
            </if>
            <if test="queryOrderInfo.channelCode !=null and queryOrderInfo.channelCode!=''">
                and channel_code like concat('%',#{queryOrderInfo.channelCode,jdbcType=VARCHAR},'%')
            </if>
            <if test="queryOrderInfo.operator !=null and queryOrderInfo.operator!=''">
                and operator like concat('%',#{queryOrderInfo.operator,jdbcType=VARCHAR},'%')
            </if>
            <if test="queryOrderInfo.redundantInfo !=null and queryOrderInfo.redundantInfo !=''">
                and (MATCH(original_redundant_info)  AGAINST(#{queryOrderInfo.redundantInfo,jdbcType=VARCHAR} IN BOOLEAN MODE))
            </if>
            <if test="queryOrderInfo.reviewer !=null and queryOrderInfo.reviewer!=''">
                and ( reviewer = #{queryOrderInfo.reviewer,jdbcType=VARCHAR} and process_status in
                ('REVIEWED','VERIFICATION','REVIEWING'))
            </if>
            <if test="queryOrderInfo.tradeType !=null and queryOrderInfo.tradeType!=''">
                and trade_type = #{queryOrderInfo.tradeType,jdbcType=VARCHAR}
            </if>
            <if test="queryOrderInfo.sysSource !=null and queryOrderInfo.sysSource.size >0">
                and
                <if test="queryOrderInfo.sysSource.size==1">
                    <foreach collection="queryOrderInfo.sysSource" item="item" index="index" separator=" or" open=" (" close=" )">
                        LOCATE(#{item},sys_source)>0
                    </foreach>
                </if>
                <if test="queryOrderInfo.sysSource.size>1">
                    <foreach collection="queryOrderInfo.sysSource" item="item" index="index" separator=" and " open=" (" close=" )">
                        LOCATE(#{item},sys_source)>0
                    </foreach>
                </if>

            </if>
        </where>
        order by utc_create desc
    </select>
    <select id="selectPageRequestList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from tb_correction_order_info
        <where>
            process_status='SUCCESS'
            <if test="correctionNos !=null and correctionNos.size >0">
                and correction_no in
                <foreach collection="correctionNos" item="item" index="index" separator=" ," open=" (" close=" )">
                    #{item}
                </foreach>
            </if>
            <if test="voucherNos !=null and voucherNos.size >0">
                and voucher_no in
                <foreach collection="voucherNos" item="item" index="index" separator=" ," open=" (" close=" )">
                    #{item}
                </foreach>
            </if>
        </where>
        order by utc_create desc
    </select>

    <select id="selectByUniqueKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from tb_correction_order_info
        <where>
            <if test="voucherUniqueInfo.correctionCode!=null and voucherUniqueInfo.correctionCode!=''">
                and correction_code=#{voucherUniqueInfo.correctionCode,jdbcType=VARCHAR}
            </if>
            <if test="voucherUniqueInfo.voucherNo!=null and voucherUniqueInfo.voucherNo!=''">
                and voucher_no = #{voucherUniqueInfo.voucherNo,jdbcType=VARCHAR}
            </if>
            <if test="voucherUniqueInfo.resVoucherNo!=null and voucherUniqueInfo.resVoucherNo!=''">
                and res_voucher_no = #{voucherUniqueInfo.resVoucherNo,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="selectByCheckingOrder" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from tb_correction_order_info
        <where>
            trade_type = #{tradeType,jdbcType=VARCHAR}
            and correction_code = #{correctionCode,jdbcType=VARCHAR}
            and process_status = #{processStatus,jdbcType=VARCHAR}
            and channel_code like concat(#{channelCode,jdbcType=VARCHAR},'%')
            and (MATCH(original_redundant_info)  AGAINST(#{originalRedundantInfo,jdbcType=VARCHAR} IN BOOLEAN MODE))
        </where>
    </select>
</mapper>