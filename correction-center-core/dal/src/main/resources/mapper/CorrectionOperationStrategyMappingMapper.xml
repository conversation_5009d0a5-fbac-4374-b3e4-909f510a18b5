<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.operating.correction.core.dal.dao.CorrectionOperationStrategyMappingMapper">
  <resultMap id="BaseResultMap" type="com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyMapping">
    <id column="operation_strategy_code" jdbcType="VARCHAR" property="operationStrategyCode" />
    <id column="correction_code" jdbcType="VARCHAR" property="correctionCode" />
    <id column="trade_type" jdbcType="VARCHAR" property="tradeType" />
    <result column="is_valid" jdbcType="TINYINT" property="isValid" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    operation_strategy_code, correction_code,trade_type, is_valid, utc_create, utc_modified, operator
  </sql>
  <select id="selectByExample" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyMappingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_correction_operation_strategy_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyMappingKey" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_correction_operation_strategy_mapping
    where operation_strategy_code = #{operationStrategyCode,jdbcType=VARCHAR}
      and correction_code = #{correctionCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyMappingKey">
    delete from tb_correction_operation_strategy_mapping
    where operation_strategy_code = #{operationStrategyCode,jdbcType=VARCHAR}
      and correction_code = #{correctionCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyMapping">
    insert into tb_correction_operation_strategy_mapping (operation_strategy_code, correction_code, 
      is_valid, utc_create, utc_modified, 
      operator)
    values (#{operationStrategyCode,jdbcType=VARCHAR}, #{correctionCode,jdbcType=VARCHAR}, 
      #{isValid,jdbcType=TINYINT}, #{utcCreate,jdbcType=TIMESTAMP}, #{utcModified,jdbcType=TIMESTAMP}, 
      #{operator,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyMapping">
    insert into tb_correction_operation_strategy_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operationStrategyCode != null">
        operation_strategy_code,
      </if>
      <if test="correctionCode != null">
        correction_code,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="isValid != null">
        is_valid,
      </if>
      <if test="utcCreate != null">
        utc_create,
      </if>
      <if test="utcModified != null">
        utc_modified,
      </if>
      <if test="operator != null">
        operator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="operationStrategyCode != null">
        #{operationStrategyCode,jdbcType=VARCHAR},
      </if>
      <if test="correctionCode != null">
        #{correctionCode,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=VARCHAR},
      </if>
      <if test="isValid != null">
        #{isValid,jdbcType=TINYINT},
      </if>
      <if test="utcCreate != null">
        #{utcCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="utcModified != null">
        #{utcModified,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update tb_correction_operation_strategy_mapping
    <set>
      <if test="record.operationStrategyCode != null">
        operation_strategy_code = #{record.operationStrategyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.correctionCode != null">
        correction_code = #{record.correctionCode,jdbcType=VARCHAR},
      </if>
      <if test="record.isValid != null">
        is_valid = #{record.isValid,jdbcType=TINYINT},
      </if>
      <if test="record.utcCreate != null">
        utc_create = #{record.utcCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.utcModified != null">
        utc_modified = #{record.utcModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tb_correction_operation_strategy_mapping
    set operation_strategy_code = #{record.operationStrategyCode,jdbcType=VARCHAR},
      correction_code = #{record.correctionCode,jdbcType=VARCHAR},
      is_valid = #{record.isValid,jdbcType=TINYINT},
      utc_create = #{record.utcCreate,jdbcType=TIMESTAMP},
      utc_modified = #{record.utcModified,jdbcType=TIMESTAMP},
      operator = #{record.operator,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyMapping">
    update tb_correction_operation_strategy_mapping
    <set>
      <if test="isValid != null">
        is_valid = #{isValid,jdbcType=TINYINT},
      </if>
      <if test="utcCreate != null">
        utc_create = #{utcCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="utcModified != null">
        utc_modified = #{utcModified,jdbcType=TIMESTAMP},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
    </set>
    where operation_strategy_code = #{operationStrategyCode,jdbcType=VARCHAR}
      and correction_code = #{correctionCode,jdbcType=VARCHAR}
      and trade_type= #{tradeType,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyMapping">
    update tb_correction_operation_strategy_mapping
    set is_valid = #{isValid,jdbcType=TINYINT},
      utc_create = #{utcCreate,jdbcType=TIMESTAMP},
      utc_modified = #{utcModified,jdbcType=TIMESTAMP},
      operator = #{operator,jdbcType=VARCHAR}
    where operation_strategy_code = #{operationStrategyCode,jdbcType=VARCHAR}
      and correction_code = #{correctionCode,jdbcType=VARCHAR}
      and trade_type= #{tradeType,jdbcType=VARCHAR}
  </update>
  <insert id="batchInsert">
    insert into tb_correction_operation_strategy_mapping
    (operation_strategy_code, correction_code, is_valid,operator,trade_type)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.operationStrategyCode,jdbcType=VARCHAR}, #{item.correctionCode,jdbcType=VARCHAR},
      #{item.isValid,jdbcType=TINYINT}, #{item.operator,jdbcType=VARCHAR}, #{item.tradeType,jdbcType=VARCHAR})
    </foreach>
    ON DUPLICATE KEY UPDATE
    is_valid =values (is_valid),operator =values (operator)
  </insert>
</mapper>