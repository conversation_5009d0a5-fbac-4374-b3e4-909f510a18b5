<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.operating.correction.core.dal.dao.CorrectionBasicInfoMapper">
    <resultMap id="BaseResultMap" type="com.payermax.operating.correction.core.dal.po.CorrectionBasicInfo">
        <id column="correction_code" jdbcType="VARCHAR" property="correctionCode"/>
        <result column="trade_type" jdbcType="VARCHAR" property="tradeType"/>
        <result column="correction_name" jdbcType="VARCHAR" property="correctionName"/>
        <result column="desc" jdbcType="VARCHAR" property="desc"/>
        <result column="exec_validate_rule" jdbcType="VARCHAR" property="execValidateRule"/>
        <result column="is_valid" jdbcType="TINYINT" property="isValid"/>
        <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate"/>
        <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified"/>
        <result column="operator" jdbcType="VARCHAR" property="operator"/>
        <result column="parent_correction_code" jdbcType="VARCHAR" property="parentCorrectionCode"/>
    </resultMap>

    <sql id="Base_Column_List">
        correction_code, trade_type, correction_name, `desc`, exec_validate_rule, is_valid,
    utc_create, utc_modified, operator,parent_correction_code
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_correction_basic_info
        where correction_code = #{correctionCode,jdbcType=VARCHAR}
    </select>
    <insert id="insertDupKeyUpdate" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionBasicInfo">
        insert into tb_correction_basic_info (correction_code, trade_type, correction_name,
                                              `desc`,exec_validate_rule, is_valid, operator,parent_correction_code)
        values (#{correctionCode,jdbcType=VARCHAR}, #{tradeType,jdbcType=VARCHAR}, #{correctionName,jdbcType=VARCHAR},
                #{desc,jdbcType=VARCHAR},#{execValidateRule,jdbcType=VARCHAR}, #{isValid,jdbcType=TINYINT}, #{operator,jdbcType=VARCHAR}, #{parentCorrectionCode,jdbcType=VARCHAR})
        ON DUPLICATE KEY UPDATE
                trade_type=values (trade_type),correction_name= values (correction_name),`desc`= values (`desc`),
                exec_validate_rule=values (exec_validate_rule),is_valid= values (is_valid),
                operator= values (operator),parent_correction_code= values (parent_correction_code)
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.payermax.operating.correction.core.dal.po.CorrectionBasicInfo">
        update tb_correction_basic_info
        <set>
            <if test="tradeType != null">
                trade_type = #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="correctionName != null">
                correction_name = #{correctionName,jdbcType=VARCHAR},
            </if>
            <if test="desc != null">
                desc = #{desc,jdbcType=VARCHAR},
            </if>
            <if test="execValidateRule != null">
                exec_validate_rule = #{execValidateRule,jdbcType=VARCHAR},
            </if>
            <if test="isValid != null">
                is_valid = #{isValid,jdbcType=TINYINT},
            </if>
            <if test="utcCreate != null">
                utc_create = #{utcCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="utcModified != null">
                utc_modified = #{utcModified,jdbcType=TIMESTAMP},
            </if>
            <if test="operator != null">
                operator = #{operator,jdbcType=VARCHAR},
            </if>
            <if test="parentCorrectionCode != null">
                parent_correction_code = #{parentCorrectionCode,jdbcType=VARCHAR},
            </if>
        </set>
        where correction_code = #{correctionCode,jdbcType=VARCHAR}
    </update>
    <select id="selectAllChildrenValid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_correction_basic_info
        where is_valid=1 and parent_correction_code <![CDATA[ <> ]]>''
    </select>
    <select id="selectAllParentValid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_correction_basic_info
        where is_valid=1 and parent_correction_code =''
    </select>

    <select id="selectPageRequest" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_correction_basic_info
        <where>
            parent_correction_code <![CDATA[ <> ]]>''
            <if test="isValid !=null and isValid !=0">
                and is_valid=#{isValid,jdbcType=TINYINT}
            </if>
        </where>
        order by utc_create desc
    </select>


    <select id="selectValidByParentCorrectionCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tb_correction_basic_info
        <where>
            is_valid = 1
            <if test="parentCorrectionCode !=null and parentCorrectionCode!=''">
                and parent_correction_code = #{parentCorrectionCode,jdbcType=VARCHAR}
            </if>
            <if test="tradeType !=null and tradeType!=''">
                and trade_type=#{tradeType,jdbcType=VARCHAR}
            </if>
        </where>
        order by utc_create desc
    </select>
</mapper>