<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.payermax.operating.correction.core.dal.dao.CorrectionOrderHandlerRecordMapper">
  <resultMap id="BaseResultMap" type="com.payermax.operating.correction.core.dal.po.CorrectionOrderHandlerRecord">
    <id column="correction_no" jdbcType="VARCHAR" property="correctionNo" />
    <id column="strategy_code" jdbcType="VARCHAR" property="strategyCode" />
    <id column="handler_type" jdbcType="VARCHAR" property="handlerType" />
    <result column="process_status" jdbcType="VARCHAR" property="processStatus" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="utc_create" jdbcType="TIMESTAMP" property="utcCreate" />
    <result column="utc_modified" jdbcType="TIMESTAMP" property="utcModified" />
    <result column="process_request" jdbcType="VARCHAR" property="processRequest" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.payermax.operating.correction.core.dal.po.CorrectionOrderHandlerRecord">
    <result column="process_result" jdbcType="LONGVARCHAR" property="processResult" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    correction_no, strategy_code, handler_type, process_status, operator, utc_create, 
    utc_modified,process_request
  </sql>
  <sql id="Blob_Column_List">
    process_result
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOrderHandlerRecordExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_correction_order_handler_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOrderHandlerRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_correction_order_handler_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOrderHandlerRecordKey" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_correction_order_handler_record
    where correction_no = #{correctionNo,jdbcType=VARCHAR}
      and strategy_code = #{strategyCode,jdbcType=VARCHAR}
      and handler_type = #{handlerType,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOrderHandlerRecordKey">
    delete from tb_correction_order_handler_record
    where correction_no = #{correctionNo,jdbcType=VARCHAR}
      and strategy_code = #{strategyCode,jdbcType=VARCHAR}
      and handler_type = #{handlerType,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOrderHandlerRecord">
    insert into tb_correction_order_handler_record (correction_no, strategy_code, handler_type, 
      process_status, operator, utc_create, 
      utc_modified, process_result,process_request)
    values (#{correctionNo,jdbcType=VARCHAR}, #{strategyCode,jdbcType=VARCHAR}, #{handlerType,jdbcType=VARCHAR}, 
      #{processStatus,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR}, #{utcCreate,jdbcType=TIMESTAMP}, 
      #{utcModified,jdbcType=TIMESTAMP}, #{processResult,jdbcType=LONGVARCHAR}, #{processRequest,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOrderHandlerRecord">
    insert into tb_correction_order_handler_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="correctionNo != null">
        correction_no,
      </if>
      <if test="strategyCode != null">
        strategy_code,
      </if>
      <if test="handlerType != null">
        handler_type,
      </if>
      <if test="processStatus != null">
        process_status,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="utcCreate != null">
        utc_create,
      </if>
      <if test="utcModified != null">
        utc_modified,
      </if>
      <if test="processResult != null">
        process_result,
      </if>
      <if test="processRequest != null">
        process_request,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="correctionNo != null">
        #{correctionNo,jdbcType=VARCHAR},
      </if>
      <if test="strategyCode != null">
        #{strategyCode,jdbcType=VARCHAR},
      </if>
      <if test="handlerType != null">
        #{handlerType,jdbcType=VARCHAR},
      </if>
      <if test="processStatus != null">
        #{processStatus,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="utcCreate != null">
        #{utcCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="utcModified != null">
        #{utcModified,jdbcType=TIMESTAMP},
      </if>
      <if test="processResult != null">
        #{processResult,jdbcType=LONGVARCHAR},
      </if>
      <if test="processRequest != null">
        #{processRequest,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update tb_correction_order_handler_record
    <set>
      <if test="record.correctionNo != null">
        correction_no = #{record.correctionNo,jdbcType=VARCHAR},
      </if>
      <if test="record.strategyCode != null">
        strategy_code = #{record.strategyCode,jdbcType=VARCHAR},
      </if>
      <if test="record.handlerType != null">
        handler_type = #{record.handlerType,jdbcType=VARCHAR},
      </if>
      <if test="record.processStatus != null">
        process_status = #{record.processStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.utcCreate != null">
        utc_create = #{record.utcCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.utcModified != null">
        utc_modified = #{record.utcModified,jdbcType=TIMESTAMP},
      </if>
      <if test="record.processResult != null">
        process_result = #{record.processResult,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.processRequest != null">
        process_request = #{record.processRequest,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update tb_correction_order_handler_record
    set correction_no = #{record.correctionNo,jdbcType=VARCHAR},
      strategy_code = #{record.strategyCode,jdbcType=VARCHAR},
      handler_type = #{record.handlerType,jdbcType=VARCHAR},
      process_status = #{record.processStatus,jdbcType=VARCHAR},
      process_request = #{record.processRequest,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=VARCHAR},
      utc_create = #{record.utcCreate,jdbcType=TIMESTAMP},
      utc_modified = #{record.utcModified,jdbcType=TIMESTAMP},
      process_result = #{record.processResult,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tb_correction_order_handler_record
    set correction_no = #{record.correctionNo,jdbcType=VARCHAR},
      strategy_code = #{record.strategyCode,jdbcType=VARCHAR},
      handler_type = #{record.handlerType,jdbcType=VARCHAR},
      process_status = #{record.processStatus,jdbcType=VARCHAR},
      process_request = #{record.processRequest,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=VARCHAR},
      utc_create = #{record.utcCreate,jdbcType=TIMESTAMP},
      utc_modified = #{record.utcModified,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOrderHandlerRecord">
    update tb_correction_order_handler_record
    <set>
      <if test="processStatus != null">
        process_status = #{processStatus,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="utcCreate != null">
        utc_create = #{utcCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="utcModified != null">
        utc_modified = #{utcModified,jdbcType=TIMESTAMP},
      </if>
      <if test="processResult != null">
        process_result = #{processResult,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where correction_no = #{correctionNo,jdbcType=VARCHAR}
      and strategy_code = #{strategyCode,jdbcType=VARCHAR}
      and handler_type = #{handlerType,jdbcType=VARCHAR}
      and process_request = #{processRequest,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.payermax.operating.correction.core.dal.po.CorrectionOrderHandlerRecord">
    update tb_correction_order_handler_record
    set process_status = #{processStatus,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR},
      utc_create = #{utcCreate,jdbcType=TIMESTAMP},
      utc_modified = #{utcModified,jdbcType=TIMESTAMP},
      process_result = #{processResult,jdbcType=LONGVARCHAR}
    where correction_no = #{correctionNo,jdbcType=VARCHAR}
      and strategy_code = #{strategyCode,jdbcType=VARCHAR}
      and handler_type = #{handlerType,jdbcType=VARCHAR}
      and process_request = #{processRequest,jdbcType=VARCHAR}
  </update>
  <update id="updateByOptimisticLock">
    update tb_correction_order_handler_record
      set process_status=#{target,jdbcType=VARCHAR}
    where correction_no = #{correctionNo,jdbcType=VARCHAR}
      and strategy_code = #{strategyCode,jdbcType=VARCHAR}
      and handler_type = #{handlerType,jdbcType=VARCHAR}
      and process_request = #{processRequest,jdbcType=VARCHAR}
      and process_status=#{expect,jdbcType=VARCHAR}
    </update>

  <update id="patchUpdateFailedHandlerRecord">
      update tb_correction_order_handler_record
      set process_status=#{target,jdbcType=VARCHAR},
          process_request=#{record.processRequest,jdbcType=VARCHAR}
      where correction_no=#{record.correctionNo,jdbcType=VARCHAR}
        and strategy_code=#{record.strategyCode,jdbcType=VARCHAR}
        and process_status=#{expect,jdbcType=VARCHAR}
    </update>
</mapper>