<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <classPathEntry location="C:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/5.1.34/mysql-connector-java-5.1.34.jar" />

    <context id="mysqlGenerator" targetRuntime="MyBatis3">
        <!--不生成注释-->
        <commentGenerator>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>
        <!-- 配置数据库连接 -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="********************************************************************************************************************************************"
                        userId="dev"
                        password="ENC(N0-4_d65e56e54e4b0d8d7e6237826)"/>

        <!-- 指定javaBean生成的位置 -->
        <javaModelGenerator targetPackage="com.payermax.operating.correction.core.dal.po"
                            targetProject="src/main/java">
            <!-- 在targetPackage的基础上，根据数据库的schema再生成一层package，最终生成的类放在这个package下，默认为false -->
            <property name="enableSubPackages" value="true"/>
            <!-- 设置是否在getter方法中，对String类型字段调用trim()方法 -->
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!--指定sql映射文件生成的位置 -->
        <sqlMapGenerator targetPackage="mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- 指定dao接口生成的位置，mapper接口 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.payermax.operating.correction.core.dal.dao"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <table tableName="tb_correction_operation_strategy_mapping" domainObjectName="CorrectionOperationStrategyMappings"
               enableSelectByExample="true" enableCountByExample="false"
               enableDeleteByExample="false" enableUpdateByExample="true"/>
    </context>

</generatorConfiguration>