package com.payermax.operating.correction.core.common.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 运营手动填写信息
 * @date 2022/9/27
 */
@Data
@NoArgsConstructor
public class OperationManualFillIn {

    /**
     * 备注
     */
    private String memo;

    /**
     * 补充扩展信息
     */
    private String extendInfo;

    /**
     * 额外的用户所需信息（加密后的信息）
     */
    private String extraUserInfo;

    /**
     * 审批批语
     */
    private String approvalComments;

    public OperationManualFillIn(String memo, String extendInfo,String extraUserInfo) {
        this.memo = memo;
        this.extendInfo = extendInfo;
        this.extraUserInfo=extraUserInfo;
    }

    public OperationManualFillIn(String approvalComments) {
        this.approvalComments = approvalComments;
    }
}
