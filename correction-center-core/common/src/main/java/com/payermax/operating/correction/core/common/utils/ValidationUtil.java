package com.payermax.operating.correction.core.common.utils;

import com.payermax.operating.correction.core.common.constant.Symbols;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.groups.Default;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * javax.validation校验工具类
 *
 * <AUTHOR>
 * @date 2021/11/25 19:26
 */
@SuppressWarnings("unused")
public class ValidationUtil {

    /**
     * 校验器对象
     */
    private static Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 对象校验
     *
     * @param obj 待验证对象
     * @param <T>
     * @return ValidationResult
     * @see ValidationResult
     */
    public static <T> ValidationResult validate(T obj) {

        return processConstraintViolation(validator.validate(obj, Default.class));
    }

    /**
     * 对象指定属性校验
     *
     * @param obj          待验证对象
     * @param propertyName 待验证属性名称
     * @param <T>
     * @return ValidationResult
     * @see ValidationResult
     */
    public static <T> ValidationResult validateProperty(T obj, String propertyName) {

        return processConstraintViolation(validator.validateProperty(obj, propertyName, Default.class));
    }

    /**
     * 校验结果处理
     *
     * @param set
     * @param <T>
     * @return ValidationResult
     */
    private static <T> ValidationResult processConstraintViolation(Set<ConstraintViolation<T>> set) {

        ValidationResult result = new ValidationResult();

        Optional.ofNullable(set).ifPresent(constraintViolations -> {

            if (CollectionUtils.isNotEmpty(constraintViolations)) {

                result.setHasErrors(Boolean.TRUE);

                Map<String, String> errorMsg = new HashMap<>();

                constraintViolations.forEach(cv -> {
                    errorMsg.put(cv.getPropertyPath().toString(), cv.getMessage());
                });

                result.setErrorMsg(errorMsg);
            }
        });

        return result;
    }

    /**
     * 参数校验响应结果
     */
    @SuppressWarnings("unused")
    @ToString
    @Getter
    @Setter
    public static class ValidationResult {

        /**
         * 校验结果是否有错
         */
        private Boolean hasErrors = Boolean.FALSE;

        /**
         * 校验错误信息
         */
        private Map<String, String> errorMsg;

        public String toPrettyString() {

            return MapUtils.isNotEmpty(errorMsg) ? StringUtils.join(errorMsg.values().toArray(), Symbols.COMMA)
                    : StringUtils.EMPTY;
        }
    }
}
