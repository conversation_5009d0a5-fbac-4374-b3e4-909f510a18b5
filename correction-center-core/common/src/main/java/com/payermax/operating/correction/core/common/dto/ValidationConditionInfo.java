package com.payermax.operating.correction.core.common.dto;

import com.payermax.operating.correction.core.common.enums.TradeType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 校验规则条件
 * @date 2023/5/30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ValidationConditionInfo {
    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 产品码
     */
    private String productCode;

}
