package com.payermax.operating.correction.core.common.enums;

import com.payermax.operating.correction.core.common.utils.Nullable;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc 有效性枚举
 * @date 2022/9/20
 */
public enum ValidType {
    VALID((byte) 1),
    INVALID((byte) 2);

    ValidType(Byte val) {
        this.val = val;
    }

    @Getter
    private Byte val;

    public static ValidType getValid(Byte val) {
        return Arrays.stream(values()).filter(e -> e.getVal().equals(val)).findFirst().orElse(Nullable.getNullVal());//CHECKED 枚举匹配只会匹配一个
    }

    public static ValidType getValidByName(String name) {
        return StringUtils.isBlank(name) ? Nullable.getNullVal() : ValidType.valueOf(name);
    }
}
