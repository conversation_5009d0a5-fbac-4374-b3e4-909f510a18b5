package com.payermax.operating.correction.core.common.utils;

import org.springframework.context.ApplicationContext;

import java.util.Map;
import java.util.Optional;

/**
 * spring application context持有工具
 * 
 * <AUTHOR>
 * @date 2021/12/05 02:54
 */
public class AppContextHolder {

    /**
     * 根据唯一Bean名称与定义的类型获取Bean
     * 
     * @param applicationContext spring context上下文
     * @param uniqueBeanName 唯一Bean名称
     * @param definedType 指定类型
     * @param <T>
     * @return Optional<T>
     */
    public static <T> Optional<T> getBean(ApplicationContext applicationContext, String uniqueBeanName,
                                          Class<T> definedType) {

        return applicationContext.containsBean(uniqueBeanName)
                ? Optional.ofNullable(applicationContext.getBean(uniqueBeanName, definedType))
                : Optional.empty();
    }

    /**
     * 根据唯一Bean名称获取Bean
     * 
     * @param applicationContext spring context上下文
     * @param uniqueBeanName 唯一Bean名称
     * @return Optional<Object>
     */
    public static Optional<Object> getBean(ApplicationContext applicationContext, String uniqueBeanName) {

        return applicationContext.containsBean(uniqueBeanName)
                ? Optional.ofNullable(applicationContext.getBean(uniqueBeanName))
                : Optional.empty();
    }

    /**
     * 根据指定类型获取Bean集合
     * 
     * @param applicationContext spring context上下文
     * @param definedType 指定类型
     * @param <T>
     * @return Map<String, T>
     */
    public static <T> Map<String, T> getBeansByType(ApplicationContext applicationContext, Class<T> definedType) {

        return applicationContext.getBeansOfType(definedType);
    }
}
