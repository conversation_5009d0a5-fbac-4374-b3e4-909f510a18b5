package com.payermax.operating.correction.core.common.enums;

import com.payermax.operating.correction.core.common.utils.Nullable;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc 退款枚举
 * @date 2022/11/16
 */
public enum RefundStatusEnum {

    //退款枚举
    REFUND_ACCEPT("退款受理", "0"),

    CLEARING_PENDING("退款清分中", "1"),

    CLEARING_SUCCESS("退款清分成功", "2"),

    REFUND_PENDING("退款处理中", "3"),

    REFUND_FAIL("退款失败", "4"),

    REFUND_SUCCESS("退款成功", "5");

    RefundStatusEnum(String name, String val) {
        this.name = name;
        this.val = val;
    }

    @Getter
    private String name;

    @Getter
    private String val;


    public static RefundStatusEnum getByVal(String val) {
        return Arrays.stream(values()).filter(e -> e.getVal().equals(val)).findFirst().orElse(Nullable.getNullVal());//CHECKED 枚举匹配只会匹配一个
    }

    public static RefundStatusEnum getByName(String name) {
        return StringUtils.isBlank(name) ? Nullable.getNullVal() : RefundStatusEnum.valueOf(name);
    }
}
