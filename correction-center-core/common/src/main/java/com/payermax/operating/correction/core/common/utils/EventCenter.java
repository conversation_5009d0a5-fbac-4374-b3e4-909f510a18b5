package com.payermax.operating.correction.core.common.utils;

import com.google.common.eventbus.EventBus;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @desc 事件中心
 * @date 2023/5/21
 */
public class EventCenter {

    /**
     * memory storage map
     */
    private final static ConcurrentHashMap<String, EventBus> eventBusMap = new ConcurrentHashMap<>();

    /**
     * 添加事件总线对象
     *
     * @param name     总线名称
     * @param eventBus 事件总线对象
     * @return EventBus
     */
    public static EventBus addEventBus(String name, EventBus eventBus) {

        if (ObjectUtils.allNotNull(name, eventBus)) {

            eventBusMap.put(name, eventBus);

            return eventBus;
        }

        return Nullable.getNullVal();
    }

    /**
     * 根据事件总线名称获取EventBus对象
     *
     * @param name 总线名称
     * @return Optional<EventBus>
     */
    public static Optional<EventBus> get(String name) {

        return eventBusMap.containsKey(name) ? Optional.ofNullable(eventBusMap.get(name)) : Optional.empty();
    }

    /**
     * 发布事件
     *
     * @param name  总线名称
     * @param event 事件对象
     */
    public static void publish(String name, Object event) {

        get(name).ifPresent(eventBus -> eventBus.post(event));
    }

    /**
     * 发布事件
     *
     * @param eventBus 事件总线
     * @param event    事件对象
     */
    public static void publish(EventBus eventBus, Object event) {

        Optional.ofNullable(eventBus).ifPresent(eb -> eventBus.post(event));
    }

    /**
     * 移除总线事件监听
     *
     * @param name       总线名称
     * @param subscriber 事件监听器
     */
    public static void remove(String name, Object subscriber) {

        get(name).ifPresent(eventBus -> eventBus.unregister(subscriber));
    }
}
