package com.payermax.operating.correction.core.common.constant;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @desc 差错常量
 * @date 2022/9/20
 */
public class CorrectionConstant {

    public static final String STRATEGY_PROCESS_JSON = "strategy_process.json";

    public static final String CORRECTION_SYSTEM_JSON = "correction_system.json";

    public static final String MATCHER_RULE_JSON = "matcher_rule.json";

    public static final String AUTO_STRATEGY_RULE_JSON = "auto_strategy_rule.json";

    public static final String PAYOUTS_COUNTRY_JSON = "payouts_country.json";

    public static final String PAY_METHOD_MAPPING_JSON = "pay_method_mapping.json";

    public static final String DING_TALK_MAPPING_JSON = "ding_talk_mapping.json";

    public static final String STRATEGY_BLACKLIST_JSON = "strategy_blacklist.json";

    public static final String APPLICATION_PROPERTIES = "application.properties";

    public static final String ROCKETMQ = "rocketmq";

    public static final String CORRECTION_CENTER="correction-center";

    public static final Integer NUM_ONE = 1;

    public static final int NUM_TWO = 2;

    public static final String FULL_TEXT_PREFIX = "ERROR:";

    public static final Integer NUM_ZERO = 0;

    public static final Integer MINUS_ONE = -1;

    public static final Integer NUM_FIVE = 5;

    public static final Integer NUM_TEN = 10;

    public static final Integer NUM_THIRTY = 30;

    public static final Long NUM_ONE_HUNDRED = 100L;

    public static final Long NUM_FiVE_LONG = 5L;

    public static final String TOPIC_TAG_ASSET="ASSETX";

    public static final String CHANNEL_RECONCILE="CHANNEL-RECONCILE";


    public static final String TOPIC_TAG_CHANNEL="FIN-CHANNEL-EXCHANGE";

    public static final String SYSTEM_OPERATION="system";

    public static final String SYSTEM_HANDLER_DIRTY="system_dirty";

    public static final String CORRECTION_FLAG=":C";

    public static final String DELAY_CORRECTION_CODE="CC002,CC003,CC004";

    public static final String EXTERNAL_DIFFERENCE_CORRECTION_CODE="CC013,CC014,CC022";

    public static final String RETRY_CHANNEL_CODE="CC003";

    public static final String CORRECTION_CHANNEL="CC004";
    public static final String CORRECTION_CHANNEL_REPEATPAY="CC004_repeatPay";

    public static final String RESULT_VOUCHER_INFO = "CC005,";

    public static final String PAYOUTS_PAYMENT_METHODS = "10";

    public static final String PAYOUTS_USER_TYPE = "1";

    public static final String RPC_VERSION = "1.0";

    public static final String USER_INFO_NAME="USER_INFO_NAME";

    public static final String PAYOUTS_PRODUCT_CODE="2001";

    public static final String PAYOUTS_PAYEE_TYPE = "PERSONAL";

    public static final String ORIGINAL_CHANNEL_RETRY= "ORIGINAL_CHANNEL_RETRY";

    public static final String CORRECTION_CHANNEL_PROCESS= "CORRECTION_CHANNEL_PROCESS";

    public static final String ORIGINAL_CHANNEL_REFUND_RETRY_PROCESSOR="originalChannelRefundRetryProcessor";

    public static final String CORRECTION_PAYOUTS_PROCESSOR = "correctionPayoutsProcessor";

    public static final String BOUNCE_BACK_PROCESSOR = "bounceBackProcessor";

    public static final String RISK_BIZ_TYPE="01";

    public final static String SHARDING_KEY = "1002";

    public final static String INSTITUTION_OFFLINE_REFUND="INSTITUTION_OFFLINE_REFUND";

    public final static String CORRECTION_PAYOUTS="CORRECTION_PAYOUTS";

    public final static String REGISTRATION_PROCESS="registrationProcess";

    public final static String ABSTRACT_CHECKER="abstract_CHECKER";

    public final static List NULL_LIST= Lists.newArrayListWithCapacity(0);

    public final static String DEFAULT="DEFAULT";

    public final static String CONFIG_STRATEGY_BEAN="configStrategyMatcher";

    public final static String INTERNAL_UNILATERAL_BEAN="internalUnilateralMatcher";

    public final static String EXTERNAL_UNILATERAL_COMMIT_BEAN="externalUnilateralCommitMatcher";

    /**
     * 对账发现出款失败，已经退票
     */
    public final static String PAYOUT_BOUNCEBACK_BY_RECONCILE_MATCHER ="payoutBouncebackByReconcileMatcher";
    public final static String RECONCILE_PAYMENT_REPEAT_MATCHER ="reconcilePaymentRepeatMatcher";

    public final static String EXTERNAL_UNILATERAL_THIRD_BEAN="externalUnilateralThirdMatcher";

    public final static String TRANSFER_ALERT_TITLE = "差错事件流转通知";

    public final static String DATA_CENTER_ALERT_TITLE = "数据中心调用异常通知";

    public final static String TRANSFER_ALERT_MSG_KEY = "【差错事件流转】:";

    public final static String TRANSFER_ALERT_MSG="您已收到相关差错事件，差错原因：%s，\n由 %s 转交 %s ，\n请尽快前往差错平台个人池处理";

    public final static String DATA_CENTER_ERROR_MSG = "调用数据中心失败：凭证类型：%s，凭证号：%s \n，错误原因: %s， \n请研发介入排查";

    public final static String CORRECTION_TRANSFER_LOCK = "TRANSFER_LOCK_CORRECTION_NO_";

    public final static String SCHEDULED_PENDING_ADVANCE_LOCK = "SCHEDULED_PENDING_ADVANCE_LOCK";

    public final static String CORRECTION_EVENT_TRANSFER_NOTIFY_LOCK="TRANSFER_EVENT_NOTIFY:CORRECTION_CODE_%s:REVIEWER_%s:OPERATOR_%s";

    public final static String DATA_CENTER_ALERT_NOTIFY_LOCK="DATA_CENTER_ALERT_NOTIFY";

    public static final String LOG_TRACE_ID = "traceId";
    public static final String GATEWAY_PRODUCT_CODE = "1201";
    public static final String PAYLINK_PRODUCT_CODE = "1003";
    public static final String MERCHNAT_CODE = "MERCHANT";
    public static final String PAYMENT_METHOD_DISCOUNT = "DISCOUNT";/*营销支付方式*/
    /**
     * 数据中心doris核对语句
     */
    public static final String TEMPLATE_ID_PAYOUT_BOUNCEBACK_BY_RECONCILE = "CORRECTION_PAYOUT_BOUNCEBACK_BY_RECONCILE";

}
