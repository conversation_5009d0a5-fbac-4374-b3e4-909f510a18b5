package com.payermax.operating.correction.core.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 返回码常量
 *
 * <AUTHOR>
 * @date 2021/12/01 10:51
 */
@AllArgsConstructor
@Getter
public enum ReturnCode {

    /* 非法请求参数 */
    ILLEGAL_PARAMS("PARAMS_INVALID", "非法请求参数"),

    /* 重复请求 */
    DUPLICATE_ORDER("DUPLICATE_ORDER", "重复请求"),

    /* 业务异常 */
    BUSINESS_EXCEPTION("BIZ_SYSTEM_BUSY", "业务异常"),

    /* 配置异常 */
    CONFIG_ERROR("CONFIG_ERROR", "不支持的业务类型"),

    /* 超时异常 */
    TIMEOUT("TIMEOUT", "请求超时"),

    RPC_EXCEPTION("RPC_FAIL", "服务调用异常"),

    /* 执行器校验失败 */
    PROCESSOR_VALID_EXCEPTION("PROCESSOR_VALID_EXCEPTION", "执行器校验失败"),

    /* 执行器校验失败 */
    STATE_MACHINE_PROCESS_FAIL("STATE_MACHINE_PROCESS_FAIL", "状态机执行失败"),

    /* 系统异常 */
    SYS_EXCEPTION("SYSTEM_BUSY", "系统异常");

    private final String code;

    private final String msg;
}
