package com.payermax.operating.correction.core.common.enums;

import com.payermax.operating.correction.core.common.utils.Nullable;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @desc 用户所需信息枚举类
 * @date 2022/11/9
 */
public enum UserInfoEnum {
    OFFLINE_TRANSFER,
    PAYOUTS,
    INSTITUTION_REFUND,
    ;

    public static UserInfoEnum getByName(String name) {
        return StringUtils.isBlank(name) ? Nullable.getNullVal() : UserInfoEnum.valueOf(name);
    }
}
