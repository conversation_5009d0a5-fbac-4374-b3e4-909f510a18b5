package com.payermax.operating.correction.core.common.enums;

import com.payermax.operating.correction.core.common.utils.Nullable;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc 条件枚举
 * @date 2022/9/20
 */
public enum ConditionEnum {
    EQUAL("="),
    CONTAIN("U"),
    NOT_CONTAIN("!"),
    ALL("*");

    ConditionEnum(String symbol) {
        this.symbol = symbol;
    }

    public static ConditionEnum getBySymbol(String symbol) {
        return Arrays.stream(values()).filter(e -> e.getSymbol().equals(symbol)).findFirst().orElse(Nullable.getNullVal());//CHECKED 枚举校验只会匹配一个
    }

    public static ConditionEnum getByName(String name) {
        return StringUtils.isBlank(name) ? Nullable.getNullVal() : ConditionEnum.valueOf(name);
    }

    @Getter
    private String symbol;
}
