package com.payermax.operating.correction.core.common.dto;

import com.payermax.operating.correction.core.common.enums.TradeType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 差错基础信息
 * @date 2022/9/27
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CorrectionBaseInfo {

    /**
     * 差错单
     */
    private String correctionNo;

    /**
     * 差错code
     */
    private String correctionCode;

    /**
     * 凭证信息
     */
    private VoucherInfo voucherInfo;

    /**
     * 交易类型
     */
    private TradeType tradeType;

    /**
     * 差错详情描述
     */
    private String detailDesc;

    /**
     * 差错详情原因code
     */
    private String detailCode;

    public CorrectionBaseInfo(String correctionNo) {
        this.correctionNo = correctionNo;
    }

    public CorrectionBaseInfo(String correctionCode, VoucherInfo voucherInfo) {
        this.correctionCode = correctionCode;
        this.voucherInfo = voucherInfo;
    }

    public CorrectionBaseInfo(String correctionNo, String correctionCode, VoucherInfo voucherInfo) {
        this.correctionNo = correctionNo;
        this.correctionCode = correctionCode;
        this.voucherInfo = voucherInfo;
    }

}
