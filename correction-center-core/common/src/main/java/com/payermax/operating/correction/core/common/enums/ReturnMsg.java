package com.payermax.operating.correction.core.common.enums;

/**
 * <AUTHOR>
 * @desc 返回消息提提示
 * @date 2022/11/21
 */
public interface ReturnMsg {

    /* RPC描述 */

    String RPC_DATA_CENTER_EXCEPTION = "调用数据中心服务异常";

    String RPC_ORDER_CENTER_EXCEPTION = "调用订单中心中心服务异常";
    String RPC_MERCHANT_EXCEPTION = "调用商户服务异常";

    String RPC_PRODUCT_CENTER_EXCEPTION = "调用产品中心服务异常";

    String RPC_PAYOUT_FIELD_EXCEPTION = "调用出款服务属性信息异常";

    String RPC_SECURITY_EXCEPTION = "调用安全服务加解密异常";

    String RPC_ASSET_EXCHANGE_EXCEPTION = "调用资产交换差错出款异常";
    String RPC_ASSET_EXCEPTION = "调用资产交换补单异常";

    String RPC_CASHIER_CORE_EXCEPTION = "调用收银核心异常";

    String RPC_CHANNEL_EXCHANGE_EXCEPTION = "调用金融交换心异常";

    String RPC_RISK_VALIDATION_EXCEPTION = "风控校验事件失败";

    String RPC_VOUCHER_EXCEPTION = "调用凭证服务异常";


    /* 数据校验异常 */

    String INCORRECT_REVIEWER = "复核人不正确:";

    String INSUFFICIENT_PERMISSIONS = "操作权限错误";

    String INCORRECT_SENDER = "操作人信息不正确";

    String INVALID_CORRECTION_INFO = "请输入正确的凭证信息";

    String BEAN_IS_NOT_EXIST = "%s isn't exist";

    String OPERATION_DATA_EXCEPTION = "操作数据库失败";

    String CORRECTION_CODE_INVALID = "差错code无效";

    String TRADE_TYPE_INVALID = "交易类型无效";

    String TRADE_ORDER_INFO_INVALID = "交易链路信息无效";

    String CORRECTION_SYSTEM_INFO_INVALID = "差错系统信息无效";

    String STRATEGY_INFO_INVALID_IN_DB = "数据库中无该策略信息";

    String STRATEGY_INFO_INVALID_IN_NACOS = "nacos配置中无该策略信息";

    String CORRECTION_NO_INVALID = "差错订单无效";

    String PARENT_BASIC_INVALID = "请选择正确的父差错原因code";

    String HANDLER_STRATEGY_INVALID = "处理策略无效";
    String HANDLER_STRATEGY_BLACKLIST = "该场景下不允许选择此策略";

    String DO_NOT_HANDEL_HAVE_DISCOUNT_ORDER = "有营销的单据不能处理";

    String HANDLER_CORRECTION_CODE_INVALID = "差错子原因选择错误";

    String RECONCILE_INVALID = "对账信息无效";


    String PAYMENT_INSTANCE_INVALID = "支付实例信息无效";

    String PAYOUT_RULE_INVALID = "出款规则校验失败";

    String HANDLER_STRATEGY_CONFIG_ERROR = "处理策略配置错误";

    String PAYOUTS_USER_INFO_ERROR = "出款用户信息获取异常";

    String PAYOUTS_PAYMENT_INFO_ERROR = "出款支付信息获取异常";

    /* RPC返回信息异常 */

    String INTERFACE_TRADE_INFO_INVALID = "数据中心返回交易信息无效";

    String INTERFACE_REFUND_INFO_INVALID = "数据中心返回退款信息无效";

    String INTERFACE_ASSET_INFO_INVALID = "数据中心资产交换信息无效";

    String INTERFACE_CHANNEL_INFO_INVALID = "数据中心渠道信息无效";

    String PAYOUT_CASHIER_PRODUCT_NO_AND_COUNTRY_INVALID = "无效的国家信息和售卖编码";

    String ASSET_STATUS_MATCH_FAIL = "资产交换订单状态匹配失败";

    String ORDER_CENTER_TIMEOUT_STATUS_MATCH_FAIL = "订单中心状态超时关单匹配失败";

    String ORDER_CENTER_FAILURE_STATUS_MATCH_FAIL = "订单中心状态交易失败匹配失败";

    String CHANNEL_REQUEST_STATUS_MATCH_FAIL = "渠道请求单状态匹配失败";

    String CORRECTION_STATUS_MATCH_FAIL = "差错处理状态校验失败";

}
