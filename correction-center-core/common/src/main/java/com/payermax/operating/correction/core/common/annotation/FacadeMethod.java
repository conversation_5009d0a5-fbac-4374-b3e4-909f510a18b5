package com.payermax.operating.correction.core.common.annotation;

import com.payermax.operating.correction.core.common.validation.Validation;

import java.lang.annotation.*;

/**
 * 参数校验注解
 *
 * <AUTHOR>
 * @date 2021/12/02 20:35
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(value = { ElementType.METHOD })
public @interface FacadeMethod {

    /**
     * 扩展校验实现,可在使用validation的基础上再次进行针对性扩展
     * 
     * @return Class<Validation>
     */
    Class<Validation> extValidation() default Validation.class;
}
