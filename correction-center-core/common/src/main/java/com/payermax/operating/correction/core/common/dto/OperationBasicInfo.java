package com.payermax.operating.correction.core.common.dto;

import com.payermax.operating.correction.core.common.enums.ValidType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc 运营操作基础对象
 * @date 2022/9/20
 */
@Data
@NoArgsConstructor
public class OperationBasicInfo {

    /**
     * 操作人
     */
    private String operator;

    /**
     * 有效性
     */
    private ValidType valid;

    /**
     * 创建时间
     **/
    private Long utcCreate;

    /**
     * 更新时间
     **/
    private Long utcModified;

    /**
     * 处理完成时间
     */
    private Long completeTime;

    public OperationBasicInfo(String operator, Long utcCreate, Long utcModified) {
        this.operator = operator;
        this.utcCreate = utcCreate;
        this.utcModified = utcModified;
    }

    public OperationBasicInfo(String operator) {
        this.operator = operator;
    }

    public OperationBasicInfo(String operator, String valid) {
        this.operator = operator;
        this.valid = ValidType.valueOf(valid);
    }

    public OperationBasicInfo(String operator, ValidType valid, Long utcCreate, Long utcModified) {
        this.operator = operator;
        this.valid = valid;
        this.utcCreate = utcCreate;
        this.utcModified = utcModified;
    }

    public OperationBasicInfo(String operator, Long utcCreate, Long utcModified, Date completeTime) {
        this.operator = operator;
        this.utcCreate = utcCreate;
        this.utcModified = utcModified;
        this.completeTime = Optional.ofNullable(completeTime).map(Date::getTime).orElse(Nullable.getNullVal());
    }
}
