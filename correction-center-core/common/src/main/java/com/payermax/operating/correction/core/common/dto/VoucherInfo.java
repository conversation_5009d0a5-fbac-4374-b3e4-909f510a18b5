package com.payermax.operating.correction.core.common.dto;

import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @desc 凭证信息
 * @date 2022/9/28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VoucherInfo {

    /**
     * 订单凭证号
     */
    private String voucherNo;

    /**
     * 凭证类型
     */
    private DCVoucherType voucherType;

    @Override
    public String toString() {
        return "{" +
                "\"voucherNo\":\"" + voucherNo + "\"" +
                ", \"voucherType\":\"" + voucherType + "\"" +
                "}";
    }

    public String uniqueNo() {
        return StringUtils.defaultIfBlank(this.voucherNo, StringUtils.EMPTY) + Symbols.LINE + ObjectUtils.defaultIfNull(this.voucherType, StringUtils.EMPTY);
    }

}
