package com.payermax.operating.correction.core.common.enums;

import com.payermax.operating.correction.core.common.utils.Nullable;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @desc 交易类型(同资产交换交易步骤)
 * @date 2022/9/20
 */
public enum TradeType {
    REFUND("退款", "ER", "REFUND","REFUND","40"),
    PAYMENT("入款", "EP", "COLLECTION","PAYMENT","20"),
    PAYOUTS("出款", "EW", "PAYMENT","PAYOUT","10");

    TradeType(String desc, String voucherType, String assetPayType, String channelPaymentType,String paymentTypeValue) {
        this.desc = desc;
        this.voucherType = voucherType;
        this.assetPayType = assetPayType;
        this.channelPaymentType = channelPaymentType;
        this.paymentTypeValue=paymentTypeValue;
    }

    @Getter
    private String desc;

    @Getter
    private String voucherType;

    @Getter
    private String assetPayType;

    @Getter
    private String channelPaymentType;

    @Getter
    private String paymentTypeValue;

    public static TradeType getTradeType(String assetPayType) {
        return Arrays.stream(values()).filter(e -> e.getAssetPayType().equals(assetPayType)).findFirst().orElse(Nullable.getNullVal());//CHECKED 枚举匹配只会匹配一个
    }

    public static TradeType getTradeTypeByName(String name){
        return StringUtils.isBlank(name)?Nullable.getNullVal():TradeType.valueOf(name);
    }

}
