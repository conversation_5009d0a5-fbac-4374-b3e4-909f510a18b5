package com.payermax.operating.correction.core.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 商户信息
 * @date 2022/9/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MerchantInfo {

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 商户订单号
     */
    private String merOrderNo;

    /**
     * APP ID
     */
    private String merchantAppId;
    /**
     * 商户业务类型
     * 02 代表电商行业
     */
    private String bizType;


    public MerchantInfo(final String merchantNo, final String merOrderNo, final String merchantAppId) {
        this.merchantNo = merchantNo;
        this.merOrderNo = merOrderNo;
        this.merchantAppId = merchantAppId;
    }
}
