package com.payermax.operating.correction.core.common.utils;

/**
 * 空对象包装类
 *
 * <AUTHOR>
 * @date 2021/09/26 13:54
 */
@SuppressWarnings("unused")
public class Nullable {

    public static Nullable NULL = new Nullable();

    public static <T> T getNullVal() {
        return null;
    }

    public boolean isNull(Object obj) {
        return (this == obj || null == obj);
    }

    @Override
    public String toString() {
        return "null";
    }

    @Override
    public int hashCode() {
        return 0;
    }
}
