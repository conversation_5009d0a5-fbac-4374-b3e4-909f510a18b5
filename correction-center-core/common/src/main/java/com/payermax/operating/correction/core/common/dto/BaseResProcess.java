package com.payermax.operating.correction.core.common.dto;

import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 基础响应处理器信息
 * @date 2022/10/14
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BaseResProcess {

    private VoucherInfo resVoucherInfo;

    private String result;

    private CommonStatusEnum status;

    public BaseResProcess(VoucherInfo resVoucherInfo, String result) {
        this.resVoucherInfo = resVoucherInfo;
        this.result = result;
    }
}
