package com.payermax.operating.correction.core.common.constant;

/**
 * 符号常量定义
 *
 * <AUTHOR>
 * @date 2021/11/29 14:05
 */
@SuppressWarnings("unused")
public interface Symbols {

    /**
     * dot symbol
     */
    String DOT = ".";

    /**
     * underline symbol
     */
    String UNDERLINE = "_";

    /**
     * line symbol
     */
    String LINE = "-";

    /**
     * comma symbol
     */
    String COMMA = ",";

    /**
     * vertical symbol
     */
    String VERTICAL_LINE = "\\|";

    /**
     * special symbol
     */
    String STEP = "---->";
    /**
     * 金额占位符
     */
    String TEMPLATE_AMOUNT_PLACEHOLDER = "amount";

    /**
     * 币种占位符
     */
    String TEMPLATE_CURRENCY_PLACEHOLDER = "currency";

    /**
     * 商户会员号占位符
     */
    String TEMPLATE_MEMBER_ID_PLACEHOLDER = "memberId";

    /**
     * 换行符
     */
    String NEW_LINE = "\n";

    /**
     * HASHTAG符号
     */
    String HASHTAG = "#";

    String COLON = ":";

    String ASTERISK = "*";

    String ALERT = "@";

    String RES_SUCCESS = "APPLY_SUCCESS";

    String SYS_OPERATION="system";

    String ARRAY_STR="[]";

    String PARANTHESES_STR="{}";
}
