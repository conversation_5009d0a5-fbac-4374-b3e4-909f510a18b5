package com.payermax.operating.correction.core.common.enums;

import com.google.common.base.Splitter;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.utils.Nullable;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 与数据中心的凭证映射类型
 * @date 2022/9/27
 */
public enum DCVoucherType {
    TRADE_TOKEN_NO("交易Token", "r_trade_token_no", CorrectionConstant.NUM_ZERO, Boolean.FALSE),
    R_MERCHANT_ORDER_NO("商户订单号（收）", "funds_r_merchant_order_no", CorrectionConstant.NUM_ZERO, Boolean.TRUE),
    D_MERCHANT_ORDER_NO("商户订单号（付）", "funds_d_merchant_order_no", CorrectionConstant.NUM_ZERO, Boolean.FALSE),
    R_TRADE_ORDER_NO("交易订单号", "funds_r_trade_order_no", CorrectionConstant.NUM_ZERO, Boolean.FALSE),
    PAYOUTS_REQUEST_NO("出款订单号", "fundsd_payment_trade_no", CorrectionConstant.NUM_ZERO, Boolean.FALSE),
    PAY_REQUEST_NO("支付请求单号", "funds_r_pay_request_no", CorrectionConstant.NUM_ZERO, Boolean.FALSE),
    REFUND_TRADE_NO("退款交易订单号", "r_refund_trade_no", CorrectionConstant.NUM_ZERO, Boolean.FALSE),
    ASSET_PAY_ORDER("资产交换订单号", "funds_r_pay_order_no", CorrectionConstant.NUM_ZERO, Boolean.TRUE),
    CHANNEL_REQUEST_NO("渠道请求单号", "funds_r_channel_pay_request_no", CorrectionConstant.NUM_ZERO, Boolean.TRUE),
    CHANNEL_COMMIT_NO("渠道提交单号", "funds_r_channel_pay_commit_no", CorrectionConstant.NUM_ZERO, Boolean.TRUE),
    THIRD_ORDER_NO("三方机构单号", "funds_r_third_org_no", CorrectionConstant.NUM_ZERO, Boolean.FALSE),
    TRADE_RECON_NO("交易对账单", "r_trade_recon_no", CorrectionConstant.NUM_ZERO, Boolean.FALSE),
    COMMIT_AND_THIRD("渠道提交单号-三方单号", "funds_r_channel_pay_commit_no", CorrectionConstant.NUM_ONE, Boolean.TRUE),
    ;

    @Getter
    private String name;

    @Getter
    private String dcType;

    @Getter
    private int orderBy;

    @Getter
    private Boolean usedFlag;

    DCVoucherType(String name, String dcType, int orderBy, boolean usedFlag) {
        this.name = name;
        this.dcType = dcType;
        this.orderBy = orderBy;
        this.usedFlag = usedFlag;
    }

    public static DCVoucherType getVoucherType(String dcType) {
        return Arrays.stream(values()).filter(e -> e.getDcType().equals(dcType)).findAny().orElse(Nullable.getNullVal());
    }

    public static DCVoucherType getVoucherTypeByName(String name) {
        return StringUtils.isBlank(name) ? Nullable.getNullVal() : DCVoucherType.valueOf(name);
    }

    /**
     * left dcQuery OrderNo
     * right extraOrderNo
     */
    public static Pair<String, String> getVoucherPair(String orderNo, DCVoucherType voucherType) {
        if (DCVoucherType.COMMIT_AND_THIRD == voucherType) {
            //做兜底逻辑，避免抛出数组越界异常
            orderNo = new StringBuilder().append(orderNo).append(Symbols.LINE).toString();
            List<String> list = Splitter.on(Symbols.LINE).splitToList(orderNo);
            return Pair.of(list.get(CorrectionConstant.NUM_ZERO), list.get(CorrectionConstant.NUM_ONE));
        }
        return Pair.of(orderNo, orderNo);
    }

    public static String getExtraOrderNo(final List<String> list, DCVoucherType voucherType) {
        return list.get(voucherType.getOrderBy());
    }

}
