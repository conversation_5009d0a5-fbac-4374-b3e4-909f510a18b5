package com.payermax.operating.correction.core.common.dto;

import com.payermax.operating.correction.core.common.enums.ConditionEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 条件对象
 * @date 2023/5/30
 */
@Data
@NoArgsConstructor
public class ConditionInfo {

    private ConditionEnum condition;

    private String value;

    public ConditionInfo(String name, String value) {
        this.condition = ConditionEnum.getByName(name);
        this.value = value;
    }
}
