package com.payermax.operating.correction.core.common.enums;

import com.payermax.operating.correction.core.common.utils.Nullable;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @desc 运营操作类型
 * @date 2022/11/9
 */
public enum ExtendEnum {
    UPLOAD,
    PAYOUT,
    ;

    public static ExtendEnum getByName(String name) {
        return StringUtils.isBlank(name) ? Nullable.getNullVal() : ExtendEnum.valueOf(name);
    }
}
