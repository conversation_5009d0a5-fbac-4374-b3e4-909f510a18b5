package com.payermax.operating.correction.core.common.enums;

import com.payermax.common.lang.util.StringUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import lombok.Getter;

/**
 * <AUTHOR>
 * @desc 自动处理策略模式
 * @date 2023/8/30
 */
public enum StrategyMode {
    CONFIG(CorrectionConstant.CONFIG_STRATEGY_BEAN),
    INSTANCE_BEAN(StringUtil.EMPTY_STRING),

    ;
    @Getter
    private String beanName;

    StrategyMode(String beanName) {
        this.beanName = beanName;
    }
}
