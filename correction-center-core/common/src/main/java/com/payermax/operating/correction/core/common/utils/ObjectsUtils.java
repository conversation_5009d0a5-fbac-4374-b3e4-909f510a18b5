package com.payermax.operating.correction.core.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.payermax.common.lang.util.money.Money;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Currency;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 对象工具类
 * @date 2023/3/9
 */
public class ObjectsUtils {

    public static <T extends Enum> Boolean anyMatch(T compareVal, T... objs) {
        return Arrays.stream(objs).anyMatch(val -> val == compareVal);
    }

    public static List<String> strToList(String str, String symbol) {
        if (StringUtils.isBlank(str)) {
            return Lists.newArrayList();
        }
        return Splitter.on(symbol).splitToList(str);
    }

    public static String moneyToStr(Money money) {
        if (Objects.isNull(money)) {
            return StringUtils.EMPTY;
        }
        return JSONObject.toJSONString(money);
    }

    public static Money buildMoney(String amount, String currency) {
        if (StringUtils.isAnyBlank(amount, currency)) {
            return Nullable.getNullVal();
        }
        return new Money(amount, Currency.getInstance(currency));
    }

    public static Money buildMoney(BigDecimal amount, String currency) {
        if (StringUtils.isBlank(currency) || Objects.isNull(amount)) {
            return Nullable.getNullVal();
        }
        return new Money(amount, Currency.getInstance(currency));
    }
}
