package com.payermax.operating.correction.domainservice.processor.impl;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.dto.MerchantInfo;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domain.enums.SourceEnum;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.queue.MqResultNotifyRepository;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Currency;

import static org.junit.Assert.assertEquals;

@RunWith(PowerMockRunner.class) //用PowerMock方式进行运行该类（SpringRunner）
@PowerMockIgnore({"javax.management.*"})//或略javax下的一些类报错
public class AssetUnfreezeDeductProcessorTest {

    @Mock
    private MqResultNotifyRepository mockMqResultNotifyRepository;

    @InjectMocks
    private AssetUnfreezeDeductProcessor assetUnfreezeDeductProcessorUnderTest;

    @Test
    public void testExec() {
        // Setup
        final DomainCorrectionInfo correctionInfo = new DomainCorrectionInfo();
        correctionInfo.setCorrectionNo("correctionNo");
        correctionInfo.setVoucherNo("voucherNo");
        correctionInfo.setVoucherType(DCVoucherType.TRADE_TOKEN_NO);
        correctionInfo.setOriVoucherInfo(new VoucherInfo("voucherNo", DCVoucherType.TRADE_TOKEN_NO));
        correctionInfo.setResVoucherInfo(new VoucherInfo("voucherNo", DCVoucherType.TRADE_TOKEN_NO));
        correctionInfo.setMerchantInfo(new MerchantInfo("merchantNo", "merOrderNo", "merchantAppId"));
        correctionInfo.setSource(SourceEnum.MANUAL);
        correctionInfo.setSysSource("sysSource");
        correctionInfo.setPayTotalMoney(new Money(0L, 0, Currency.getInstance("USD")));
        correctionInfo.setProcessStatus(ProcessStatusEnum.PROCESSED);

        final BaseResProcess expectedResult = new BaseResProcess(new VoucherInfo("voucherNo", DCVoucherType.TRADE_TOKEN_NO), null);
        // Run the test
        final BaseResProcess result = assetUnfreezeDeductProcessorUnderTest.exec(correctionInfo);

        // Verify the results
        assertEquals(expectedResult, result);
    }

    @Test
    public void testSucHandler() {
        // Setup
        final GlobalOrderInfo globalOrderInfo = new GlobalOrderInfo();
        globalOrderInfo.setOriginalMerchantInfo(new MerchantInfo("merchantNo", "merOrderNo", "merchantAppId"));
        globalOrderInfo.setTradeInfo(new GlobalOrderInfo.TradeInfo(new GlobalOrderInfo.OrderInfo("orderNo", "status",null,null,null,null,null,null,null,true), new GlobalOrderInfo.OrderInfo("orderNo", "status",null,null,null,null,null,null,null,true), null, new GlobalOrderInfo.OrderInfo("orderNo", "status",null,null,null,null,null,null,null,true)));
        globalOrderInfo.setAssetInfo(new GlobalOrderInfo.AssetInfo(new GlobalOrderInfo.OrderInfo("orderNo", "status",null,null,null,null,null,null,null,true), new Money(0L, 0, Currency.getInstance("USD")), TradeType.REFUND));
        globalOrderInfo.setChannelInfo(new GlobalOrderInfo.ChannelInfo());
        globalOrderInfo.setProductCode("productCode");

        // Run the test
        final CommonStatusEnum result = assetUnfreezeDeductProcessorUnderTest.handlerResult(globalOrderInfo, null);

        // Verify the results
        assertEquals(CommonStatusEnum.PENDING, result);
    }
}
