package com.payermax.operating.correction.domainservice.matcher.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.payermax.operating.correction.core.common.dto.MerchantInfo;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.domainservice.matcher.AbstractMatcher;
import com.payermax.operating.correction.integration.config.nacos.model.Expression;
import com.payermax.operating.correction.integration.config.nacos.model.MatcherRule;
import com.payermax.operating.correction.integration.config.nacos.repository.ConfigRepository;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class) //用PowerMock方式进行运行该类（SpringRunner）
@PowerMockIgnore({"javax.management.*"})//或略javax下的一些类报错
public class CorrectionHandleMatchImplTest {

    @Mock
    private ConfigRepository mockConfigRepository;

    @InjectMocks
    private CorrectionHandleMatchImpl correctionHandleMatchImplUnderTest;

    @InjectMocks
    private AbstractMatcher abstractMatcher;

    @Test
    public void testMatchRule() {
        // Setup
        final GlobalOrderInfo globalOrderInfo = new GlobalOrderInfo();
        final MerchantInfo originalMerchantInfo = new MerchantInfo();
        originalMerchantInfo.setMerchantNo("123456789");
        originalMerchantInfo.setMerOrderNo("abcd13245");
        originalMerchantInfo.setMerchantAppId("merchantAppId");
        globalOrderInfo.setOriginalMerchantInfo(originalMerchantInfo);
        final GlobalOrderInfo.TradeInfo tradeInfo = new GlobalOrderInfo.TradeInfo();
        final GlobalOrderInfo.OrderInfo tradeOrder = new GlobalOrderInfo.OrderInfo();
        tradeOrder.setOrderNo("123456798");
        tradeOrder.setStatus("1");
        tradeInfo.setTradeOrder(tradeOrder);
        final GlobalOrderInfo.OrderInfo payRequest = new GlobalOrderInfo.OrderInfo();
        payRequest.setOrderNo("orderNo");
        payRequest.setStatus("status");
        tradeInfo.setPayRequest(payRequest);
        final GlobalOrderInfo.RefundOrderInfo refundInfo = new GlobalOrderInfo.RefundOrderInfo();
        final GlobalOrderInfo.OrderInfo refundOrder = new GlobalOrderInfo.OrderInfo();
        refundOrder.setOrderNo("orderNo");
        refundOrder.setStatus("status");
        refundInfo.setRefundOrder(refundOrder);
        tradeInfo.setRefundInfo(refundInfo);
        globalOrderInfo.setTradeInfo(tradeInfo);

        // Configure ConfigRepository.getRuleInfo(...).
        String rulesJson = "[\n" +
                "            {\n" +
                "                \"spel\":\"tradeInfo.tradeOrder.status\",\n" +
                "                \"rule\":{\n" +
                "                    \"1\":{\n" +
                "                        \"desc\":\"交易成功\",\n" +
                "                        \"childCorrectionCode\":\"CC001_tradeSuccess\"\n" +
                "                    },\n" +
                "                    \"2\":{\n" +
                "                        \"desc\":\"交易失败\",\n" +
                "                        \"childCorrectionCode\":\"CC001_redeliveryTradeFail_autoRefund\"\n" +
                "                    },\n" +
                "                    \"3\":{\n" +
                "                        \"desc\":\"支付中\",\n" +
                "                        \"childCorrectionCode\":\"CC001_tradePending\"\n" +
                "                    },\n" +
                "                    \"4\":{\n" +
                "                        \"desc\":\"交易关单\",\n" +
                "                        \"childCorrectionCode\":\"CC001_tradeClose\"\n" +
                "                    },\n" +
                "                    \"5\":{\n" +
                "                        \"desc\":\"交易成功\",\n" +
                "                        \"childCorrectionCode\":\"CC001_tradeSuccess\"\n" +
                "                    },\n" +
                "                    \"6\":{\n" +
                "                        \"desc\":\"交易成功\",\n" +
                "                        \"childCorrectionCode\":\"CC001_tradeSuccess\"\n" +
                "                    }\n" +
                "                }\n" +
                "            }\n" +
                "        ]";
        List<MatcherRule> matcherRuleInfo = JSONObject.parseObject(rulesJson, new TypeReference<List<MatcherRule>>() {
        });
        when(mockConfigRepository.getRuleInfo("CC001", TradeType.PAYMENT)).thenReturn(matcherRuleInfo);

        // Run the test
        final List<String> result = correctionHandleMatchImplUnderTest.matchRule("CC001", globalOrderInfo);
        System.out.println("childCorrectionCode:" + result.contains("CC001_tradeSuccess"));
    }


    @Test
    public void testNotMatchRule() {
        // Setup
        final GlobalOrderInfo globalOrderInfo = new GlobalOrderInfo();
        final MerchantInfo originalMerchantInfo = new MerchantInfo();
        originalMerchantInfo.setMerchantNo("123456789");
        originalMerchantInfo.setMerOrderNo("abcd13245");
        originalMerchantInfo.setMerchantAppId("merchantAppId");
        globalOrderInfo.setOriginalMerchantInfo(originalMerchantInfo);
        final GlobalOrderInfo.TradeInfo tradeInfo = new GlobalOrderInfo.TradeInfo();
        final GlobalOrderInfo.OrderInfo tradeOrder = new GlobalOrderInfo.OrderInfo();
        tradeOrder.setOrderNo("123456798");
        tradeOrder.setStatus("1");
        tradeInfo.setTradeOrder(tradeOrder);
        final GlobalOrderInfo.OrderInfo payRequest = new GlobalOrderInfo.OrderInfo();
        payRequest.setOrderNo("orderNo");
        payRequest.setStatus("status");
        tradeInfo.setPayRequest(payRequest);
        final GlobalOrderInfo.RefundOrderInfo refundInfo = new GlobalOrderInfo.RefundOrderInfo();
        final GlobalOrderInfo.OrderInfo refundOrder = new GlobalOrderInfo.OrderInfo();
        refundOrder.setOrderNo("orderNo");
        refundOrder.setStatus("status");
        refundInfo.setRefundOrder(refundOrder);
        tradeInfo.setRefundInfo(refundInfo);
        globalOrderInfo.setTradeInfo(tradeInfo);

        when(mockConfigRepository.getRuleInfo("CC002",TradeType.REFUND)).thenReturn(null);

        // Run the test
        final List<String> result = correctionHandleMatchImplUnderTest.matchRule("CC002", globalOrderInfo);
        // Verify the results
        assertEquals(null, result);
    }

    @Test
    public void testMatchRule_productCode() {
        // Setup
        final GlobalOrderInfo globalOrderInfo = new GlobalOrderInfo();
        final MerchantInfo originalMerchantInfo = new MerchantInfo();
        globalOrderInfo.setProductCode("1201");
        originalMerchantInfo.setMerchantNo("123456789");
        originalMerchantInfo.setMerOrderNo("abcd13245");
        originalMerchantInfo.setMerchantAppId("merchantAppId");
        globalOrderInfo.setOriginalMerchantInfo(originalMerchantInfo);
        final GlobalOrderInfo.TradeInfo tradeInfo = new GlobalOrderInfo.TradeInfo();
        final GlobalOrderInfo.OrderInfo tradeOrder = new GlobalOrderInfo.OrderInfo();
        tradeOrder.setOrderNo("123456798");
        tradeOrder.setStatus("1");
        tradeOrder.setProductCode("1201");
        tradeInfo.setTradeOrder(tradeOrder);
        final GlobalOrderInfo.OrderInfo payRequest = new GlobalOrderInfo.OrderInfo();
        payRequest.setOrderNo("orderNo");
        payRequest.setStatus("status");
        tradeInfo.setPayRequest(payRequest);
        final GlobalOrderInfo.RefundOrderInfo refundInfo = new GlobalOrderInfo.RefundOrderInfo();
        final GlobalOrderInfo.OrderInfo refundOrder = new GlobalOrderInfo.OrderInfo();
        refundOrder.setOrderNo("orderNo");
        refundOrder.setStatus("status");
        refundInfo.setRefundOrder(refundOrder);
        tradeInfo.setRefundInfo(refundInfo);
        globalOrderInfo.setTradeInfo(tradeInfo);

        List<Expression> list = new ArrayList<>();
        Expression expression = new Expression();
        expression.setConditionVal("1201");
        expression.setExp("tradeInfo?.tradeOrder?.productCode");
        expression.setSymbol("=");
        list.add(expression);


        Boolean aBoolean = abstractMatcher.strategyMatch(list, globalOrderInfo);

        assertTrue(aBoolean);


        // Run the test
        // Verify the results
    }




}
