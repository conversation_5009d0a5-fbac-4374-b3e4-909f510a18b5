package com.payermax.operating.correction.domainservice.checker.impl;

import com.payermax.operating.correction.core.common.dto.MerchantInfo;
import com.payermax.operating.correction.domainservice.checker.AbstractChecker;
import com.payermax.operating.correction.domainservice.matcher.CorrectionHandleMatch;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class) //用PowerMock方式进行运行该类（SpringRunner）
@PowerMockIgnore({"javax.management.*"})//或略javax下的一些类报错
public class OrgSuccessChannelFailCheckerTest {

    @Mock
    private CorrectionHandleMatch mockHandleMatch;

    @InjectMocks
    private AbstractChecker orgSuccessChannelFailCheckerUnderTest;

    @Test
    public void testCheck() {
        // Setup
        final CorrectionOrderInfoDTO correctionInfo = new CorrectionOrderInfoDTO("20230228020303CP1002100207000180");
        correctionInfo.setOperationCorrectionCode("CC001_tradePending");
        correctionInfo.setStrategyCode("correctionInfo");
        correctionInfo.getBaseInfo().setCorrectionCode("CC001");

        final GlobalOrderInfo globalOrderInfo = new GlobalOrderInfo();
        final MerchantInfo originalMerchantInfo = new MerchantInfo();
        originalMerchantInfo.setMerchantNo("merchantNo");
        originalMerchantInfo.setMerOrderNo("merOrderNo");
        originalMerchantInfo.setMerchantAppId("merchantAppId");
        globalOrderInfo.setOriginalMerchantInfo(originalMerchantInfo);
        final GlobalOrderInfo.TradeInfo tradeInfo = new GlobalOrderInfo.TradeInfo();
        final GlobalOrderInfo.OrderInfo tradeOrder = new GlobalOrderInfo.OrderInfo();
        tradeOrder.setOrderNo("orderNo");
        tradeOrder.setStatus("3");
        tradeInfo.setTradeOrder(tradeOrder);
        final GlobalOrderInfo.OrderInfo payRequest = new GlobalOrderInfo.OrderInfo();
        payRequest.setOrderNo("orderNo");
        payRequest.setStatus("status");
        tradeInfo.setPayRequest(payRequest);
        final GlobalOrderInfo.RefundOrderInfo refundInfo = new GlobalOrderInfo.RefundOrderInfo();
        final GlobalOrderInfo.OrderInfo refundOrder = new GlobalOrderInfo.OrderInfo();
        refundOrder.setOrderNo("orderNo");
        refundOrder.setStatus("status");
        refundInfo.setRefundOrder(refundOrder);
        tradeInfo.setRefundInfo(refundInfo);
        globalOrderInfo.setTradeInfo(tradeInfo);

        when(mockHandleMatch.matchRule(anyString(), any())).thenReturn(Arrays.asList("CC001_tradePending"));

        // Run the test
        orgSuccessChannelFailCheckerUnderTest.check(correctionInfo, globalOrderInfo,null);

        // Verify the results
    }

    @Test
    public void testCheck_CorrectionHandleMatchReturnsNoItems() {
        // Setup
        final CorrectionOrderInfoDTO correctionInfo = new CorrectionOrderInfoDTO("correctionNo");
        correctionInfo.getBaseInfo().setCorrectionCode("CC001");
        final GlobalOrderInfo globalOrderInfo = new GlobalOrderInfo();
        final MerchantInfo originalMerchantInfo = new MerchantInfo();
        originalMerchantInfo.setMerchantNo("merchantNo");
        originalMerchantInfo.setMerOrderNo("merOrderNo");
        originalMerchantInfo.setMerchantAppId("merchantAppId");
        globalOrderInfo.setOriginalMerchantInfo(originalMerchantInfo);
        final GlobalOrderInfo.TradeInfo tradeInfo = new GlobalOrderInfo.TradeInfo();
        final GlobalOrderInfo.OrderInfo tradeOrder = new GlobalOrderInfo.OrderInfo();
        tradeOrder.setOrderNo("orderNo");
        tradeOrder.setStatus("status");
        tradeInfo.setTradeOrder(tradeOrder);
        final GlobalOrderInfo.OrderInfo payRequest = new GlobalOrderInfo.OrderInfo();
        payRequest.setOrderNo("orderNo");
        payRequest.setStatus("status");
        tradeInfo.setPayRequest(payRequest);
        final GlobalOrderInfo.RefundOrderInfo refundInfo = new GlobalOrderInfo.RefundOrderInfo();
        final GlobalOrderInfo.OrderInfo refundOrder = new GlobalOrderInfo.OrderInfo();
        refundOrder.setOrderNo("orderNo");
        refundOrder.setStatus("status");
        refundInfo.setRefundOrder(refundOrder);
        tradeInfo.setRefundInfo(refundInfo);
        globalOrderInfo.setTradeInfo(tradeInfo);

        when(mockHandleMatch.matchRule("correctionCode", new GlobalOrderInfo())).thenReturn(Collections.emptyList());

        // Run the test
        orgSuccessChannelFailCheckerUnderTest.check(correctionInfo, globalOrderInfo,null);

        // Verify the results
    }
}
