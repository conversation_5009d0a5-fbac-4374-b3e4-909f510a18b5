package com.payermax.operating.correction.domainservice.factory;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.payermax.operating.correction.core.common.dto.MerchantInfo;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.*;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domainservice.assembler.CorrectionDomainServiceAssembler;
import com.payermax.operating.correction.domainservice.repository.impl.IDomainRepositoryImpl;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.config.nacos.model.CorrectionSystemInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.ReconcileRedundantDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.datacenter.repository.DataCenterRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class) //用PowerMock方式进行运行该类（SpringRunner）
@PowerMockIgnore({"javax.management.*"})//或略javax下的一些类报错
public class BizFactoryImplTest {

    @Mock
    private DataCenterRepository mockDataCenterRepository;
    @Mock
    private IDomainRepositoryImpl mockDomainRepository;
    @Mock
    private CorrectionDomainServiceAssembler mockDomainAssembler;

    @InjectMocks
    private BizFactoryImpl bizFactoryImplUnderTest;

    @Test
    public void testAddCorrectionEventPrepareFactory() {
        // Setup
        String operationDomainCorrectionInfoStr="{\"baseInfo\":{\"correctionCode\":\"CC003\",\"voucherInfo\":{\"voucherNo\":\"20230329094747DR3970071400461919001-46121354\",\"voucherType\":\"CHANNEL_REQUEST_NO\"},\"detailDesc\":\"testDesc\"},\"operator\":\"system\",\"payTotalMoney\":{\"currency\":\"KWD\"},\"processStatus\":\"PROCESSED\",\"source\":\"SYSTEM\",\"sysSource\":\"FIN-CHANNEL-EXCHANGE\"}";
        final OperationDomainCorrectionInfo correctionInfo = JSONObject.parseObject(operationDomainCorrectionInfoStr,new TypeReference<OperationDomainCorrectionInfo>(){
        });

        // Configure CorrectionDomainServiceAssembler.toDomainCorrection(...).
        final DomainCorrectionInfo domainCorrectionInfo = new DomainCorrectionInfo();
        domainCorrectionInfo.setCorrectionNo("20230329094805DF8914100200461930001");
        domainCorrectionInfo.setVoucherNo("20230329094747DR3970071400461919001-46121354");
        domainCorrectionInfo.setVoucherType(DCVoucherType.CHANNEL_REQUEST_NO);
        final VoucherInfo oriVoucherInfo1 = new VoucherInfo();
        oriVoucherInfo1.setVoucherNo("20230329094747DR3970071400461919001-46121354");
        oriVoucherInfo1.setVoucherType(DCVoucherType.CHANNEL_REQUEST_NO);
        domainCorrectionInfo.setOriVoucherInfo(oriVoucherInfo1);
        final MerchantInfo merchantInfo1 = new MerchantInfo();
        merchantInfo1.setMerchantNo("020113838535952");
        merchantInfo1.setMerOrderNo("RenshTest1676365383129");
        domainCorrectionInfo.setMerchantInfo(merchantInfo1);
        domainCorrectionInfo.setChannelCode("I_AMAZONPAY_NETWORK_S02");
        final CorrectionBasicInfoDTO basicInfoDTO1 = new CorrectionBasicInfoDTO();
        domainCorrectionInfo.setBasicInfoDTO(basicInfoDTO1);
        final CorrectionSystemInfo systemInfo1 = new CorrectionSystemInfo();
        domainCorrectionInfo.setSystemInfo(Lists.newArrayList(systemInfo1));
        final ReconcileRedundantDTO redundantInfo1 = new ReconcileRedundantDTO();
        redundantInfo1.setProductCode("1001");
        redundantInfo1.setMerchantNo("020113838535952");
        redundantInfo1.setRefundTypeSource("0");
        domainCorrectionInfo.setRedundantInfo(redundantInfo1);
        final GlobalOrderInfo globalOrderInfo1 = new GlobalOrderInfo();
        final MerchantInfo originalMerchantInfo1 = new MerchantInfo();
        originalMerchantInfo1.setMerchantNo("020113838535952");
        originalMerchantInfo1.setMerOrderNo("RenshTest1676365383129");
        globalOrderInfo1.setOriginalMerchantInfo(originalMerchantInfo1);
        final GlobalOrderInfo.TradeInfo tradeInfo1 = new GlobalOrderInfo.TradeInfo();
        final GlobalOrderInfo.RefundOrderInfo refundInfo1 = new GlobalOrderInfo.RefundOrderInfo();
        refundInfo1.setRefundType("0");
        tradeInfo1.setRefundInfo(refundInfo1);
        globalOrderInfo1.setTradeInfo(tradeInfo1);
        final GlobalOrderInfo.ChannelInfo channelInfo1 = new GlobalOrderInfo.ChannelInfo();
        final GlobalOrderInfo.ChannelCommitInfo lastChannelCommit1 = new GlobalOrderInfo.ChannelCommitInfo();
        lastChannelCommit1.setChannelCode("I_AMAZONPAY_NETWORK_S02");
        channelInfo1.setLastChannelCommit(lastChannelCommit1);
        globalOrderInfo1.setChannelInfo(channelInfo1);
        globalOrderInfo1.setProductCode("1001");
        domainCorrectionInfo.setGlobalOrderInfo(globalOrderInfo1);
        domainCorrectionInfo.setDetailDesc(correctionInfo.getBaseInfo().getDetailDesc());
        when(mockDomainAssembler.toDomainCorrection(any()))
                .thenReturn(domainCorrectionInfo);

        // Configure IDomainRepositoryImpl.getValidBasicInfo(...).
        final CorrectionBasicInfoDTO correctionBasicInfoDTO = new CorrectionBasicInfoDTO("CC003",
                TradeType.REFUND, "OperationDomainCorrectionInfo", "渠道退款失败",
               null,
                new OperationBasicInfo("ut", ValidType.VALID.name()), Arrays.asList(""),
                "");
        when(mockDomainRepository.getValidBasicInfo("CC003")).thenReturn(correctionBasicInfoDTO);

        when(mockDomainRepository.getValidCorrectionNo("20230329094747DR3970071400461919001-46121354", TradeType.REFUND,"CC001")).thenReturn("20230329094805DF8914100200461930001");

        // Configure IDomainRepositoryImpl.getValidCorrectionSys(...).
        final CorrectionSystemInfo correctionSystemInfo = new CorrectionSystemInfo("CC003", "FIN-CHANNEL-EXCHANGE",
                "",true);
        when(mockDomainRepository.getValidCorrectionSys("CC003", "FIN-CHANNEL-EXCHANGE"))
                .thenReturn(correctionSystemInfo);

        // Configure DataCenterRepository.dcGlobalQuery(...).
        String correctionGlobalOrderInfoStr="{\"correctionOrderInfo\":{},\"originalOrderInfo\":{\"assetInfo\":{\"assetOrder\":{\"orderNo\":\"20230329094747DR3970071400461919001\",\"status\":\"4\"},\"tradeType\":\"REFUND\"},\"bizIdentify\":\"P0101\",\"channelInfo\":{\"channelCommit\":[{\"channelCode\":\"I_AMAZONPAY_NETWORK_S02\",\"commitInfo\":{\"orderNo\":\"DRC07142616817015817851027097\",\"status\":\"2\"}}],\"channelRequest\":{\"orderNo\":\"20230329094747DR3970071400461919001-46121354\",\"status\":\"0\"},\"lastChannelCommit\":{\"$ref\":\"$.originalOrderInfo.channelInfo.channelCommit[0]\"}},\"originalMerchantInfo\":{\"merOrderNo\":\"RenshTest1676365383129\",\"merchantAppId\":\"bbd8d2639a7c4dfd8df7d005294390df\",\"merchantNo\":\"020113838535952\"},\"productCode\":\"1001\",\"tradeInfo\":{\"payRequest\":{\"orderNo\":\"20230329094747DR3970071400461919001\",\"status\":\"0\"},\"refundInfo\":{\"createTime\":\"2023-03-29 09:47:48.094642\",\"refundOrder\":{\"orderNo\":\"20230329094746MI3164634600461914001\",\"status\":\"3\"},\"refundType\":\"0\"},\"tradeOrder\":{\"orderNo\":\"20230214090307TI5644071400410175001\",\"status\":\"2\"}}}}\n";
        final GlobalOrderInfo correctionGlobalOrderInfo = JSONObject.parseObject(correctionGlobalOrderInfoStr,new TypeReference<GlobalOrderInfo>(){
        });
        when(mockDataCenterRepository.dcGlobalQuery(DCVoucherType.CHANNEL_REQUEST_NO, "20230329094747DR3970071400461919001-46121354"))
                .thenReturn(correctionGlobalOrderInfo);

        // Run the test
        final DomainCorrectionInfo result = bizFactoryImplUnderTest.addCorrectionEventPrepareFactory(correctionInfo);

    }

}
