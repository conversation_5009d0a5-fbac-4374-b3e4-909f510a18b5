package com.payermax.operating.correction;

import com.google.common.base.Splitter;
import com.payermax.operating.correction.core.common.constant.Symbols;
import org.junit.Test;
import org.junit.platform.commons.util.StringUtils;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.List;

@RunWith(PowerMockRunner.class) //用PowerMock方式进行运行该类（SpringRunner）
@PowerMockIgnore({"javax.management.*"})//或略javax下的一些类报错
public class PatchModifyScript {

    @Test
    public void tradeFail_2_Modify(){
        String script="curl --location --request POST 'http://127.0.0.1:7081/correction-center/controller/skipApprovalTest' \\\n" +
                "--header 'Content-Type: application/json' \\\n" +
                "--data-raw '{\n" +
                "    \"correctionNo\":\"%s\",\n" +
                "    \"strategyCode\":\"redeliveryTradeFail\",\n" +
                "    \"operationCorrectionCode\":\"CC001_redeliveryTradeFail_autoRefund\",\n" +
                "    \"operator\":\"PAYNOW_patchModify\"\n" +
                "}'";

        String correctionNoListsStr= "20230108181342CP1002100204000147,";
        List<String> correctionNoLists = Splitter.on(Symbols.COMMA).splitToList(correctionNoListsStr);
        for (String correctionNo : correctionNoLists) {
            if (StringUtils.isBlank(correctionNo)){
                return;
            }
            String replace = correctionNo.replace("\n", "");
            System.out.println(String.format(script, replace));
        }
    }

    @Test
    public void assetFail_Modify(){
        String script="curl --location --request POST 'http://127.0.0.1:8080/payment-operation/asset/operation/resend/payment' \\\n" +
                "--header 'Content-Type: application/json' \\\n" +
                "--data-raw '{\n" +
                "    \"paymentOrderNo\": \"%s\",\n" +
                "    \"operator\": \"YESONGLIN\"\n" +
                "}'";

        String correctionNoListsStr= "20230108161115CP1002100203000064,";
        List<String> correctionNoLists = Splitter.on(Symbols.COMMA).splitToList(correctionNoListsStr);
        for (String correctionNo : correctionNoLists) {
            if (StringUtils.isBlank(correctionNo)){
                return;
            }
            String replace = correctionNo.replace("\n", "");
            System.out.println(String.format(script, replace));
        }
    }

    @Test
    public void tradePending_3_Modify(){
        String script="curl --location --request POST 'http://127.0.0.1:7081/correction-center/controller/skipApprovalTest' \\\n" +
                "--header 'Content-Type: application/json' \\\n" +
                "--data-raw '{\n" +
                "    \"correctionNo\":\"%s\",\n" +
                "    \"strategyCode\":\"patchPayOrder\",\n" +
                "    \"operationCorrectionCode\":\"CC001_tradePending\",\n" +
                "    \"operator\":\"PAYNOW_patchModify\"\n" +
                "}'";

        String correctionNoListsStr="20230108161115CP1002100203000064,\n";
        List<String> correctionNoLists = Splitter.on(Symbols.COMMA).splitToList(correctionNoListsStr);
        for (String correctionNo : correctionNoLists) {
            if (StringUtils.isBlank(correctionNo)){
                return;
            }
            String replace = correctionNo.replace("\n", "");
            System.out.println(String.format(script, replace));
        }
    }
}
