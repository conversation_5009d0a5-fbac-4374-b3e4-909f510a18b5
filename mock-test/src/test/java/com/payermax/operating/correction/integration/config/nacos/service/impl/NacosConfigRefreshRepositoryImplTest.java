package com.payermax.operating.correction.integration.config.nacos.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.payermax.operating.correction.integration.config.nacos.model.CorrectionSystemInfo;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyProcessorInfo;
import com.payermax.operating.correction.integration.config.nacos.repository.impl.ConfigRefreshRepositoryImpl;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@RunWith(PowerMockRunner.class) //用PowerMock方式进行运行该类（SpringRunner）
@PowerMockIgnore({"javax.management.*"})//或略javax下的一些类报错
public class NacosConfigRefreshRepositoryImplTest {

    @InjectMocks
    private ConfigRefreshRepositoryImpl nacosConfigRefreshRepositoryImplUnderTest;

    @Before
    public void init(){
        String strategyJson = "{\"redeliveryTradeFail\":{\"strategyCode\":\"redeliveryTradeFail\",\"desc\":\"交易终态(失败)补发货\",\"processors\":[{\"processName\":\"订单中心补交易\",\"beanName\":\"compensationTradeProcessor\"},{\"processName\":\"通知服务补通知\",\"beanName\":\"compensationNotifyProcessor\"}]},\"redeliveryTradeClose\":{\"strategyCode\":\"redeliveryTradeClose\",\"desc\":\"交易终态(超时关单)补发货\",\"processors\":[{\"processName\":\"资产交换退款置失败解冻并返回\",\"beanName\":\"assetRefundProcessor\"},{\"processName\":\"订单中心补交易\",\"beanName\":\"compensationTradeProcessor\"},{\"processName\":\"通知服务补通知\",\"beanName\":\"compensationNotifyProcessor\"}]},\"autoRefund\":{\"strategyCode\":\"autoRefund\",\"desc\":\"自动退款\",\"processors\":[{\"processName\":\"订单中心补支付\",\"beanName\":\"compensationPaymentProcessor\"}]}}";
        ConcurrentHashMap<String, StrategyProcessorInfo> strategyMap = JSONObject.parseObject(strategyJson, new TypeReference<ConcurrentHashMap<String, StrategyProcessorInfo>>() {
        });
        ReflectionTestUtils.setField(nacosConfigRefreshRepositoryImplUnderTest, "strategyProcessMap", strategyMap);


        String correctionSysJson="{\"CC001:FIN-CHANNEL-EXCHANGE\":{\"correctionCode\":\"CC001\",\"sysSource\":\"FIN-CHANNEL-EXCHANGE\",\"notifyBeanName\":\"\"},\"CC001:CHANNEL-RECONCILE\":{\"correctionCode\":\"CC001\",\"sysSource\":\"CHANNEL-RECONCILE\",\"notifyBeanName\":\"mqNotifyProcessor\"},\"CC002:FIN-ASSET-EXCHANGE\":{\"strategyCode\":\"CC002\",\"sysSource\":\"FIN-ASSET-EXCHANGE\",\"notifyBeanName\":\"mqNotifyProcessor\"}}";
        ConcurrentHashMap<String, CorrectionSystemInfo> correctionSysMap = JSONObject.parseObject(correctionSysJson, new TypeReference<ConcurrentHashMap<String, CorrectionSystemInfo>>() {
        });
        ReflectionTestUtils.setField(nacosConfigRefreshRepositoryImplUnderTest, "correctionSysMap", correctionSysMap);

    }

    @Test
    public void testStrategyProcessListener() {
        String strategyJson = "{\"redeliveryTradeFail\":{\"strategyCode\":\"redeliveryTradeFail\",\"desc\":\"交易终态(失败)补发货\",\"processors\":[{\"processName\":\"订单中心补交易\",\"beanName\":\"compensationTradeProcessor\"},{\"processName\":\"通知服务补通知\",\"beanName\":\"compensationNotifyProcessor\"}]},\"redeliveryTradeClose\":{\"strategyCode\":\"redeliveryTradeClose\",\"desc\":\"交易终态(超时关单)补发货\",\"processors\":[{\"processName\":\"资产交换退款置失败解冻并返回\",\"beanName\":\"assetRefundProcessor\"},{\"processName\":\"订单中心补交易\",\"beanName\":\"compensationTradeProcessor\"},{\"processName\":\"通知服务补通知\",\"beanName\":\"compensationNotifyProcessor\"}]},\"autoRefund\":{\"strategyCode\":\"autoRefund\",\"desc\":\"自动退款\",\"processors\":[{\"processName\":\"订单中心补支付\",\"beanName\":\"compensationPaymentProcessor\"}]}}";
        // Setup
        // Run the test
        nacosConfigRefreshRepositoryImplUnderTest.strategyProcessListener(strategyJson);
        nacosConfigRefreshRepositoryImplUnderTest.strategyProcessListener("errorJson");

        // Verify the results
    }

    @Test
    public void testCorrectionSysListener() {
        String correctionSysJson="{\"CC001:FIN-CHANNEL-EXCHANGE\":{\"correctionCode\":\"CC001\",\"sysSource\":\"FIN-ASSET-EXCHANGE\",\"notifyBeanName\":\"\"},\"CC001:CHANNEL-RECONCILE\":{\"correctionCode\":\"CC001\",\"sysSource\":\"CHANNEL-RECONCILE\",\"notifyBeanName\":\"\"},\"CC002:FIN-ASSET-EXCHANGE\":{\"strategyCode\":\"CC002\",\"sysSource\":\"FIN-ASSET-EXCHANGE\",\"notifyBeanName\":\"\"}}";
        // Setup
        // Run the test
        nacosConfigRefreshRepositoryImplUnderTest.correctionSysListener(correctionSysJson);
        nacosConfigRefreshRepositoryImplUnderTest.correctionSysListener("errorJson");

        // Verify the results
    }

    @Test
    public void testGetStrategyProcessor() {
        // Setup
        final StrategyProcessorInfo expectedResult = new StrategyProcessorInfo();

        // Run the test
        final StrategyProcessorInfo result = nacosConfigRefreshRepositoryImplUnderTest.getStrategyProcessor("redeliveryTradeFail");

    }

    @Test
    public void testGetStrategyProcessorBlank() {
        // Setup
        final StrategyProcessorInfo expectedResult = new StrategyProcessorInfo();

        // Run the test
        final StrategyProcessorInfo result = nacosConfigRefreshRepositoryImplUnderTest.getStrategyProcessor("");

        Assert.assertNull(result);
    }

    @Test
    public void testGetCorrectionSys() {
        // Setup
        final CorrectionSystemInfo expectedResult = new CorrectionSystemInfo("CC001","FIN-CHANNEL-EXCHANGE","",Boolean.FALSE);

        // Run the test
        final CorrectionSystemInfo result = nacosConfigRefreshRepositoryImplUnderTest.getCorrectionSys("CC001", "FIN-CHANNEL-EXCHANGE");

        Assert.assertEquals("result is not match",expectedResult.toString(),result.toString());
    }


    @Test
    public void testGetCorrectionSysBlank() {
        // Setup
        final CorrectionSystemInfo expectedResult = new CorrectionSystemInfo();

        // Run the test
        final CorrectionSystemInfo result = nacosConfigRefreshRepositoryImplUnderTest.getCorrectionSys("", "");

        Assert.assertNull(result);

    }
}
