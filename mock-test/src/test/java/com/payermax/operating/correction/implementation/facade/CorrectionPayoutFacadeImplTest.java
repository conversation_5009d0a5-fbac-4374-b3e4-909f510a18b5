package com.payermax.operating.correction.implementation.facade;

import com.alibaba.fastjson.JSONObject;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.correction.domain.dto.PaymentInstanceDomainInfo;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.payermax.operating.correction.domainservice.service.DomainOperationService;
import com.payermax.operating.correction.facade.request.CorrectionPayoutsPaymentInstanceRequest;
import com.payermax.operating.correction.facade.request.PayoutsPaymentInstanceRequest;
import com.payermax.operating.correction.facade.response.PayoutsCoreInfoRenderingResponse;
import com.payermax.operating.correction.facade.response.TargetOrgInfo;
import com.payermax.operating.correction.integration.dto.PaymentInstanceDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsFiledInfo;
import com.payermax.operating.correction.integration.rpc.security.repository.SecurityRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class) //用PowerMock方式进行运行该类（SpringRunner）
@PowerMockIgnore({"javax.management.*"})//或略javax下的一些类报错
public class CorrectionPayoutFacadeImplTest {

    @Mock
    private DomainBizService mockDomainBizService;
    @Mock
    private DomainOperationService mockDomainOperationService;
    @Mock
    private IDomainRepository mockDomainRepository;
    @Mock
    private SecurityRepository mockSecurityRepository;

    @InjectMocks
    private CorrectionPayoutFacadeImpl correctionPayoutFacadeImplUnderTest;

    @Test
    public void testQueryPayoutsParamInfo() {
        // Setup
        final PayoutsPaymentInstanceRequest paymentInstance = new PayoutsPaymentInstanceRequest();
        paymentInstance.setCountry("country");
        paymentInstance.setPaymentMethodNo("paymentMethodNo");
        paymentInstance.setTargetOrg("targetOrg");

        // Configure DomainOperationService.renderingPayOutInfo(...).
        final List<PayoutsFiledInfo> payoutsFiledInfos = Arrays.asList(new PayoutsFiledInfo("fieldName", "fieldMsg"));
        when(mockDomainOperationService.renderingPayOutInfo(
                new PaymentInstanceDomainInfo("country", "paymentMethodNo", "paymentType")))
                .thenReturn(payoutsFiledInfos);

        // Run the test
        final Result<PayoutsCoreInfoRenderingResponse> result = correctionPayoutFacadeImplUnderTest.queryPayoutsParamInfo(
                paymentInstance);

        // Verify the results
        verify(mockDomainOperationService).getCashierProductNo(
                new PaymentInstanceDomainInfo("country", "paymentMethodNo", "paymentType"));
    }

    @Test
    public void testQueryPayoutsParamInfo_DomainOperationServiceRenderingPayOutInfoReturnsNoItems() {
        // Setup
        final PayoutsPaymentInstanceRequest paymentInstance = new PayoutsPaymentInstanceRequest();
        paymentInstance.setCountry("country");
        paymentInstance.setPaymentMethodNo("paymentMethodNo");
        paymentInstance.setTargetOrg("targetOrg");

        when(mockDomainOperationService.renderingPayOutInfo(
                new PaymentInstanceDomainInfo("country", "paymentMethodNo", "paymentType")))
                .thenReturn(Collections.emptyList());

        // Run the test
        final Result<PayoutsCoreInfoRenderingResponse> result = correctionPayoutFacadeImplUnderTest.queryPayoutsParamInfo(
                paymentInstance);

        // Verify the results
        verify(mockDomainOperationService).getCashierProductNo(
                new PaymentInstanceDomainInfo("country", "paymentMethodNo", "paymentType"));
    }

    @Test
    public void testBounceBackRegister() {
        // Setup
        // Run the test
        final Result result = correctionPayoutFacadeImplUnderTest.bounceBackRegister("correctionNo");

        // Verify the results
        verify(mockDomainBizService).bounceBackEvent("correctionNo");
    }

    @Test
    public void testQueryTargetOrgList() {
        // Setup
        final CorrectionPayoutsPaymentInstanceRequest paymentInstance = new CorrectionPayoutsPaymentInstanceRequest();
        paymentInstance.setCorrectionNo("correctionNo");
        final PayoutsPaymentInstanceRequest paymentInstanceRequest = new PayoutsPaymentInstanceRequest();
        paymentInstanceRequest.setCountry("country");
        paymentInstanceRequest.setPaymentMethodNo("paymentMethodNo");
        paymentInstanceRequest.setTargetOrg("targetOrg");
        paymentInstance.setPaymentInstanceRequest(paymentInstanceRequest);

        // Configure DomainOperationService.getPaymentInstanceList(...).
        final List<PaymentInstanceDTO> paymentInstanceDTOS = Arrays.asList(
                new PaymentInstanceDTO("BANK_TRANSFER", "bizIdentify", "targetOrg", "targetOrgName",
                        Arrays.asList(new PaymentInstanceDTO.ExtendProperty("key", "value", "logicKey"))));
        when(mockDomainOperationService.getPaymentInstanceList("correctionNo", "country",
                "paymentMethodNo")).thenReturn(paymentInstanceDTOS);

        // Run the test
        final Result<List<TargetOrgInfo>> result = correctionPayoutFacadeImplUnderTest.queryTargetOrgList(
                paymentInstance);

        // Verify the results
    }

    @Test
    public void testQueryTargetOrgList_DomainOperationServiceReturnsNoItems() {
        // Setup
        final CorrectionPayoutsPaymentInstanceRequest paymentInstance = new CorrectionPayoutsPaymentInstanceRequest();
        paymentInstance.setCorrectionNo("correctionNo");
        final PayoutsPaymentInstanceRequest paymentInstanceRequest = new PayoutsPaymentInstanceRequest();
        paymentInstanceRequest.setCountry("country");
        paymentInstanceRequest.setPaymentMethodNo("paymentMethodNo");
        paymentInstanceRequest.setTargetOrg("targetOrg");
        paymentInstance.setPaymentInstanceRequest(paymentInstanceRequest);

        when(mockDomainOperationService.getPaymentInstanceList("correctionNo", "country",
                "paymentMethodNo")).thenReturn(Collections.emptyList());

        // Run the test
        final Result<List<TargetOrgInfo>> result = correctionPayoutFacadeImplUnderTest.queryTargetOrgList(
                paymentInstance);

        // Verify the results
    }

    @Test
    public void testSavePayoutRuleInfo() {
        String jsonStr = "{\n" +
                "    \"correctionNo\":\"20221222123206CR1002100220000004\",\n" +
                "    \"payoutsRuleInfo\":{\n" +
                "        \"payeeAccount\":\"samye\",\n" +
                "        \"amount\":\"********\",\n" +
                "        \"payeeName\":\"zhangsan\"\n" +
                "    },\n" +
                "    \"payoutsCoreInfo\":{\n" +
                "        \"cashierProductInfo\":{\n" +
                "            \"cashierProductNo\":\"M00000005002\",\n" +
                "            \"paymentType\":\"10\",\n" +
                "            \"country\":\"ID\",\n" +
                "            \"paymentInstance\":{\n" +
                "                \"paymentMethodNo\":\"BANK_TRANSFER\",\n" +
                "                \"targetOrg\":\"ANZ\"\n" +
                "            }\n" +
                "        },\n" +
                "        \"correctionNo\":\"20221222123206CR1002100220000004\"\n" +
                "    }\n" +
                "}";
        // Setup
        final JSONObject json = JSONObject.parseObject(jsonStr);

        // Configure IDomainRepository.getValidCorrectionOrderInfoDto(...).
        final CorrectionOrderInfoDTO orderInfoDTO = new CorrectionOrderInfoDTO("20221222123206CR1002100220000004");
        when(mockDomainRepository.getValidCorrectionOrderInfoDto(anyString())).thenReturn(orderInfoDTO);

        when(mockSecurityRepository.encrypt(anyString())).thenReturn("abcdefg");

        // Run the test
        final Result result = correctionPayoutFacadeImplUnderTest.savePayoutRuleInfo(json);

    }
}
