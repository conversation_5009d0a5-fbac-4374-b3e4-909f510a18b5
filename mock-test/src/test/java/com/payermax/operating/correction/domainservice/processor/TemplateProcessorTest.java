package com.payermax.operating.correction.domainservice.processor;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.dto.MerchantInfo;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.enums.ValidType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.enums.CorrectionEvent;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domain.enums.SourceEnum;
import com.payermax.operating.correction.domainservice.machine.CorrectionOrderStateMachine;
import com.payermax.operating.correction.domainservice.machine.context.CorrectionOrderStateMachineContext;
import com.payermax.operating.correction.domainservice.processor.impl.AssetUnfreezeReturnProcessor;
import com.payermax.operating.correction.domainservice.processor.impl.PatchTradeOrderProcessor;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionHandlerUniInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionRepository;
import com.payermax.operating.correction.integration.queue.MqResultNotifyRepository;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.datacenter.repository.DataCenterRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Currency;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class) //用PowerMock方式进行运行该类（SpringRunner）
@PowerMockIgnore({"javax.management.*"})//或略javax下的一些类报错
public class TemplateProcessorTest {

    @Mock
    private DataCenterRepository mockDataCenterRepository;
    @Mock
    private CorrectionRepository mockCorrectionRepository;
    @Mock
    private ProcessorHolder mockProcessorHolder;
    @Mock
    private MqResultNotifyRepository mockMqResultNotifyRepository;
    @Mock
    private TransactionTemplate mockTransactionTemplate;
    @Mock
    private CorrectionOrderStateMachine mockCorrectionOrderStateMachine;

    @Mock
    private AssetUnfreezeReturnProcessor assetUnfreezeReturnProcessor;

    @Mock
    private PatchTradeOrderProcessor patchTradeOrderProcessor;

    @InjectMocks
    private TemplateProcessor templateProcessorUnderTest;

    @Test
    public void testDoAction() {
        String orderInfoVoStr = "{\"basicInfoDTO\":{\"correctionCode\":\"CC001\",\"correctionName\":\"机构侧成功金融交换失败\",\"desc\":\"机构侧首次返回成失败后又返回成功\",\"opBasicInfo\":{\"operator\":\"yesonglin\",\"utcCreate\":1666140355000,\"utcModified\":1668138815000,\"valid\":\"VALID\"},\"parentCorrectionCode\":\"\",\"tradeType\":\"PAYMENT\"},\"correctionNo\":\"20221111105309CP1002100209000001\",\"globalOrderInfo\":{\"assetInfo\":{\"assetOrder\":{\"orderNo\":\"20221109030140PP5861652106002393\",\"status\":\"3\"},\"payMoney\":{\"amount\":100,\"currency\":\"SAR\"},\"tradeType\":\"PAYMENT\"},\"channelInfo\":{\"channelCode\":\"I_AMAZONPAY_NETWORK_S02\",\"channelCommit\":{\"orderNo\":\"DPC65217516679629035732740099\",\"status\":\"2\"}},\"originalMerchantInfo\":{\"merOrderNo\":\"RenshTest1667962849304\",\"merchantAppId\":\"6666c8b036a24579974497c2f9a33333\",\"merchantNo\":\"010213834784554\"},\"productCode\":\"1001\",\"tradeInfo\":{\"payRequest\":{\"orderNo\":\"20221109030140PP5861652106002393\",\"status\":\"2\"},\"tradeOrder\":{\"orderNo\":\"20221109030051TI2913652106002392\",\"status\":\"3\"}}},\"handlerRecordMap\":{\"CHOOSE_STRATEGY\":{\"handlerInfo\":{\"correctionNo\":\"20221111105309CP1002100209000001\",\"handlerType\":\"CHOOSE_STRATEGY\",\"strategyCode\":\"redeliveryTradeClose\"},\"opBasicInfo\":{\"operator\":\"system\",\"utcCreate\":1668712296000,\"utcModified\":1668712296000},\"processResult\":\"\",\"status\":\"SUCCESS\"},\"APPROVAL_AGREE\":{\"handlerInfo\":{\"correctionNo\":\"20221111105309CP1002100209000001\",\"handlerType\":\"APPROVAL_AGREE\",\"strategyCode\":\"redeliveryTradeClose\"},\"opBasicInfo\":{\"operator\":\"yesonglin\",\"utcCreate\":1668712343000,\"utcModified\":1668712343000},\"processResult\":\"通过\",\"status\":\"SUCCESS\"},\"assetUnfreezeReturnProcessor\":{\"handlerInfo\":{\"correctionNo\":\"20221111105309CP1002100209000001\",\"handlerType\":\"assetUnfreezeReturnProcessor\",\"strategyCode\":\"redeliveryTradeClose\"},\"opBasicInfo\":{\"operator\":\"system\",\"utcCreate\":1668712343000,\"utcModified\":1668712343000},\"processResult\":{\"resVoucherInfo \":{\"voucherNo \":\"DPC65217516679629035732740099 \",\"voucherType \":\"CHANNEL_COMMIT_NO \"}},\"status\":\"SUCCESS\"}},\"memo\":\"test备注\",\"operationBasic\":{\"correctionCode\":\"test渠道CC001\",\"correctionName\":\"test_CC001\",\"desc\":\"test描述\",\"opBasicInfo\":{\"operator\":\"yesonglin\",\"utcCreate\":1668654046000,\"utcModified\":1668654046000,\"valid\":\"VALID\"},\"parentCorrectionCode\":\"CC001\",\"tradeType\":\"PAYMENT\"},\"operator\":\"system\",\"oriVoucherInfo\":{\"voucherNo\":\"DPC65217516679629035732740099\",\"voucherType\":\"CHANNEL_COMMIT_NO\"},\"processStatus\":\"PENDING\",\"source\":\"SYSTEM\",\"strategyProcessor\":{\"dbInfo\":{\"opBasicInfo\":{\"operator\":\"YESONGLIN\",\"utcCreate\":1666141879000,\"utcModified\":1668042163000,\"valid\":\"VALID\"},\"strategyCode\":\"redeliveryTradeClose\",\"strategyName\":\"原交易关单补发货\"},\"desc\":\"交易终态(关单)补发货\",\"processors\":[{\"beanName\":\"assetUnfreezeReturnProcessor\",\"handler\":\"REQUEST\",\"processName\":\"资产交换退款置失败解冻并返回\"},{\"beanName\":\"patchTradeOrderProcessor\",\"handler\":\"RESULT\",\"processName\":\"超时关单状态修正\"}],\"strategyCode\":\"redeliveryTradeClose\"},\"sysSource\":\"FIN-CHANNEL-EXCHANGE\",\"systemInfo\":{\"correctionCode\":\"CC001\",\"notifyBeanName\":\"\",\"sysSource\":\"FIN-CHANNEL-EXCHANGE\"},\"voucherNo\":\"DPC65217516679629035732740099\",\"voucherType\":\"CHANNEL_COMMIT_NO\"}";
        // Setup
        final DomainCorrectionInfo correctionInfo = JSONObject.parseObject(orderInfoVoStr, new TypeReference<DomainCorrectionInfo>() {
        });
        correctionInfo.setResVoucherInfo(new VoucherInfo("20221025134018PP0093503806000663:C",DCVoucherType.R_TRADE_ORDER_NO));
        when(mockProcessorHolder.getProcess("assetUnfreezeReturnProcessor")).thenReturn(assetUnfreezeReturnProcessor);
        when(mockProcessorHolder.getProcess("patchTradeOrderProcessor")).thenReturn(patchTradeOrderProcessor);
        when(patchTradeOrderProcessor.handlerResult(any(),null)).thenReturn(CommonStatusEnum.SUCCESS);
        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenReturn(true);

        // Configure CorrectionRepository.loadCorrectionLastRecord(...).
        final CorrectionOrderHandlerRecordDTO orderHandlerRecordDTO = new CorrectionOrderHandlerRecordDTO(new CorrectionHandlerUniInfo("20221111105309CP1002100209000001", "redeliveryTradeFail", "patchTradePayOrderProcessor"), CommonStatusEnum.SUCCESS, "{\"resVoucherInfo\":{\"voucherNo\":\"20221026090043TI3861005906000805:C\",\"voucherType\":\"R_TRADE_ORDER_NO\"}}", new OperationBasicInfo("system", ValidType.VALID, DateUtil.date().getTime(), DateUtil.date().getTime()));
        when(mockCorrectionRepository.loadCorrectionLastRecord(new CorrectionHandlerUniInfo("20221111105309CP1002100209000001", "redeliveryTradeFail", "patchTradePayOrderProcessor"))).thenReturn(Nullable.getNullVal());

        // Configure DataCenterRepository.dcGlobalQuery(...).
        String globalOrderInfoStr = "{\"assetInfo\":{\"assetOrder\":{\"orderNo\":\"20221109030140PP5861652106002393\",\"status\":\"3\"},\"payMoney\":{\"amount\":100,\"currency\":\"SAR\"},\"tradeType\":\"PAYMENT\"},\"channelInfo\":{\"channelCode\":\"I_AMAZONPAY_NETWORK_S02\",\"channelCommit\":{\"orderNo\":\"DPC65217516679629035732740099\",\"status\":\"2\"}},\"originalMerchantInfo\":{\"merOrderNo\":\"RenshTest1667962849304\",\"merchantAppId\":\"6666c8b036a24579974497c2f9a33333\",\"merchantNo\":\"010213834784554\"},\"productCode\":\"1001\",\"tradeInfo\":{\"payRequest\":{\"orderNo\":\"20221109030140PP5861652106002393\",\"status\":\"2\"},\"tradeOrder\":{\"orderNo\":\"20221109030051TI2913652106002392\",\"status\":\"3\"}}}";
        final GlobalOrderInfo globalOrderInfo = JSONObject.parseObject(globalOrderInfoStr, new TypeReference<GlobalOrderInfo>() {
        });
        final GlobalOrderInfo correctionOrder = new GlobalOrderInfo();
        when(mockDataCenterRepository.dcGlobalQuery(any(),anyString())).thenReturn(correctionOrder);

        when(mockCorrectionRepository.restoreHandlerStatus(any(),anyString(),anyString())).thenReturn(true);

        // Run the test
        templateProcessorUnderTest.doAction(correctionInfo);

    }

    @Test
    public void testDoAction_TransactionTemplateThrowsTransactionException() {
        // Setup
        final DomainCorrectionInfo correctionInfo = new DomainCorrectionInfo();
        correctionInfo.setCorrectionNo("20221111105309CP1002100209000001");
        correctionInfo.setVoucherNo("DPC65217516679629035732740099");
        correctionInfo.setVoucherType(DCVoucherType.CHANNEL_COMMIT_NO);
        correctionInfo.setOriVoucherInfo(new VoucherInfo("DPC65217516679629035732740099", DCVoucherType.CHANNEL_COMMIT_NO));
        correctionInfo.setResVoucherInfo(new VoucherInfo("voucherNo", DCVoucherType.TRADE_TOKEN_NO));
        correctionInfo.setMerchantInfo(new MerchantInfo("merchantNo", "merOrderNo", "merchantAppId"));
        correctionInfo.setSource(SourceEnum.MANUAL);
        correctionInfo.setSysSource("sysSource");
        correctionInfo.setPayTotalMoney(new Money(0L, 0, Currency.getInstance("USD")));
        correctionInfo.setProcessStatus(ProcessStatusEnum.PROCESSED);

        when(mockProcessorHolder.getProcess("beanName")).thenReturn(null);
//        when(mockTransactionTemplate.execute(any(TransactionCallback.class))).thenThrow(TransactionException.class);

        // Configure CorrectionRepository.loadCorrectionLastRecord(...).
        final CorrectionOrderHandlerRecordDTO orderHandlerRecordDTO = new CorrectionOrderHandlerRecordDTO(new CorrectionHandlerUniInfo("correctionNo", "strategyCode", "handlerType"), CommonStatusEnum.PENDING, "processResult", new OperationBasicInfo("operator", ValidType.VALID, 0L, 0L));
        when(mockCorrectionRepository.loadCorrectionLastRecord(new CorrectionHandlerUniInfo("correctionNo", "strategyCode", "handlerType"))).thenReturn(orderHandlerRecordDTO);

        // Configure DataCenterRepository.dcGlobalQuery(...).
        final GlobalOrderInfo globalOrderInfo = new GlobalOrderInfo();
        globalOrderInfo.setOriginalMerchantInfo(new MerchantInfo("merchantNo", "merOrderNo", "merchantAppId"));
        globalOrderInfo.setTradeInfo(new GlobalOrderInfo.TradeInfo(new GlobalOrderInfo.OrderInfo("orderNo", "status",null,null,null,null,null,null,null,true), new GlobalOrderInfo.OrderInfo("orderNo", "status",null,null,null,null,null,null,null,true), null, new GlobalOrderInfo.OrderInfo("orderNo", "status",null,null,null,null,null,null,null,true)));
        globalOrderInfo.setAssetInfo(new GlobalOrderInfo.AssetInfo(new GlobalOrderInfo.OrderInfo("orderNo", "status",null,null,null,null,null,null,null,true), new Money(0L, 0, Currency.getInstance("USD")), TradeType.REFUND));
        globalOrderInfo.setChannelInfo(new GlobalOrderInfo.ChannelInfo());
        globalOrderInfo.setProductCode("productCode");
        final GlobalOrderInfo globalOrderInfo1 = new GlobalOrderInfo();
        globalOrderInfo1.setOriginalMerchantInfo(new MerchantInfo("merchantNo", "merOrderNo", "merchantAppId"));
        globalOrderInfo1.setTradeInfo(new GlobalOrderInfo.TradeInfo(new GlobalOrderInfo.OrderInfo("orderNo", "status",null,null,null,null,null,null,null,true), new GlobalOrderInfo.OrderInfo("orderNo", "status",null,null,null,null,null,null,null,true), null, new GlobalOrderInfo.OrderInfo("orderNo", "status",null,null,null,null,null,null,null,true)));
        globalOrderInfo1.setAssetInfo(new GlobalOrderInfo.AssetInfo(new GlobalOrderInfo.OrderInfo("orderNo", "status",null,null,null,null,null,null,null,true), new Money(0L, 0, Currency.getInstance("USD")), TradeType.REFUND));
        globalOrderInfo1.setChannelInfo(new GlobalOrderInfo.ChannelInfo());
        globalOrderInfo1.setProductCode("productCode");
        when(mockDataCenterRepository.dcGlobalQuery(DCVoucherType.TRADE_TOKEN_NO, "orderId")).thenReturn(globalOrderInfo1);

        when(mockCorrectionRepository.restoreHandlerStatus(new CorrectionHandlerUniInfo("correctionNo", "strategyCode", "handlerType"), "expect", "target")).thenReturn(false);

        // Run the test
        templateProcessorUnderTest.doAction(correctionInfo);

        // Verify the results
        verify(mockCorrectionRepository).storeOrderHandlerRecordInfo(new CorrectionOrderHandlerRecordDTO(new CorrectionHandlerUniInfo("correctionNo", "strategyCode", "handlerType"), CommonStatusEnum.PENDING, "processResult", new OperationBasicInfo("operator", ValidType.VALID, 0L, 0L)));
        verify(mockCorrectionRepository).storeOrderHandlerRecordResultInfo(new CorrectionHandlerUniInfo("correctionNo", "strategyCode", "handlerType"), new BaseResProcess(new VoucherInfo("voucherNo", DCVoucherType.TRADE_TOKEN_NO), "result"));
        verify(mockCorrectionRepository).storeCorrectionResVoucherInfo("correctionNo", new VoucherInfo("voucherNo", DCVoucherType.TRADE_TOKEN_NO));
        verify(mockMqResultNotifyRepository).delayHandlerNotify("correctionNo",0);
        verify(mockCorrectionRepository).restoreHandlerStatus(new CorrectionHandlerUniInfo("correctionNo", "strategyCode", "handlerType"), "expect", "target");
        verify(mockCorrectionOrderStateMachine).sendEvent(ProcessStatusEnum.PROCESSED, CorrectionEvent.PUSH, new CorrectionOrderStateMachineContext(new DomainCorrectionInfo()));
    }
}
