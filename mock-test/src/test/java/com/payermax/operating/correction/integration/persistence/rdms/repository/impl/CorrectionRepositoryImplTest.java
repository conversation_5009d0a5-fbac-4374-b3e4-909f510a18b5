package com.payermax.operating.correction.integration.persistence.rdms.repository.impl;

import com.payermax.operating.correction.core.common.dto.ConditionInfo;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.enums.ConditionEnum;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.enums.ValidType;
import com.payermax.operating.correction.core.dal.dao.CorrectionBasicInfoMapper;
import com.payermax.operating.correction.core.dal.dao.CorrectionOperationStrategyInfoMapper;
import com.payermax.operating.correction.core.dal.po.CorrectionBasicInfo;
import com.payermax.operating.correction.core.dal.po.CorrectionOperationStrategyInfo;
import com.payermax.operating.correction.integration.config.nacos.repository.ConfigRepository;
import com.payermax.operating.correction.integration.persistence.rdms.assembler.CorrectionRdmsAssembler;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOperationStrategyInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.ValidationRuleConditionDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class) //用PowerMock方式进行运行该类（SpringRunner）
@PowerMockIgnore({"javax.management.*"})//或略javax下的一些类报错
public class CorrectionRepositoryImplTest {

    @Mock
    private CorrectionOperationStrategyInfoMapper mockStrategyInfoMapper;
    @Mock
    private CorrectionBasicInfoMapper mockBasicInfoMapper;
    @Mock
    private CorrectionRdmsAssembler mockAssembler;
    @Mock
    private ConfigRepository mockConfigRepository;

    @InjectMocks
    private CorrectionRepositoryImpl correctionRepositoryImplUnderTest;

    private OperationBasicInfo operationBasic;

    @Before
    public void init() {
        operationBasic = new OperationBasicInfo("yesonglin", ValidType.VALID, null, null);
    }

    @Test
    public void testStoreOperationStrategyInfo() {
        // Setup
        final CorrectionOperationStrategyInfoDTO info = new CorrectionOperationStrategyInfoDTO();
        info.setStrategyCode("redeliveryTradeFail");
        info.setStrategyName("交易单失败补交易");
        info.setOpBasicInfo(operationBasic);

        when(mockStrategyInfoMapper.insertDupKeyUpdate(any(CorrectionOperationStrategyInfo.class))).thenReturn(1);

        // Configure CorrectionOperationAssembler.operationStrategyModelToPo(...).
        final CorrectionOperationStrategyInfo correctionOperationStrategyInfo = new CorrectionOperationStrategyInfo();
        correctionOperationStrategyInfo.setOperationStrategyCode("redeliveryTradeFail");
        correctionOperationStrategyInfo.setOperationStrategyName("交易单失败补交易");
        correctionOperationStrategyInfo.setIsValid((byte) 1);
        correctionOperationStrategyInfo.setUtcCreate(null);
        correctionOperationStrategyInfo.setUtcModified(null);
        correctionOperationStrategyInfo.setOperator("yesonglin");
        when(mockAssembler.operationStrategyModelToPo(new CorrectionOperationStrategyInfoDTO())).thenReturn(correctionOperationStrategyInfo);

        // Run the test
        correctionRepositoryImplUnderTest.storeOperationStrategyInfo(info);

    }

    @Test
    public void testLoadOperationStrategy() {
        // Setup
        final CorrectionOperationStrategyInfoDTO expectedResult = new CorrectionOperationStrategyInfoDTO();
        expectedResult.setStrategyCode("redeliveryTradeFail");
        expectedResult.setStrategyName("交易单失败补交易");
        expectedResult.setOpBasicInfo(operationBasic);

        // Configure CorrectionOperationStrategyInfoMapper.selectByPrimaryKey(...).
        final CorrectionOperationStrategyInfo correctionOperationStrategyInfo = new CorrectionOperationStrategyInfo();
        correctionOperationStrategyInfo.setOperationStrategyCode("redeliveryTradeFail");
        correctionOperationStrategyInfo.setOperationStrategyName("交易单失败补交易");
        correctionOperationStrategyInfo.setIsValid((byte) 1);
        correctionOperationStrategyInfo.setUtcCreate(null);
        correctionOperationStrategyInfo.setUtcModified(null);
        correctionOperationStrategyInfo.setOperator("yesonglin");
        when(mockStrategyInfoMapper.selectByPrimaryKey("redeliveryTradeFail")).thenReturn(correctionOperationStrategyInfo);

        // Configure CorrectionOperationAssembler.operationStrategyPoToModel(...).
        final CorrectionOperationStrategyInfoDTO correctionOperationStrategyInfoDTO = new CorrectionOperationStrategyInfoDTO();
        correctionOperationStrategyInfoDTO.setStrategyCode("redeliveryTradeFail");
        correctionOperationStrategyInfoDTO.setStrategyName("交易单失败补交易");
        correctionOperationStrategyInfoDTO.setOpBasicInfo(operationBasic);
        when(mockAssembler.operationStrategyPoToModel(any(CorrectionOperationStrategyInfo.class))).thenReturn(correctionOperationStrategyInfoDTO);

        // Configure ConfigRepository.getOperationRuleInfoListByRuleId(...).

        // Run the test
        final CorrectionOperationStrategyInfoDTO result = correctionRepositoryImplUnderTest.loadOperationStrategy("redeliveryTradeFail");

    }

    @Test
    public void testLoadOperationStrategy_ConfigRepositoryReturnsNoItems() {
        when(mockStrategyInfoMapper.selectByPrimaryKey("strategyCode")).thenReturn(null);

        // Run the test
        final CorrectionOperationStrategyInfoDTO result = correctionRepositoryImplUnderTest.loadOperationStrategy("strategyCode");

        Assert.assertNull(result);

    }

    @Test
    public void testStoreCorrectionBasicInfo() {
        // Setup
        final ValidationRuleConditionDTO validationRuleConditionDTO = new ValidationRuleConditionDTO();
        ConditionInfo conditionInfo = new ConditionInfo();
        conditionInfo.setValue("channelCodeTest");
        conditionInfo.setCondition(ConditionEnum.EQUAL);
        validationRuleConditionDTO.setChannelCodeCon(conditionInfo);
        final CorrectionBasicInfoDTO info = CorrectionBasicInfoDTO.builder()
                .correctionCode("CC001")
                .desc("金融交换侧失败，机构侧成功")
                .opBasicInfo(operationBasic)
                .execValidateRules(validationRuleConditionDTO)
                .build();
        when(mockBasicInfoMapper.insertDupKeyUpdate(any(CorrectionBasicInfo.class))).thenReturn(0);

        // Configure CorrectionOperationAssembler.basicModelToPo(...).
        final CorrectionBasicInfo correctionBasicInfo = new CorrectionBasicInfo();
        correctionBasicInfo.setCorrectionCode("CC001");
        correctionBasicInfo.setCorrectionName("金融交换侧失败，机构侧成功");
        correctionBasicInfo.setDesc("机构首次返回失败后又返回成功");
        correctionBasicInfo.setExecValidateRule(validationRuleConditionDTO.toString());
        correctionBasicInfo.setIsValid(ValidType.VALID.getVal());
        correctionBasicInfo.setUtcCreate(null);
        correctionBasicInfo.setUtcModified(null);
        correctionBasicInfo.setOperator("yesonglin");
        when(mockAssembler.basicModelToPo( CorrectionBasicInfoDTO.builder()
                .correctionCode("CC001")
                .desc("金融交换侧失败，机构侧成功")
                .opBasicInfo(operationBasic)
                .execValidateRules(validationRuleConditionDTO)
                .build())).thenReturn(correctionBasicInfo);

        // Run the test
        correctionRepositoryImplUnderTest.storeCorrectionBasicInfo(info);

    }

    @Test
    public void testLoadBasicInfo() {
        // Setup
        final ValidationRuleConditionDTO validationRuleConditionDTO = new ValidationRuleConditionDTO();
        ConditionInfo conditionInfo = new ConditionInfo();
        conditionInfo.setValue("channelCodeTest");
        conditionInfo.setCondition(ConditionEnum.EQUAL);
        validationRuleConditionDTO.setChannelCodeCon(conditionInfo);
        final CorrectionBasicInfoDTO expectedResult =  CorrectionBasicInfoDTO.builder()
                .correctionCode("CC001")
                .desc("金融交换侧失败，机构侧成功")
                .opBasicInfo(operationBasic)
                .execValidateRules(validationRuleConditionDTO)
                .build();

        // Configure CorrectionBasicInfoMapper.selectByPrimaryKey(...).
        final CorrectionBasicInfo correctionBasicInfo = new CorrectionBasicInfo();
        correctionBasicInfo.setCorrectionCode("CC001");
        correctionBasicInfo.setCorrectionName("金融交换侧失败，机构侧成功");
        correctionBasicInfo.setDesc("机构首次返回失败后又返回成功");
        correctionBasicInfo.setExecValidateRule(validationRuleConditionDTO.toString());
        correctionBasicInfo.setIsValid((byte) 1);
        correctionBasicInfo.setUtcCreate(null);
        correctionBasicInfo.setUtcModified(null);
        correctionBasicInfo.setOperator("yesonglin");
        when(mockBasicInfoMapper.selectByPrimaryKey("CC001")).thenReturn(correctionBasicInfo);

        // Configure CorrectionOperationAssembler.basicPoToModel(...).
        final ValidationRuleConditionDTO validationRuleConditionDTO1 = new ValidationRuleConditionDTO();
        ConditionInfo conditionInfo1 = new ConditionInfo();
        conditionInfo.setValue("channelCodeTest");
        conditionInfo.setCondition(ConditionEnum.EQUAL);
        validationRuleConditionDTO.setChannelCodeCon(conditionInfo1);
        final CorrectionBasicInfoDTO correctionBasicInfoDTO =  CorrectionBasicInfoDTO.builder()
                .correctionCode("CC001")
                .desc("金融交换侧失败，机构侧成功")
                .opBasicInfo(operationBasic)
                .execValidateRules(validationRuleConditionDTO)
                .build();
        when(mockAssembler.basicPoToModel(any(CorrectionBasicInfo.class))).thenReturn(correctionBasicInfoDTO);

        // Run the test
        final CorrectionBasicInfoDTO result = correctionRepositoryImplUnderTest.loadBasicInfo("CC001");

    }


    @Test
    public void testLoadBasicInfoNull() {
        // Setup
        when(mockBasicInfoMapper.selectByPrimaryKey("CC001")).thenReturn(null);

        // Run the test
        final CorrectionBasicInfoDTO result = correctionRepositoryImplUnderTest.loadBasicInfo("CC001");


        Assert.assertNull(result);
    }
}
