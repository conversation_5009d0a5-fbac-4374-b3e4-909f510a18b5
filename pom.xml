<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.12.RELEASE</version>
        <relativePath/>
    </parent>

    <groupId>com.payermax.operating</groupId>
    <artifactId>fin-correction-center</artifactId>
    <version>1.0.0-RELEASE</version>
    <packaging>pom</packaging>

    <modules>
        <module>correction-center-facade</module>
        <module>correction-center-service-implementation</module>
        <module>correction-center-integration</module>
        <module>correction-center-domain</module>
        <module>correction-center-core</module>
        <module>correction-center-domain-service</module>
        <module>mock-test</module>
    </modules>
    <properties>
        <revision>1.0.0</revision>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <springboot.version>2.3.12.RELEASE</springboot.version>
        <aws.mysql.jdbc.version>1.1.7.2</aws.mysql.jdbc.version>
        <guava.version>28.1-jre</guava.version>
        <mybatis.spring.boot.starter.version>2.1.0</mybatis.spring.boot.starter.version>
        <lombok.version>1.18.8</lombok.version>
        <druid.version>1.2.9</druid.version>
        <commons.lang3.version>3.9</commons.lang3.version>
        <slf4j.version>1.7.5</slf4j.version>
        <fintech.common.version>1.0.0.RELEASE</fintech.common.version>
        <common.enum.version>1.1.4-20221101-RELEASE</common.enum.version>
        <org.mapstruct.version>1.4.2.Final</org.mapstruct.version>
        <javax.validation-api.version>2.0.1.Final</javax.validation-api.version>
        <hibernate.validator.version>6.0.16.Final</hibernate.validator.version>
        <dubbo.version>3.0.12.PM1</dubbo.version>
        <nacos-client.version>2.1.0</nacos-client.version>
        <nacos-config-spring-boot.version>0.2.8</nacos-config-spring-boot.version>
        <spring-context-support.version>1.0.11</spring-context-support.version>
        <ionia-rocketMQ.version>0.0.43-20240821-RELEASE</ionia-rocketMQ.version>
        <mybatis-plus-version>3.5.1</mybatis-plus-version>
        <cola-statemachine.version>1.0.0</cola-statemachine.version>
        <correction.facade.version>1.1.0-20250715-RELEASE</correction.facade.version>
        <correction.center.version>1.0.0-RELEASE</correction.center.version>
        <sale-product-centre-facade.version>1.5.9-20230420-RELEASE</sale-product-centre-facade.version>
        <assetx.api.version>1.1.3-20230105-1-RELEASE</assetx.api.version>
        <assetx.correction.operation.version>1.0.0-20250102-RELEASE</assetx.correction.operation.version>
        <security-api-version>2.0.11-20250218-RELEASE</security-api-version>
        <context-center-client-version>1.0.18-20221103-RELEASE</context-center-client-version>
        <cashier-core-version>1.1.5-20221201-RELEASE</cashier-core-version>
        <channel-exchange-version>1.0.85-20250522-RELEASE</channel-exchange-version>
        <fintech.components.dubbo.extension.qos.version>1.0.0.RELEASE</fintech.components.dubbo.extension.qos.version>
        <fin.voucher.version>1.1.10-20231102_2-RELEASE</fin.voucher.version>
        <log4j2.version>2.18.0</log4j2.version>
        <ionia.log.digest.version>0.0.26-20231007-RELEASE</ionia.log.digest.version>
        <sales-product-solution.facade.version>1.0.13-20240417-RELEASE</sales-product-solution.facade.version>
        <ionia.version>0.0.70-20250328-RELEASE</ionia.version>
        <awsjavasdk.version>1.12.161</awsjavasdk.version>
        <sentry-spring-boot-starter.version>4.3.0</sentry-spring-boot-starter.version>
        <sentry-log4j2.version>4.3.0</sentry-log4j2.version>
        <fintech-components-sentry.version>1.0.0-20220414-RELEASE</fintech-components-sentry.version>
        <slf4j.version>1.7.15</slf4j.version>
        <nacos-spring-context.version>1.1.1-PM1-20240718-RELEASE</nacos-spring-context.version>
        <sales-omc-merchant-facade.version>1.0.48-20241017-RELEASE</sales-omc-merchant-facade.version>
        <paylink-facade.version>1.3.3-20250325-RELEASE</paylink-facade.version>
        <order-center-facade.version>2.1.17-20250529-RELEASE</order-center-facade.version>
        <assetx-operation-facade.version>1.0.0-20250102-RELEASE</assetx-operation-facade.version>
        <basic-security-auth-core.version>2.0.11-20250218-RELEASE</basic-security-auth-core.version>
        <ionia-config-encrypt.version>0.0.70-20250328-RELEASE</ionia-config-encrypt.version>
        <ionia-qos-client.version>0.0.70-20250328-RELEASE</ionia-qos-client.version>
        <xxl-job-core.version>2.0.1-noglue.PM1</xxl-job-core.version>
        <channel-reconcile-facade.version>1.0.8-20250704-RELEASE</channel-reconcile-facade.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-qos-client</artifactId>
                <version>${ionia-qos-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-config-encrypt-all</artifactId>
                <version>${ionia-config-encrypt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.trade</groupId>
                <artifactId>paylink-facade</artifactId>
                <version>${paylink-facade.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>ionia-log-digest-core</artifactId>
                        <groupId>com.payermax.infra</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.payermax.payment</groupId>
                <artifactId>assetx-operation-facade</artifactId>
                <version>${assetx-operation-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.sales</groupId>
                <artifactId>sales-omc-merchant-facade</artifactId>
                <version>${sales-omc-merchant-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>io.sentry</groupId>
                <artifactId>sentry-spring-boot-starter</artifactId>
                <version>${sentry-spring-boot-starter.version}</version>
                <exclusions>
                    <!-- 可能会与项目中spring-boot-starter包冲突，所以进行排除 -->
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-spring-context</artifactId>
                <version>${nacos-spring-context.version}</version>
            </dependency>
            <dependency>
                <groupId>io.sentry</groupId>
                <artifactId>sentry-log4j2</artifactId>
                <version>${sentry-log4j2.version}</version>
            </dependency>
            <!--匿名内部类错误堆栈掩码聚合，防止相同错误错误分散-->
            <dependency>
                <groupId>com.payermax.common</groupId>
                <artifactId>fintech-components-sentry</artifactId>
                <version>${fintech-components-sentry.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.sales</groupId>
                <artifactId>sales-product-solution-facade</artifactId>
                <version>${sales-product-solution.facade.version}</version>
            </dependency>
            <!--springboot-->
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-spring-boot-starter</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo-registry-nacos</artifactId>
                <version>${dubbo.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>nacos-client</artifactId>
                <version>${nacos-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>nacos-config-spring-boot-starter</artifactId>
                <version>${nacos-config-spring-boot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba.nacos</groupId>
                        <artifactId>nacos-spring-context</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.dubbo</groupId>
                <artifactId>dubbo</artifactId>
                <version>${dubbo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring-context-support.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${javax.validation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate.validator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${springboot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-starter-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-core</artifactId>
                <version>${log4j2.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-log4j2</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${springboot.version}</version>
            </dependency>
            <dependency>
                <groupId>software.aws.rds</groupId>
                <artifactId>aws-mysql-jdbc</artifactId>
                <version>${aws.mysql.jdbc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.spring.boot.starter.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ushareit.fintech.parent</groupId>
                        <artifactId>fintech-components-jasypt-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <!--公司依赖组件包-->
            <dependency>
                <groupId>com.payermax.common</groupId>
                <artifactId>common-enum</artifactId>
                <version>${common.enum.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-rocketMQ</artifactId>
                <version>${ionia-rocketMQ.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-log-digest-dubbo3</artifactId>
                <version>${ionia.log.digest.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-log-digest-annotation</artifactId>
                <version>${ionia.log.digest.version}</version>
            </dependency>
            <!-- 差错模块依赖jar包-->
            <dependency>
                <groupId>com.payermax.operating</groupId>
                <artifactId>correction-center-facade</artifactId>
                <version>${correction.facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.operating</groupId>
                <artifactId>correction-center-domain</artifactId>
                <version>${correction.center.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ushareit.fintech.parent</groupId>
                        <artifactId>fintech-components-jasypt-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.payermax.operating</groupId>
                <artifactId>common</artifactId>
                <version>${correction.center.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.operating</groupId>
                <artifactId>correction-center-integration</artifactId>
                <version>${correction.center.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ushareit.fintech.parent</groupId>
                        <artifactId>fintech-components-jasypt-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.payermax.operating</groupId>
                <artifactId>dal</artifactId>
                <version>${correction.center.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.ushareit.fintech.parent</groupId>
                        <artifactId>fintech-components-jasypt-dubbo</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.payermax.operating</groupId>
                <artifactId>correction-center-domain-service</artifactId>
                <version>${correction.center.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.operating</groupId>
                <artifactId>correction-center-service-implementation</artifactId>
                <version>${correction.center.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.zaxxer</groupId>
                        <artifactId>HikariCP</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 数据中心jar包-->
            <dependency>
                <groupId>com.payermax.data</groupId>
                <artifactId>dc-query-facade</artifactId>
                <version>1.1.4-20231115-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.trade</groupId>
                <artifactId>trade-ordercenter-facade</artifactId>
                <version>${order-center-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.sale</groupId>
                <artifactId>sale-product-centre-facade</artifactId>
                <version>${sale-product-centre-facade.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.funds</groupId>
                <artifactId>funds-order-facade</artifactId>
                <version>1.1.6-20230713-RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.risk</groupId>
                <artifactId>risk-engine-client</artifactId>
                <version>2.0.2-20240229-RELEASE</version>
            </dependency>
            <dependency>
                <artifactId>payment-assetsx-facade</artifactId>
                <groupId>com.payermax.payment</groupId>
                <version>${assetx.api.version}</version>
            </dependency>
            <dependency>
                <artifactId>assetx-correction-facade</artifactId>
                <groupId>com.payermax.payment</groupId>
                <version>${assetx.correction.operation.version}</version>
            </dependency>
            <dependency>
                <artifactId>fintech-security-service-api</artifactId>
                <groupId>com.ushareit.fintech.security</groupId>
                <version>${security-api-version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.basic</groupId>
                <artifactId>context-center-service-client</artifactId>
                <version>${context-center-client-version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.payment</groupId>
                <artifactId>payment-cashier-core-facade</artifactId>
                <version>${cashier-core-version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.channel</groupId>
                <artifactId>channel-exchange-facade</artifactId>
                <version>${channel-exchange-version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.trade</groupId>
                <artifactId>trade-settle-facade</artifactId>
                <version>1.0.17-20230427-RELEASE</version>
            </dependency>
            <!--互金基础jar包-->
            <dependency>
                <groupId>com.payermax.basic</groupId>
                <artifactId>basic-security-auth-core</artifactId>
                <version>${basic-security-auth-core.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-config-encrypt-system</artifactId>
                <version>${ionia.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.infra</groupId>
                <artifactId>ionia-config-encrypt-nacos</artifactId>
                <version>${ionia.version}</version>
            </dependency>
            <dependency>
                <groupId>com.payermax.common</groupId>
                <artifactId>common-lang</artifactId>
                <version>2.0.7-20230419-RELEASE</version>
            </dependency>

            <dependency>
                <artifactId>basic-voucher-facade</artifactId>
                <groupId>com.payermax.basic</groupId>
                <version>${fin.voucher.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ushareit.fintech.parent</groupId>
                <artifactId>fintech-common</artifactId>
                <version>${fintech.common.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>spring-context</artifactId>
                        <groupId>org.springframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hutool-all</artifactId>
                        <groupId>cn.hutool</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jsr305</artifactId>
                        <groupId>com.google.code.findbugs</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>error_prone_annotations</artifactId>
                        <groupId>com.google.errorprone</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>failureaccess</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>listenablefuture</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>j2objc-annotations</artifactId>
                        <groupId>com.google.j2objc</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-collections</artifactId>
                        <groupId>commons-collections</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-lang3</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>checker-qual</artifactId>
                        <groupId>org.checkerframework</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>animal-sniffer-annotations</artifactId>
                        <groupId>org.codehaus.mojo</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-api</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <!--单元测试-->
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-api-mockito2</artifactId>
                <version>2.0.5</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>2.0.5</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>2.27.0</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.objenesis</groupId>
                <artifactId>objenesis</artifactId>
                <version>2.6</version>
            </dependency>

            <!-- mybatis 分页插件 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus-version}</version>
            </dependency>
            <!-- mybatis 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.2.13</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>cola-statemachine</artifactId>
                <version>${cola-statemachine.version}</version>
            </dependency>
            <!--解决jar 包冲突-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.5.7</version>
            </dependency>
            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>4.3</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.dataformat</groupId>
                <artifactId>jackson-dataformat-yaml</artifactId>
                <version>2.12.4</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-bom</artifactId>
                <version>${awsjavasdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-core</artifactId>
                <version>1.12.26</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-kms</artifactId>
                <version>1.12.26</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job-core.version}</version>
                </dependency>
            <dependency>
                <groupId>com.payermax.channel</groupId>
                <artifactId>channel-reconcile-facade</artifactId>
                <version>${channel-reconcile-facade.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <generateBackupPoms>true</generateBackupPoms>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-archetype-plugin</artifactId>
                    <version>3.0.1</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-deploy-plugin</artifactId>
                    <configuration>
                        <skip>false</skip>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.6.1</version>
                    <configuration>
                        <source>${maven.compiler.source}</source>
                        <target>${maven.compiler.target}</target>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${org.mapstruct.version}</version>
                            </path>
                            <annotationProcessorPath>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </annotationProcessorPath>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.0.2</version>
                    <configuration>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <executions>
                        <execution>
                            <id>attach-sources</id>
                            <goals>
                                <goal>jar</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
    <distributionManagement>
        <repository>
            <id>Releases</id>
            <name>shareit-release maven</name>
            <url>http://pay-nexus.shareitpay.in/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>Snapshots</id>
            <name>shareit-snapshots maven</name>
            <url>http://pay-nexus.shareitpay.in/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
