<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fin-correction-center</artifactId>
        <groupId>com.payermax.operating</groupId>
        <version>1.0.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>correction-center-service-implementation</artifactId>
    <packaging>jar</packaging>
    <version>${correction.center.version}</version>

    <dependencies>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-qos-client</artifactId>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-spring-boot-starter</artifactId>
            <exclusions>
                <!-- 可能会与项目中spring-boot-starter包冲突，所以进行排除 -->
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-config-encrypt-all</artifactId>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-log4j2</artifactId>
        </dependency>
        <!--匿名内部类错误堆栈掩码聚合，防止相同错误错误分散-->
        <dependency>
            <groupId>com.payermax.common</groupId>
            <artifactId>fintech-components-sentry</artifactId>
            <version>${fintech-components-sentry.version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-context-support</artifactId>
                    <groupId>com.alibaba.spring</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-context-support</artifactId>
                    <groupId>com.alibaba.spring</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.zaxxer</groupId>
                    <artifactId>HikariCP</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>software.aws.rds</groupId>
            <artifactId>aws-mysql-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.operating</groupId>
            <artifactId>correction-center-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.operating</groupId>
            <artifactId>correction-center-domain-service</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.infra</groupId>
            <artifactId>ionia-config-encrypt-system</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>${project.basedir}/src/main/resources</directory>
                <excludes>
                    <exclude>**/env/**/*.properties</exclude>
                </excludes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <mainClass>com.payermax.operating.correction.implementation.StartApplication</mainClass>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>2.3.2</version>
                <configuration>
                    <!--指定生成文档的使用的配置文件,配置文件放在自己的项目中-->
                    <configFile>/src/main/resources/smart-doc.json</configFile>
                    <!--指定项目名称-->
                    <projectName>差错中心</projectName>
                    <excludes>
                        <!--1.0.7版本开始你还可以用正则匹配排除,如：poi.* -->
                        <exclude>com.alibaba:fastjson</exclude>
                    </excludes>
                    <includes>
                        <!--格式为：groupId:artifactId;参考如下-->
                        <include>com.alibaba:fastjson</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>correction-center-service-implementation</finalName>
    </build>
</project>
