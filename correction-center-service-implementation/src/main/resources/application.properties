#global config
nacos.config.server-addr=${nacos.config.server-addr}
nacos.config.group=${spring.application.name}
nacos.config.username=${nacos.config.username}
nacos.config.password=${nacos.config.password}
nacos.config.namespace=${nacos.config.namespace}
# spring éç½®
spring.application.name=correction-center
spring.main.banner-mode=off
spring.jackson.default-property-inclusion=non_null
spring.main.allow-bean-definition-overriding=true
#æå¡å¯å¨éç½®
server.port=7081
server.compression.enabled=true
server.compression.mime-types=application/json,text/html
server.undertow.threads.io=16
server.undertow.threads.worker=256
server.undertow.buffer-size=1024
server.undertow.url-charset=UTF-8
# æ¯å¦åéçç´æ¥åå­(NIOç´æ¥åéçå å¤åå­)
server.undertow.direct-buffers=true
#mybatis
mybatis.mapper-locations=classpath:mapper/*.xml
#system config
#endpointsæ´é²
management.endpoints.web.exposure.include=online,offline,health,info,prometheus
management.server.port=10108
spring.sleuth.enabled=false
#dubbo registry center
dubbo.application.name=Pay-${spring.application.name}
dubbo.application.qos-enable=true
dubbo.application.qos-port=22222
dubbo.consumer.check=false
dubbo.consumer.retries=0
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.registry.address=nacos://${nacos.config.server-addr}?namespace=${nacos.config.namespace}
dubbo.registry.username=${nacos.config.username}
dubbo.registry.password=${nacos.config.password}
#nacos config center
nacos.config.auto-refresh=true
nacos.config.bootstrap.enable=true
nacos.config.data-ids=application.properties,database.properties,strategy_process.json,correction_system.json,rocketmq,matcher_rule.json,payouts_country.json,pay_method_mapping.json,strategy_blacklist.json
nacos.config.enable-remote-sync-config=true
nacos.config.type=properties
nacos.discovery.server-addr=${nacos.config.server-addr}
nacos.config.autoRefresh=true
nacos.config.remoteFirst=true
nacos.config.bootstrap.logEnable=false

