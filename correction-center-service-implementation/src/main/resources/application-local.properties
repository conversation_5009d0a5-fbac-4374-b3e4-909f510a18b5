#nacos
nacos.config.bootstrap.enable=true
nacos.config.namespace=6e07ceb6-97a7-49de-a8ce-b66097165a27
nacos.config.server-addr=************:8848
nacos.config.group=${spring.application.name}
nacos.config.type=properties
nacos.config.data-ids=strategy_process.json,correction_system.json,rocketmq,matcher_rule.json,payouts_country.json,pay_method_mapping.json
nacos.config.auto-refresh=true
nacos.config.enable-remote-sync-config=true
nacos.discovery.server-addr=************:8848
nacos.config.username=nacos
nacos.config.password=nacos
#dubbo
dubbo.application.qos-port=29200
dubbo.application.qos-enable=true
dubbo.application.name=Pay-${spring.application.name}
dubbo.registry.address=nacos://************:8848?namespace=6e07ceb6-97a7-49de-a8ce-b66097165a27&username=nacos&password=nacos
dubbo.registry.register-mode=instance
dubbo.protocol.name=dubbo
dubbo.protocol.port=-1
dubbo.consumer.check=false
dubbo.consumer.retries=0
# rocketmq name sever
rocketmq.name-server=************:9876;************:9876;************:9876
# rocket mq producer group
rocketmq.producer.group=correction-center-producer
rocketmq.consumer.group=correction-center-consumer
#å·®éç»æéç¥topic
correction.topic.result.notify=topic_correction_center_result_notify
#å·®éäºä»¶æ¨étopic
correction.topic.event.push=topic_correction_center_event_push
#å·®éäºä»¶å»¶æ¶å¤çtopic
correction.topic.delay.handler=topic_correction_center_delay_handler
correction.topic.delay.level=2
#undertow
server.servlet.context-path=/correction-center
server.compression.enabled=true
server.compression.mime-types=application/json,text/html
server.undertow.threads.io=16
server.undertow.threads.worker=256
server.undertow.buffer-size=1024
server.undertow.url-charset=UTF-8
server.undertow.direct-buffers=true
#database
spring.datasource.url=***************************************************************************************************************************************************************************************************
spring.datasource.driver-class-name=software.aws.rds.jdbc.mysql.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.username=dev
spring.datasource.password=fyH3Gs10Bdi9NaNDXvRajJoAV90IqSrT
spring.datasource.initialSize=5
spring.datasource.minidle=5
spring.datasource.maxActive=10
spring.datasource.maxWait=60000
spring.datasource.timeBetweenEvictionRunsMillis=60000
spring.datasource.validationQuery=SELECT 1 FROM dual
#mybatis
mybatis.mapper-locations=classpath:mapper/*.xml
spring.cache.redis.key-prefix=correctionCenter:
spring.redis.client-name=jedis
spring.redis.database=12
spring.redis.host=shareit-pay-all-dev.5uzvt6.0001.apse1.cache.amazonaws.com
spring.redis.jedis.pool.max-active=50
spring.redis.jedis.pool.max-idle=50
spring.redis.jedis.pool.max-wait=2000
spring.redis.jedis.pool.min-idle=10
spring.redis.jedis.pool.time-between-eviction-runs=60000
spring.redis.port=6379
spring.redis.timeout=10000