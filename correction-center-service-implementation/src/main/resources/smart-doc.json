{
  "isStrict": false, //是否开启严格模式
  "allInOne": true,
  "createDebugPage": true, //生成测试页
  "framework": "dubbo",
  "outPath": "src/main/resources/static/doc", //指定文档的输出路径
  "projectName": "fin-correction-center",//配置自己的项目名称
  "rpcApiDependencies":[{ // 项目开放的dubbo api接口模块依赖，配置后输出到文档方便使用者集成
    "artifactId":"fin-correction-center-facade",
    "groupId":"com.payermax.operating",
    "version":"1.0.0-RELEASE"
  }],
  "appToken": "c2024af3dbec44bfa6bf33311c5c825c", // torna平台对接appKey
  "openUrl": "http://torna-dev.payermax.com/api",//torna平台地址，
//  "packageFilters": "com.payermax.operating.correction.service.implementation.controller",
  "replace": false //是否替换原文档
}
