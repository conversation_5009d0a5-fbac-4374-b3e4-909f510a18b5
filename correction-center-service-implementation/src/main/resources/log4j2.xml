<?xml version="1.0" encoding="UTF-8"?>

<!--设置log4j2的自身log级别为error-->

<configuration status="error" packages="org.apache.logging.log4j.core,com.payermax.infra.ionia.log.mask.util,io.sentry.log4j2">

    <Properties>
        <Property name="dir">logs</Property>
        <Property name="log_level">info</Property>
        <Property name="logFormat">[%d{yyyy-MM-dd HH:mm:ss:SSS}] [%-5level] [%t] [%traceId] [%X{traceId}] [correction-center] [%c(%L)] %m%n</Property>
        <Property name="every_file_size">100MB</Property>
        <Property name="metricFormat"> %m%n</Property>
    </Properties>

    <appenders>
        <console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${logFormat}"/>
        </console>
        <RollingFile name="RollingFileDebug" fileName="${dir}/debug.log"
                     filePattern="${dir}/debug-%d{yyyy-MM-dd}-%i.log">
            <Filters>
                <ThresholdFilter level="DEBUG"/>
            </Filters>
            <PatternLayout pattern="${logFormat}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="${every_file_size}"/>
            </Policies>
        </RollingFile>
        <RollingFile name="RollingFileInfo" fileName="${dir}/info.log"
                     filePattern="${dir}/info-%d{yyyy-MM-dd}-%i.log">
            <Filters>
                <ThresholdFilter level="INFO"/>
                <ThresholdFilter level="WARN" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
            <PatternLayout pattern="${logFormat}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="${every_file_size}"/>
            </Policies>
        </RollingFile>
        <RollingFile name="RollingFileWarn" fileName="${dir}/warn.log"
                     filePattern="${dir}/warn-%d{yyyy-MM-dd}-%i.log">
            <Filters>
                <ThresholdFilter level="WARN"/>
                <ThresholdFilter level="ERROR" onMatch="DENY" onMismatch="NEUTRAL"/>
            </Filters>
            <PatternLayout pattern="${logFormat}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="${every_file_size}"/>
            </Policies>
        </RollingFile>
        <RollingFile name="RollingFileError" fileName="${dir}/error.log"
                     filePattern="${dir}/error-%d{yyyy-MM-dd}-%i.log">
            <ThresholdFilter level="ERROR"/>
            <PatternLayout pattern="${logFormat}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="${every_file_size}"/>
            </Policies>
        </RollingFile>
        <RollingFile name="WEB-TIME" fileName="${dir}/pay_metric.log" filePattern="${dir}/pay_metric-%d{yyyy-MM-dd}-%i.log">
            <ThresholdFilter level="INFO"/>
            <PatternLayout pattern="${metricFormat}"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="${every_file_size}"/>
            </Policies>
        </RollingFile>
        <Sentry name="Sentry" ></Sentry>
        <!--GRPC Log Appender-->
        <GRPCLogClientAppender name="grpc-log">
            <PatternLayout pattern="%msg"/>
        </GRPCLogClientAppender>
    </appenders>
    <loggers>
        <logger name="digestLog" level="INFO" additivity="false">
            <appender-ref ref="grpc-log"/>
            <appender-ref ref="RollingFileInfo"/>
        </logger>
        <root level="${log_level}">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileDebug"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
            <Appender-ref ref="Sentry"/>
        </root>
        <logger name="org.springframework" level="INFO"/>
        <logger name="org.mybatis" level="INFO"/>
        <logger name="org.apache.dubbo" level="error"/>
        <logger name="com.alibaba.nacos.spring.context.annotation.config.NacosValueAnnotationBeanPostProcessor" level="warn"/>
    </loggers>

</configuration>