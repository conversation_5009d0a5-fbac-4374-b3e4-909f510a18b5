package com.payermax.operating.correction.implementation.response;

import com.payermax.operating.correction.facade.common.OrderCorrectionBaseInfo;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @desc 差错中心数据返回
 * @date 2022/3/7
 */
@ToString
@Getter
@Setter
@NoArgsConstructor
public class CorrectionBaseInfoResponse extends OrderCorrectionBaseInfo {

    /**
     * 差错原因
     * @mock 金额不一致
     */
    private String errorReasonStr;
    /**
     * 来源
     * @mock 人工录入
     */
    private String sourceStr;

    /**
     * 处理状态
     * @mock 已处理, 处理审核中
     */
    private String processStatus;

    /**
     * 处理方案
     *@mock 订正修复
     */
    private String processStrategy;

    /**
     * 差错创建时间(手动录入时间，API接口创建时间)
     */
    private Date correctionCreateTime;

    /**
     * 服务信息
     */
    private ServiceFeeInfo serviceFeeInfo;

    /**
     * 操作信息
     */
    private OperationInfo operationInfo;

    /**
     * 审批信息
     */
    private ApproveInfo approveInfo;
}
