package com.payermax.operating.correction.implementation.facade;

import com.payermax.common.lang.model.dto.Page;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.correction.core.common.annotation.FacadeMethod;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.enums.ValidType;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.payermax.operating.correction.domainservice.service.DomainOperationService;
import com.payermax.operating.correction.facade.common.BaseReasonBasicInfo;
import com.payermax.operating.correction.facade.common.QueryPageRequest;
import com.payermax.operating.correction.facade.common.ReasonBasicInfo;
import com.payermax.operating.correction.facade.provider.operation.CorrectionOperationReasonFacade;
import com.payermax.operating.correction.facade.request.SaveReasonBasicInfo;
import com.payermax.operating.correction.implementation.assembler.CorrectionInfoDomainAssembler;
import com.payermax.operating.correction.implementation.assembler.DomainAssemblerUtils;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 差错运营平台facade实现类
 * @date 2022/10/18
 */
@SuppressWarnings("unused")
@DubboService(version = "1.0")
@Slf4j
public class CorrectionOperationReasonFacadeImpl implements CorrectionOperationReasonFacade {
    @Resource
    private DomainBizService domainBizService;

    @Resource
    private DomainOperationService domainOperationService;

    @Resource
    private CorrectionInfoDomainAssembler infoDomainAssembler;

    @Resource
    private IDomainRepository domainRepository;

    @Override
    @FacadeMethod
    public Result<Page<ReasonBasicInfo>> queryReasonInfoByPage(QueryPageRequest queryPage) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<CorrectionBasicInfoDTO> page = domainOperationService.getReasonBasicInfoByPage(queryPage.getPage(), ValidType.getValidByName(queryPage.getValid()));
        Map<String, CorrectionBasicInfoDTO> parentReasonInfoMap = domainOperationService.getAllMappingParentReasonInfo();
        List<ReasonBasicInfo> list = page.getRecords().stream().map(e -> infoDomainAssembler.toReasonBasicInfo(e, parentReasonInfoMap)).collect(Collectors.toList());
        return ResultUtil.success(DomainAssemblerUtils.buildPage((int) page.getCurrent(), (int) page.getTotal(), (int) page.getSize(), list));
    }

    @Override
    @FacadeMethod
    public Result storeReasonInfo(SaveReasonBasicInfo saveReasonBasicInfo) {
        domainRepository.getValidParentReasonBasicInfo(saveReasonBasicInfo.getParentBasicInfo().getCorrectionCode());
        CorrectionBasicInfoDTO dto = infoDomainAssembler.toReasonInfo(saveReasonBasicInfo);
        domainOperationService.storeReasonBasicInfo(dto);
        return ResultUtil.success();
    }

    @Override
    @FacadeMethod
    public Result invalidReasonBasic(String correctionCode, String operator) {
        CorrectionBasicInfoDTO basicInfoDTO = new CorrectionBasicInfoDTO();
        basicInfoDTO.setCorrectionCode(correctionCode);
        basicInfoDTO.setOpBasicInfo(new OperationBasicInfo(operator, ValidType.INVALID.name()));
        domainOperationService.updateReasonBasicInfo(basicInfoDTO);
        return ResultUtil.success();
    }

    @Override
    @FacadeMethod
    public Result<List<BaseReasonBasicInfo>> queryAllParentCorrectionCode() {
        List<CorrectionBasicInfoDTO> allParentCorrectionReason = domainOperationService.getAllParentCorrectionReason();
        List<BaseReasonBasicInfo> list = allParentCorrectionReason.stream().map(e -> new BaseReasonBasicInfo(e.getCorrectionCode(), e.getCorrectionName())).collect(Collectors.toList());
        return ResultUtil.success(list);
    }

    @Override
    @FacadeMethod
    public Result<List<String>> queryStrategyList(String correctionCode, String tradeType) {
        return ResultUtil.success(domainOperationService.getConfigStrategyCode(correctionCode, TradeType.getTradeTypeByName(tradeType)));
    }

    @Override
    @FacadeMethod
    public Result<List<BaseReasonBasicInfo>> filterChildCorrectionCode(String parentCorrectionCode,String tradeType) {
        List<CorrectionBasicInfoDTO> basicInfoList = domainOperationService.getBasicInfoByParentCode(parentCorrectionCode,TradeType.getTradeTypeByName(tradeType));
        return ResultUtil.success(basicInfoList.stream().map(e -> new BaseReasonBasicInfo(e.getCorrectionCode(), e.getCorrectionName())).collect(Collectors.toList()));
    }
}
