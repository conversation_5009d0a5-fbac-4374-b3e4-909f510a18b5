package com.payermax.operating.correction.implementation.request;

import com.payermax.common.lang.model.dto.request.PageRequest;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @desc 查询差错中心请求
 * @date 2022/3/7
 */
@Getter
@Setter
@NoArgsConstructor
public class QueryCorrectionInfoRequest {
    /**
     * 订单号
     * @mock PCHLP103983453435
     */
    private String voucherNo;
    /**
     * 订单号
     * @mock PCHLP103983453435
     */
    private String voucherType;
    /**
     * 差错原因
     * @mock INCONSISTENT_AMOUNTS
     */
    private String event;
    /**
     * 处理状态
     * @mock PROCESSED
     */
    private String processStatus;
    /**
     * 开始时间(差错创建时间)
     */
    private Long startTime;
    /**
     * 结束时间(差错创建时间)
     */
    private Long endTime;
    /**
     * page信息
     */
    private PageRequest page;
    /**
     * 操作人
     */
    @NotBlank(message = "operator can't be blank")
    private String operator;
}
