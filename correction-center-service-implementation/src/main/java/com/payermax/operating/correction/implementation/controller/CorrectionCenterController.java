package com.payermax.operating.correction.implementation.controller;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.correction.core.common.enums.GlobalOrderReadEvent;
import com.payermax.operating.correction.core.common.enums.OrderDetailType;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.payermax.operating.correction.domainservice.service.DomainOperationService;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.persistence.kvstore.repository.KVRepository;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 前端调用controller层
 *
 * <AUTHOR>
 * @desc 前端调用controller层
 * @date 2022 /3/7
 */
@RestController
@RequestMapping("/controller")
public class CorrectionCenterController {

    @Resource
    private IDomainRepository domainRepository;

    @Resource
    private DomainOperationService domainOperationService;

    @Resource
    private DomainBizService domainBizService;

    @Resource
    private KVRepository kvRepository;

    @GetMapping("/refreshParentBasicInfo")
    public Result refreshParentBasicInfo() {
        domainRepository.refreshParentBasicInfoCache();
        return ResultUtil.success();
    }

    @GetMapping("/compensateResultNotify")
    public Result compensateCorrectionResultNotify(String correctionNo) {
        DomainCorrectionInfo correctionInfo = domainRepository.getValidCorrectionOrderInfo(correctionNo, OrderDetailType.All_QUERY, GlobalOrderReadEvent.DISPLAY);
        domainRepository.handlerResultNotify(correctionInfo);
        return ResultUtil.success();
    }

    @GetMapping("/updateDirtyData")
    public Result updateDirtyData(String correctionNo, String targetStatus) {
        domainOperationService.handlerDirtyCorrectionNo(correctionNo, targetStatus);
        return ResultUtil.success();
    }

    @GetMapping("/resetHandlerStatus")
    public Result resetHandlerStatus(String correctionNo) {
        DomainCorrectionInfo correctionInfo = domainRepository.getValidCorrectionOrderInfo(correctionNo, OrderDetailType.All_QUERY, GlobalOrderReadEvent.OPERATION);
        //寻找当前处理记录
        domainBizService.resetHandleRecord(correctionInfo);
        return ResultUtil.success();
    }

    @GetMapping("/clearingCache")
    public Result clearingCache(String cacheKey) {
        //移除key
        kvRepository.remove(cacheKey);
        return ResultUtil.success();
    }

}
