package com.payermax.operating.correction.implementation.config;

import com.google.common.eventbus.AsyncEventBus;
import com.payermax.operating.correction.core.common.enums.Publisher;
import com.payermax.operating.correction.core.common.utils.EventCenter;
import com.payermax.operating.correction.domainservice.subscriber.ResultNotifySubscriber;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;

/**
 * 事件总线配置
 *
 * <AUTHOR>
 * @date 2021/12/17 13:04
 */
@Configuration
@Slf4j
@RequiredArgsConstructor
@SuppressWarnings("unused")
public class EventBusConfiguration implements InitializingBean {

    /**
     * 支付事件订阅者
     */
    private final ResultNotifySubscriber notifySubscriber;

    /**
     * 事件异步执行线程池
     */
    @Resource(name = "bizAsyncExecutor")
    private ThreadPoolTaskExecutor bizAsyncExecutor;

    /**
     * 初始化事件总线
     */
    @Override
    public void afterPropertiesSet() throws Exception {

        AsyncEventBus asyncEventBus = new AsyncEventBus(bizAsyncExecutor);

        /* 初始化支付结果通知事件总线 */
        EventCenter.addEventBus(Publisher.HANDLE_RESULT.name(), asyncEventBus).register(notifySubscriber);
    }
}
