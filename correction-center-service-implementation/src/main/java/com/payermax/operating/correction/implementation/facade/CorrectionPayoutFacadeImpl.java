package com.payermax.operating.correction.implementation.facade;

import com.alibaba.fastjson.JSONObject;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.correction.core.common.annotation.FacadeMethod;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.enums.UserInfoEnum;
import com.payermax.operating.correction.domain.dto.ExtraUserInfoDTO;
import com.payermax.operating.correction.domain.dto.PaymentInstanceDomainInfo;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.payermax.operating.correction.domainservice.service.DomainOperationService;
import com.payermax.operating.correction.facade.provider.operation.CorrectionPayoutFacade;
import com.payermax.operating.correction.facade.request.CorrectionPayoutsPaymentInstanceRequest;
import com.payermax.operating.correction.facade.request.PayoutsPaymentInstanceRequest;
import com.payermax.operating.correction.facade.request.SupportedPayoutsCountryRequest;
import com.payermax.operating.correction.facade.response.PayoutsCoreInfoRenderingResponse;
import com.payermax.operating.correction.facade.response.PayoutsInfoRenderingResponse;
import com.payermax.operating.correction.facade.response.TargetOrgInfo;
import com.payermax.operating.correction.implementation.assembler.CorrectionInfoDomainAssembler;
import com.payermax.operating.correction.implementation.request.PayoutRuleInfoRequest;
import com.payermax.operating.correction.integration.dto.PayPaymentInstanceInfoDTO;
import com.payermax.operating.correction.integration.dto.PaymentInstanceDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsCoreInfo;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsFiledInfo;
import com.payermax.operating.correction.integration.rpc.productcenter.dto.CashierProductInfo;
import com.payermax.operating.correction.integration.rpc.security.repository.SecurityRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 出款接口实现类
 * @date 2022/12/15
 */
@DubboService(version = "1.0")
@Slf4j
public class CorrectionPayoutFacadeImpl implements CorrectionPayoutFacade {

    @Resource
    private DomainBizService domainBizService;

    @Resource
    private DomainOperationService domainOperationService;

    @Resource
    private IDomainRepository domainRepository;

    @Resource
    private SecurityRepository securityRepository;

    @Resource
    private CorrectionInfoDomainAssembler infoDomainAssembler;

    @Override
    @FacadeMethod
    public Result<PayoutsCoreInfoRenderingResponse> queryPayoutsParamInfo(PayoutsPaymentInstanceRequest paymentInstance) {
        PayoutsCoreInfoRenderingResponse coreInfoResponse = new PayoutsCoreInfoRenderingResponse();
        PaymentInstanceDomainInfo paymentInstanceDomainInfo = new PaymentInstanceDomainInfo(paymentInstance.getCountry(), paymentInstance.getPaymentMethodNo(), CorrectionConstant.PAYOUTS_PAYMENT_METHODS,paymentInstance.getTargetOrg());
        //先获取售卖编码
        domainOperationService.getCashierProductNo(paymentInstanceDomainInfo);
        //获取出款所需核心参数
        List<PayoutsFiledInfo> payoutsFiledInfos = domainOperationService.renderingPayOutInfo(paymentInstanceDomainInfo);
        //transfer
        List<PayoutsInfoRenderingResponse> list = payoutsFiledInfos.stream().map(e -> new PayoutsInfoRenderingResponse(e.getFieldMsg(), e.getFieldName())).collect(Collectors.toList());
        coreInfoResponse.setRenderingList(list);
        coreInfoResponse.setCashierProductNo(paymentInstanceDomainInfo.getCashierProductNo());
        return ResultUtil.success(coreInfoResponse);
    }

    @Override
    @FacadeMethod
    public Result bounceBackRegister(String correctionNo) {
        domainBizService.bounceBackEvent(correctionNo);
        return ResultUtil.success();
    }

    @Override
    @FacadeMethod
    public Result<List<TargetOrgInfo>> queryTargetOrgList(CorrectionPayoutsPaymentInstanceRequest paymentInstance) {
        List<PaymentInstanceDTO> paymentInstanceList = domainOperationService.getPaymentInstanceList(paymentInstance.getCorrectionNo(), paymentInstance.getPaymentInstanceRequest().getCountry(), paymentInstance.getPaymentInstanceRequest().getPaymentMethodNo());
        List<TargetOrgInfo> list = paymentInstanceList.stream().map(e -> new TargetOrgInfo(e.getTargetOrg(), e.getTargetOrgName())).collect(Collectors.toList());
        return ResultUtil.success(list);
    }

    @Override
    @FacadeMethod
    public Result savePayoutRuleInfo(JSONObject json) {
        PayoutRuleInfoRequest payoutRuleInfoRequest = json.toJavaObject(PayoutRuleInfoRequest.class);
        PayoutsCoreInfo payoutsCoreInfo = payoutRuleInfoRequest.getPayoutsCoreInfo();
        //获取差错订单信息
        CorrectionOrderInfoDTO orderInfoDTO = domainRepository.getValidCorrectionOrderInfoDto(payoutRuleInfoRequest.getCorrectionNo());

        //出款规则校验
        payoutRuleInfoRequest.getPayoutsRuleInfo().setAmount(orderInfoDTO.getPayTotalMoney().getAmount().toString());

        payoutsCoreInfo.setPayOutsMoney(orderInfoDTO.getPayTotalMoney());
        domainRepository.validationPayoutRule(payoutRuleInfoRequest.getPayoutsRuleInfo(), payoutsCoreInfo);

        //构建扩展信息
        CashierProductInfo cashierProductInfo = payoutsCoreInfo.getCashierProductInfo();
        PayPaymentInstanceInfoDTO paymentInstanceInfoDTO = new PayPaymentInstanceInfoDTO(cashierProductInfo.getCountry(), cashierProductInfo.getPaymentInstance().getPaymentMethodNo(), cashierProductInfo.getPaymentInstance().getTargetOrg());
        //加密存储
        ExtraUserInfoDTO userInfo = new ExtraUserInfoDTO(UserInfoEnum.PAYOUTS, securityRepository.encrypt(JSONObject.toJSONString(payoutRuleInfoRequest.getPayoutsRuleInfo())));
        userInfo.setExtraInfo(JSONObject.toJSONString(paymentInstanceInfoDTO));
        domainOperationService.storeUserInfo(payoutRuleInfoRequest.getCorrectionNo(), userInfo);
        return ResultUtil.success();
    }


    @Override
    @FacadeMethod
    public Result<List<SupportedPayoutsCountryRequest>> querySupportedPayoutsCountry() {
        return ResultUtil.success(domainOperationService.getCorrectionPayoutsCountry().stream().map(infoDomainAssembler::toSupportedPayoutsCountry).collect(Collectors.toList()));
    }
}
