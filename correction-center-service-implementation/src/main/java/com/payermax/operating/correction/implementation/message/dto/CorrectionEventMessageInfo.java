package com.payermax.operating.correction.implementation.message.dto;

import com.payermax.infra.ionia.rocketmq.bean.BaseMqMessage;
import com.payermax.operating.correction.facade.common.ChannelReconcileRedundantInfo;
import com.payermax.operating.correction.facade.common.CorrectionAmountInfo;
import com.payermax.operating.correction.facade.common.CorrectionEventInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 差错推送事件消息
 * @date 2022/9/27
 */
@Data
@NoArgsConstructor
public class CorrectionEventMessageInfo extends BaseMqMessage {

    private static final long serialVersionUID = 233454245664623L;

    private EventMessageInfo messageBody;

    @Data
    @NoArgsConstructor
    public static class EventMessageInfo {

        /**
         * 系统来源
         */
        private String sysSource;

        /**
         * 差错信息
         */
        private CorrectionEventInfo correctionInfo;
        /**
         * 差错金额信息
         */
        private CorrectionAmountInfo amountInfo;

        /**
         * 渠道对账冗余信息
         */
        private ChannelReconcileRedundantInfo reconcileRedundantInfo;

        /**
         * 重试类型
         */
        private String retryType;

        /**
         * 重试次数
         */
        private Integer retryNum;
    }

}
