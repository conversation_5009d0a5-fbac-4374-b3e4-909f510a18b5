package com.payermax.operating.correction.implementation.aspect;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.core.common.utils.ValidationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcException;
import org.apache.skywalking.apm.toolkit.trace.TraceContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.aop.aspectj.MethodInvocationProceedingJoinPoint;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

/**
 * 参数校验切面处理器
 *
 * <AUTHOR>
 * @date 2021/12/02 21:24
 */
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor
@SuppressWarnings("unsed")
public class GlobalAspect {

    @NacosValue(value = "${aspect.param.debug:true}", autoRefreshed = true)
    @SuppressWarnings("unused")
    private boolean paramDebug;

    /**
     * 切面切入点
     */
    @Pointcut(value = "@annotation(com.payermax.operating.correction.core.common.annotation.FacadeMethod)")
    @SuppressWarnings("unused")
    public void pointCut() {
    }

    /**
     * 环绕切面执行
     *
     * @param joinPoint ProceedingJoinPoint
     * @return Object 环绕织入响应结果
     * @throws Throwable
     */
    @Around(value = "pointCut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        String invokedMethodName = this.getInvokedMethod(joinPoint);

        StopWatch stopWatch = new StopWatch(invokedMethodName);

        stopWatch.start();

        Object facadeReq = joinPoint.getArgs();

        Object result = Nullable.getNullVal();

        MDC.put(CorrectionConstant.LOG_TRACE_ID,TraceContext.traceId());

        try {
            ValidationUtil.ValidationResult validationResult = ValidationUtil.validate(facadeReq);

            AssertUtil.isTrue(!validationResult.getHasErrors(), validationResult.toPrettyString());

            result = joinPoint.proceed();

        } catch (Exception e) {

            log.error("method invoke catch exception,methodName:{},parameter:{}", invokedMethodName, JSONObject.toJSONString(facadeReq), e);

            result = this.exceptionHandler(facadeReq, e);

        } finally {

            stopWatch.stop();

            release();
        }

        return result;
    }

    /**
     * 全局异常捕捉处理
     *
     * @param e 异常对象
     * @return Result
     */
    private Result<?> exceptionHandler(Object reqInfo, Exception e) {

        if (e instanceof BusinessException) {
            log.warn("biz exception,reqInfo : [{}]", JSONObject.toJSONString(reqInfo), e);
            BusinessException bizExP = (BusinessException) e;
            return ResultUtil.fail(bizExP.getErrCode(), bizExP.getMessage());
        } else if (e instanceof DuplicateKeyException) {
            log.warn("duplicate db,reqInfo : [{}]", JSONObject.toJSONString(reqInfo));
            return ResultUtil.fail(ReturnCode.DUPLICATE_ORDER.getCode(), ReturnCode.DUPLICATE_ORDER.getMsg());
        } else if (e instanceof UnsupportedOperationException) {
            log.error("Unsupported exception:", e);
            UnsupportedOperationException operationException = (UnsupportedOperationException) e;
            return ResultUtil.fail(ReturnCode.CONFIG_ERROR.getCode(), operationException.getMessage());
        } else if (e instanceof RpcException) {
            log.error("rpc exception:", e);
            return ResultUtil.fail(ReturnCode.TIMEOUT.getCode(), e.getMessage());
        } else {
            log.error("exceptionHandler system exception: reqInfo[{}]", JSONObject.toJSONString(reqInfo), e);
            return ResultUtil.fail(ReturnCode.SYS_EXCEPTION.getCode(), ReturnCode.SYS_EXCEPTION.getMsg());
        }
    }

    /**
     * 组装当前执行类全限定路径
     *
     * @param joinPoint ProceedingJoinPoint
     * @return String
     */
    private String getInvokedMethod(ProceedingJoinPoint joinPoint) {

        return joinPoint.getSignature().getDeclaringTypeName().concat(Symbols.DOT)
                .concat(((MethodInvocationProceedingJoinPoint) joinPoint).getSignature().getName());
    }

    /**
     * 资源释放清理
     */
    private void release() {

        MDC.clear();
    }
}
