package com.payermax.operating.correction.implementation.message;

import com.alibaba.fastjson.JSONObject;
import com.payermax.infra.ionia.rocketmq.handler.BaseMqMessageListener;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.payermax.operating.correction.facade.request.ExternalResultInfo;
import com.payermax.operating.correction.integration.queue.MqResultNotifyRepository;
import com.payermax.operating.correction.integration.queue.dto.ExternalResultInfoMessageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 差错事件接收消息消费者
 * @date 2022/9/27
 */
@RocketMQMessageListener(
        topic = "${correction.topic.external.result.notify}",
        consumerGroup = "${rocketmq.consumer.group.external.notify}",
        consumeThreadNumber = 5

)
@Component
@Slf4j
public class ExternalResultNotifyConsumerImpl extends BaseMqMessageListener<ExternalResultInfoMessageInfo> implements RocketMQListener<ExternalResultInfoMessageInfo> {

    @Resource
    private DomainBizService domainBizService;

    @Resource
    private MqResultNotifyRepository mqNotifyRepository;

    @Override
    protected void handleMessage(ExternalResultInfoMessageInfo msg) {
        log.info("ExternalResultNotifyConsumerImpl handleMessage,msg : [{}]",JSONObject.toJSONString(msg.getMessageBody()));
        ExternalResultInfoMessageInfo.ExternalResultMessage resultInfo = msg.getMessageBody();
        try {
            domainBizService.registrationCompleteEvent(new VoucherInfo(resultInfo.getEventInfo().getVoucherNo(), DCVoucherType.getVoucherTypeByName(resultInfo.getEventInfo().getVoucherType())),
                    resultInfo.getEventInfo().getCorrectionCode(), resultInfo.getMemo(),CorrectionConstant.SYSTEM_OPERATION);
        } catch (Exception e) {
            mqNotifyRepository.delayPushExternalResultEvent(msg);
        }
    }

    @Override
    protected void overMaxRetryTimesMessage(ExternalResultInfoMessageInfo msg) {
        log.error("externalResultNotifyConsumerImpl push consumer overMaxRetryTimesMessage, msg:[{}]]", JSONObject.toJSONString(msg));
    }

    @Override
    protected int maxRetryTimes() {
        return 3;
    }

    @Override
    public void onMessage(ExternalResultInfoMessageInfo resultNotify) {
        super.dispatchMessage(resultNotify);
    }


}
