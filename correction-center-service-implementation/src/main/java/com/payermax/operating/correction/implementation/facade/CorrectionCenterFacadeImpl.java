package com.payermax.operating.correction.implementation.facade;

import com.google.common.collect.Lists;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.correction.core.common.annotation.FacadeMethod;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.CorrectionBaseInfo;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.GlobalOrderReadEvent;
import com.payermax.operating.correction.core.common.enums.OrderDetailType;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domainservice.repository.impl.IDomainRepositoryImpl;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.facade.common.CorrectionEventInfo;
import com.payermax.operating.correction.facade.common.OrderCorrectionBaseInfo;
import com.payermax.operating.correction.facade.enums.StatusEnum;
import com.payermax.operating.correction.facade.provider.CorrectionCenterFacade;
import com.payermax.operating.correction.facade.request.ExternalResultInfo;
import com.payermax.operating.correction.facade.response.CorrectionOrderInfoResponse;
import com.payermax.operating.correction.facade.response.UnionCorrectionOrderInfoResponse;
import com.payermax.operating.correction.implementation.assembler.CorrectionInfoDomainAssembler;
import com.payermax.operating.correction.integration.config.nacos.model.CorrectionSystemInfo;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyProcessorInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.ReconcileRedundantDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 差错接口API
 *
 * <AUTHOR>
 * @desc 差错中心服务提供
 * @date 2022 /3/4
 */
@DubboService(version = "1.0", timeout = 5000)
@Slf4j
public class CorrectionCenterFacadeImpl implements CorrectionCenterFacade {


    @Resource
    private DomainBizService domainBizService;

    @Resource
    private IDomainRepositoryImpl domainRepository;

    @Resource
    private CorrectionInfoDomainAssembler infoDomainAssembler;

    @Resource(name = "eventPushHandlerExecutor")
    private ThreadPoolTaskExecutor eventPushHandlerExecutor;


    /**
     * 批量查询差错处理结果
     *
     * @param eventInfos 订单差错信息
     * @return the result
     */
    @Override
    @FacadeMethod
    @SneakyThrows
    public Result<CorrectionOrderInfoResponse> eventQuery(CorrectionEventInfo eventInfos) {
        if (CorrectionConstant.RESULT_VOUCHER_INFO.contains(eventInfos.getCorrectionCode())) {
            //避免差错未收到订单中心的ack响应，金融交换已跟进支付请求单来查询三方单号问题
            Thread.sleep(1000);
        }
        DomainCorrectionInfo domainCorrectionInfo = domainBizService.eventQuery(new CorrectionBaseInfo(eventInfos.getCorrectionCode(), new VoucherInfo(eventInfos.getVoucherNo(), DCVoucherType.getVoucherTypeByName(eventInfos.getVoucherType()))));
        return ResultUtil.success(convertCorrectionOrderInfoResponse(domainCorrectionInfo));
    }

    @Override
    @FacadeMethod
    public Result<CorrectionOrderInfoResponse> pushSendEvent(OrderCorrectionBaseInfo eventInfo) {
        OperationDomainCorrectionInfo operationDomainCorrection = infoDomainAssembler.toCorrectionInfo(eventInfo);
        operationDomainCorrection.getBaseInfo().setVoucherInfo(new VoucherInfo(eventInfo.getCorrectionInfo().getVoucherNo(), DCVoucherType.getVoucherTypeByName(eventInfo.getCorrectionInfo().getVoucherType())));
        operationDomainCorrection.fillSystemCorrectionInfoDTO();
        eventPushHandlerExecutor.submit(() -> domainBizService.addEvent(operationDomainCorrection));
        return ResultUtil.success(new CorrectionOrderInfoResponse(eventInfo.getCorrectionInfo(), StatusEnum.PROCESSING, operationDomainCorrection.getBaseInfo().getCorrectionNo()));
    }

    @Override
    @FacadeMethod
    public Result<CorrectionOrderInfoResponse> eventQuery(String correctionNo) {
        DomainCorrectionInfo domainCorrectionInfo = domainRepository.getValidCorrectionOrderInfo(correctionNo, OrderDetailType.All_QUERY, GlobalOrderReadEvent.DISPLAY);
        return ResultUtil.success(convertCorrectionOrderInfoResponse(domainCorrectionInfo));
    }

    @Override
    @FacadeMethod
    public Result eventExternalResultNotify(ExternalResultInfo resultInfo) {
        domainBizService.registrationCompleteEvent(new VoucherInfo(resultInfo.getEventInfo().getVoucherNo(), DCVoucherType.getVoucherTypeByName(resultInfo.getEventInfo().getVoucherType())),
                resultInfo.getEventInfo().getCorrectionCode(), resultInfo.getMemo(), CorrectionConstant.SYSTEM_OPERATION);
        return ResultUtil.success();
    }

    @Override
    @FacadeMethod
    public Result<UnionCorrectionOrderInfoResponse> unionEventQuery(CorrectionEventInfo eventInfo) {
        CorrectionOrderInfoDTO orderInfoDTO = domainRepository.getValidCorrectionOrderInfoDto(eventInfo.getCorrectionCode(), eventInfo.getVoucherNo());

        List<CorrectionSystemInfo> systemInfos = domainRepository.getSystemInfos(orderInfoDTO.getBaseInfo().getCorrectionCode(), orderInfoDTO.getSysSource());
        List<String> sysSources = systemInfos.stream().map(CorrectionSystemInfo::getName).collect(Collectors.toList());

        CorrectionBasicInfoDTO basicInfo = domainRepository.getValidParentReasonBasicInfo(orderInfoDTO.getBaseInfo().getCorrectionCode());

        if (CorrectionConstant.EXTERNAL_DIFFERENCE_CORRECTION_CODE.contains(orderInfoDTO.getBaseInfo().getCorrectionCode())) {
            ReconcileRedundantDTO reconcileRedundantDTO = orderInfoDTO.getOriginalTradeRedundantInfo();
            UnionCorrectionOrderInfoResponse orderInfoResponse = infoDomainAssembler.toUnionCorrection(orderInfoDTO, basicInfo, reconcileRedundantDTO, sysSources);
            if (orderInfoDTO.getOriVoucherInfo().getVoucherType()==DCVoucherType.THIRD_ORDER_NO){
                orderInfoResponse.setThirdOrderNo(orderInfoDTO.getOriVoucherInfo().getVoucherNo());
            }else {
                orderInfoResponse.setChannelCommitNo(orderInfoDTO.getOriVoucherInfo().getVoucherNo());
            }
            return ResultUtil.success(orderInfoResponse);
        }

        GlobalOrderInfo globalOrderInfo = domainRepository.getGlobalOrderInfo(orderInfoDTO.getOriVoucherInfo(), GlobalOrderReadEvent.DISPLAY);
        globalOrderInfo.channelCommitInfoValidation();
        StrategyProcessorInfo strategyInfo = null;
        //当差错还未处理，未选择策略code时不去查询校验
        //这里查询strategyInfo 只是为里取strategyCodeName字段，
        // 针对该查询接口，如果为空返回为空即可，不应该中断流程
        if(StringUtils.isNotBlank(orderInfoDTO.getStrategyCode())){
            strategyInfo = domainRepository.getValidStrategyInfo(orderInfoDTO.getStrategyCode());
        }
        return ResultUtil.success(infoDomainAssembler.toUnionCorrection(orderInfoDTO, globalOrderInfo, basicInfo, strategyInfo, sysSources));
    }

    private StatusEnum convertStatus(ProcessStatusEnum status) {
        switch (status) {
            case SUCCESS:
                return StatusEnum.SUCCESS;
            case FAILURE:
                return StatusEnum.FAILURE;
            default:
                return StatusEnum.PROCESSING;
        }
    }

    private CorrectionOrderInfoResponse convertCorrectionOrderInfoResponse(DomainCorrectionInfo domainCorrectionInfo) {
        CorrectionEventInfo correctionEventInfo = new CorrectionEventInfo(domainCorrectionInfo.getVoucherNo()
                , domainCorrectionInfo.getVoucherType().name()
                , domainCorrectionInfo.getBasicInfoDTO().getCorrectionCode(), domainCorrectionInfo.getDetailDesc(), domainCorrectionInfo.getRedundantInfo().getErrorCode());
        CorrectionOrderInfoResponse orderInfoResponse = new CorrectionOrderInfoResponse(correctionEventInfo, this.convertStatus(domainCorrectionInfo.getProcessStatus()), domainCorrectionInfo.getCorrectionNo());
        //差错单号数组0，扩展单号数组1
        List<String> strategyList = Lists.newArrayList(domainCorrectionInfo.getCorrectionNo(), DCVoucherType.getVoucherPair(domainCorrectionInfo.getVoucherNo(), domainCorrectionInfo.getVoucherType()).getRight());
        //从策略中获取三方单号
        orderInfoResponse.setThirdOrderNo(DCVoucherType.getExtraOrderNo(strategyList, domainCorrectionInfo.getVoucherType()));
        return orderInfoResponse;
    }

}
