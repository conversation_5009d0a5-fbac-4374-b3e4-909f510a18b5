package com.payermax.operating.correction.implementation.scheduled;

import com.google.common.collect.Lists;
import com.payermax.common.lang.model.dto.request.PageRequest;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domainservice.service.DomainOperationService;
import com.payermax.operating.correction.facade.provider.operation.CorrectionOperationOrderHandlerFacade;
import com.payermax.operating.correction.integration.config.nacos.constant.NacosGlobalConfigProperties;
import com.payermax.operating.correction.integration.persistence.kvstore.repository.KVRepository;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.QueryCorrectionOrderPageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.List;

@Slf4j
@Component
@EnableScheduling
public class ScheduledTask {

    @Resource
    private KVRepository kvRepository;

    @Resource
    private NacosGlobalConfigProperties nacosGlobalConfigProperties;

    @Resource
    private DomainOperationService domainOperationService;

    @Resource
    private CorrectionOperationOrderHandlerFacade operationOrderHandlerFacade;

    @Scheduled(cron = "0 0 0-2,15-23 * * ? ")
    public void pendingAdvance() {
        if (BooleanUtils.isTrue(nacosGlobalConfigProperties.getScheduledPendingSwitch())) {
            long node = kvRepository.incr(CorrectionConstant.SCHEDULED_PENDING_ADVANCE_LOCK) % nacosGlobalConfigProperties.getScheduledPendingNodes();
            log.info("ScheduledTask pendingAdvance apply lock node :{}", node);
            //开始时间
            Calendar startDate = Calendar.getInstance();
            startDate.add(Calendar.DATE, nacosGlobalConfigProperties.getScheduledPendingStartDay());

            //结束时间
            Calendar endDate = Calendar.getInstance();
            endDate.add(Calendar.DATE, nacosGlobalConfigProperties.getScheduledPendingEndDay());

            //1. 差错一定时间范围内处于pending的数据，每次100条
            QueryCorrectionOrderPageDTO queryPage = QueryCorrectionOrderPageDTO.builder()
                    .page(new PageRequest(CorrectionConstant.NUM_ONE_HUNDRED, node + 1, startDate.getTimeInMillis(), endDate.getTimeInMillis()))
                    .status(Lists.newArrayList(ProcessStatusEnum.PENDING.name()))
                    .build();
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<CorrectionOrderInfoDTO> page = domainOperationService.getCorrectionOrderByPage(queryPage);
            List<CorrectionOrderInfoDTO> records = page.getRecords();
            for (CorrectionOrderInfoDTO record : records) {
                try {
                    operationOrderHandlerFacade.queryCorrectionOrderDetailInfo(record.getBaseInfo().getCorrectionNo());
                } catch (Exception e) {
                    log.error("ScheduledTask pending advance base system exception:", e);
                }
            }
        }

    }
}
