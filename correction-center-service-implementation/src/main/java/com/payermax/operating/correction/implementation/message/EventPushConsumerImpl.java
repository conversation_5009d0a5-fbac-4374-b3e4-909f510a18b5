package com.payermax.operating.correction.implementation.message;

import com.alibaba.fastjson.JSONObject;
import com.payermax.infra.ionia.rocketmq.handler.BaseMqMessageListener;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.payermax.operating.correction.implementation.assembler.CorrectionInfoDomainAssembler;
import com.payermax.operating.correction.implementation.message.dto.CorrectionEventMessageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 差错事件接收消息消费者
 * @date 2022/9/27
 */
@RocketMQMessageListener(
        topic = "${correction.topic.event.push}",
        consumerGroup = "${rocketmq.consumer.group}",
        consumeThreadNumber = 5
)
@Component
@Slf4j
public class EventPushConsumerImpl extends BaseMqMessageListener<CorrectionEventMessageInfo> implements RocketMQListener<CorrectionEventMessageInfo> {

    @Resource
    private CorrectionInfoDomainAssembler serviceImplAssembler;

    @Resource
    private DomainBizService domainBizService;

    @Resource(name = "eventPushHandlerExecutor")
    private ThreadPoolTaskExecutor eventPushHandlerExecutor;

    @Override
    protected void handleMessage(CorrectionEventMessageInfo msg){
        log.info("correction eventPush consumer handleMessage,msg : [{}]", JSONObject.toJSONString(msg));
        OperationDomainCorrectionInfo operationDomainCorrection = serviceImplAssembler.toOperationDomainCorrectionInfo(msg.getMessageBody());
        operationDomainCorrection.getBaseInfo().getVoucherInfo().setVoucherType(DCVoucherType.getVoucherTypeByName(msg.getMessageBody().getCorrectionInfo().getVoucherType()));
        operationDomainCorrection.fillSystemCorrectionInfoDTO();
        eventPushHandlerExecutor.submit(() -> domainBizService.addEvent(operationDomainCorrection));
    }

    @Override
    protected void overMaxRetryTimesMessage(CorrectionEventMessageInfo msg) {
        log.error("correction event push consumer overMaxRetryTimesMessage, msg:[{}]]", JSONObject.toJSONString(msg));
    }

    @Override
    protected int maxRetryTimes() {
        return 2;
    }

    @Override
    public void onMessage(CorrectionEventMessageInfo eventMessageInfo) {
        super.dispatchMessage(eventMessageInfo);
    }


}
