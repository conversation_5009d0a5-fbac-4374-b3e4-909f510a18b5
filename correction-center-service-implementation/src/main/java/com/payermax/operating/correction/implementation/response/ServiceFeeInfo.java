package com.payermax.operating.correction.implementation.response;

import com.payermax.common.lang.util.money.Money;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @desc 服务信息
 * @date 2022/3/16
 */
@Data
@NoArgsConstructor
@ToString
public class ServiceFeeInfo {

    /**
     * 责任方
     * @mock owner
     */
    private String responsibleParty;
    /**
     * 受益方
     * @mock user
     */
    private String beneficiary;

    /**
     * 服务费
     */
    private Money serviceAmount;

}
