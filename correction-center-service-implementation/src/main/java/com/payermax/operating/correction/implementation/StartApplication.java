package com.payermax.operating.correction.implementation;

import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySources;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;

/**
 * <AUTHOR>
 * @desc bootstrap main application
 * @date 2022/9/20
 */
@SpringBootApplication(scanBasePackages = {"com.payermax.operating.correction", "com.payermax.infra.*","com.payermax.components.*"},exclude = MongoAutoConfiguration.class)
@MapperScan(basePackages = "com.payermax.operating.correction.core.dal.dao")
@EnableDubbo(scanBasePackages = "com.payermax.operating.correction.implementation.facade")
@NacosPropertySources({
                @NacosPropertySource(dataId = CorrectionConstant.STRATEGY_PROCESS_JSON, groupId = CorrectionConstant.CORRECTION_CENTER, autoRefreshed = true),
                @NacosPropertySource(dataId = CorrectionConstant.CORRECTION_SYSTEM_JSON, groupId = CorrectionConstant.CORRECTION_CENTER, autoRefreshed = true),
                @NacosPropertySource(dataId = CorrectionConstant.MATCHER_RULE_JSON, groupId = CorrectionConstant.CORRECTION_CENTER, autoRefreshed = true),
                @NacosPropertySource(dataId = CorrectionConstant.PAY_METHOD_MAPPING_JSON, groupId = CorrectionConstant.CORRECTION_CENTER, autoRefreshed = true),
                @NacosPropertySource(dataId = CorrectionConstant.APPLICATION_PROPERTIES, groupId = CorrectionConstant.CORRECTION_CENTER, autoRefreshed = true),
                @NacosPropertySource(dataId = CorrectionConstant.ROCKETMQ, groupId = CorrectionConstant.CORRECTION_CENTER, autoRefreshed = true),
                @NacosPropertySource(dataId = CorrectionConstant.DING_TALK_MAPPING_JSON, groupId = CorrectionConstant.CORRECTION_CENTER, autoRefreshed = true),
                @NacosPropertySource(dataId = CorrectionConstant.STRATEGY_BLACKLIST_JSON, groupId = CorrectionConstant.CORRECTION_CENTER, autoRefreshed = true),
})
@Slf4j
public class StartApplication {

    public static void main(String[] args) {
        log.info("Service started...");

        SpringApplication springApplication = new SpringApplication(StartApplication.class);

        springApplication.run(args);

        log.info("Service started successful!");
    }
}
