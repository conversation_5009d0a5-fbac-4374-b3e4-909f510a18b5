package com.payermax.operating.correction.implementation.facade;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.correction.core.common.annotation.FacadeMethod;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.payermax.operating.correction.domainservice.service.DomainOperationService;
import com.payermax.operating.correction.facade.provider.operation.CorrectionOperationManualProvider;
import com.payermax.operating.correction.facade.request.CorrectionStrategyCodeHandlerInfo;
import com.payermax.operating.correction.implementation.assembler.CorrectionInfoDomainAssembler;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @desc 差错运营平台facade实现类
 * @date 2022/10/18
 */
@SuppressWarnings("unused")
@DubboService(version = "1.0")
@Slf4j
public class CorrectionOperationManualProviderImpl implements CorrectionOperationManualProvider {

    @Resource
    private DomainBizService domainBizService;

    @Resource
    private DomainOperationService domainOperationService;

    @Resource
    private CorrectionInfoDomainAssembler infoDomainAssembler;

    @Override
    @FacadeMethod
    public Result execStrategySkipApproval(CorrectionStrategyCodeHandlerInfo strategyCodeHandlerInfo) {
        OperationDomainCorrectionInfo correctionInfoDTO = infoDomainAssembler.toOperationDomainCorrectionInfo(strategyCodeHandlerInfo);
        domainBizService.chooseStrategyCode(correctionInfoDTO);
        return ResultUtil.success();
    }

    @Override
    @FacadeMethod
    public Result processStrategy(String correctionNo) {
        domainBizService.execStrategy(correctionNo, CorrectionConstant.MINUS_ONE);
        return ResultUtil.success();
    }
}
