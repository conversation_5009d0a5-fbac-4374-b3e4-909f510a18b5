package com.payermax.operating.correction.implementation.facade;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.payermax.common.lang.model.dto.Page;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.correction.core.common.annotation.FacadeMethod;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.core.common.utils.ObjectsUtils;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.payermax.operating.correction.domainservice.service.DomainOperationService;
import com.payermax.operating.correction.facade.common.CorrectionOperationBasicInfo;
import com.payermax.operating.correction.facade.common.OrderInfo;
import com.payermax.operating.correction.facade.provider.operation.CorrectionOperationRelationQueryFacade;
import com.payermax.operating.correction.facade.request.CorrectionPrimaryQueryRequest;
import com.payermax.operating.correction.facade.response.CorrectionRelationInfo;
import com.payermax.operating.correction.facade.response.RelationDetailResponse;
import com.payermax.operating.correction.implementation.assembler.CorrectionInfoDomainAssembler;
import com.payermax.operating.correction.implementation.assembler.DomainAssemblerUtils;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.datacenter.repository.DataCenterRepository;
import com.payermax.operating.correction.integration.utils.ListSizeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Future;

/**
 * <AUTHOR>
 * @desc 差错关联查询
 * @date 2022/11/13
 */
@SuppressWarnings("unused")
@DubboService(version = "1.0")
@Slf4j
public class CorrectionOperationRelationQueryFacadeImpl implements CorrectionOperationRelationQueryFacade {

    @Resource
    private DomainBizService domainBizService;

    @Resource
    private DomainOperationService domainOperationService;

    @Resource
    private CorrectionInfoDomainAssembler infoDomainAssembler;

    @Resource
    private IDomainRepository domainRepository;

    @Resource
    private DataCenterRepository dataCenterRepository;

    @Resource(name = "bizAsyncExecutor")
    private ThreadPoolTaskExecutor bizAsyncExecutor;

    @Override
    @FacadeMethod
    public Result<Page<CorrectionRelationInfo>> queryRelationList(CorrectionPrimaryQueryRequest primaryQuery) {
        List<String> corrections = ObjectsUtils.strToList(primaryQuery.getCorrectionNos(), Symbols.COMMA);
        List<String> voucherNos = ObjectsUtils.strToList(primaryQuery.getVoucherNos(), Symbols.COMMA);
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<CorrectionOrderInfoDTO> page = domainOperationService.getCorrectionOrderByPage(primaryQuery.getPage(), corrections, voucherNos);
        List<CorrectionOrderInfoDTO> records = page.getRecords();
        Map<VoucherInfo, Future<GlobalOrderInfo>> map = Maps.newHashMap();
        //异步发送请求去联合查询
        for (CorrectionOrderInfoDTO record : records) {
            VoucherInfo oriVoucherInfo = record.getOriVoucherInfo();
            VoucherInfo resVoucherInfo = record.getResVoucherInfo();
            Future<GlobalOrderInfo> oriFuture = bizAsyncExecutor.submit(() -> domainOperationService.getGlobalOrderInfo(oriVoucherInfo.getVoucherNo(), oriVoucherInfo.getVoucherType()));
            map.put(oriVoucherInfo, oriFuture);
            if (Objects.nonNull(resVoucherInfo)){
                Future<GlobalOrderInfo> resFuture = bizAsyncExecutor.submit(() -> domainOperationService.getGlobalOrderInfo(resVoucherInfo.getVoucherNo(), resVoucherInfo.getVoucherType()));
                map.put(resVoucherInfo, resFuture);
            }
        }
        Map<String, String> allStrategyMapping = domainOperationService.getAllMappingStrategyCodeInfo();
        Map<String, String> allReasonInfoMapping = domainOperationService.getAllChildrenMappingReasonInfo();
        Map<String, CorrectionBasicInfoDTO> parentReasonInfo = domainOperationService.getAllMappingParentReasonInfo();
        //数据组装
        List list = Lists.newArrayList();
        for (CorrectionOrderInfoDTO record : records) {
            GlobalOrderInfo oriGlobalOrderInfo = Optional.ofNullable(map.get(record.getOriVoucherInfo())).map(e -> {
                try {
                    return e.get();
                } catch (Exception ex) {
                    log.error("query original error oriOrderInfo:{}", record.getOriVoucherInfo().toString(), ex);
                }
                return Nullable.getNullVal();
            }).orElse(Nullable.getNullVal());
            GlobalOrderInfo resOrderInfo=Nullable.getNullVal();
            if (Objects.nonNull(record.getResVoucherInfo())){
                resOrderInfo = Optional.ofNullable(map.get(record.getResVoucherInfo())).map(e -> {
                    try {
                        return e.get();
                    } catch (Exception ex) {
                        log.warn("query response error resOrderInfo:{}", record.getResVoucherInfo().toString(), ex);
                    }
                    return Nullable.getNullVal();
                }).orElse(Nullable.getNullVal());
            }

            list.add(this.wrapRelationInfo(allStrategyMapping, allReasonInfoMapping, parentReasonInfo, record, oriGlobalOrderInfo, resOrderInfo));
        }
        return ResultUtil.success(DomainAssemblerUtils.buildPage((int) page.getCurrent(), (int) page.getTotal(), (int) page.getSize(), list));
    }

    @Override
    @FacadeMethod
    public Result<RelationDetailResponse> queryDetail(String correctionNo) {
        CorrectionOrderInfoDTO orderInfoDTO = domainRepository.getValidCorrectionOrderInfoDto(correctionNo);
        RelationDetailResponse relationDetailResponse = new RelationDetailResponse(orderInfoDTO.getBaseInfo().getCorrectionNo(), DomainAssemblerUtils.dateTimeToStr(orderInfoDTO.getOpBasicInfo().getCompleteTime()), orderInfoDTO.getProcessStatus());
        //填充原始订单详情信息
        GlobalOrderInfo orderInfo = domainOperationService.getGlobalOrderInfo(orderInfoDTO.getBaseInfo().getVoucherInfo().getVoucherNo(), orderInfoDTO.getBaseInfo().getVoucherInfo().getVoucherType());
        relationDetailResponse.setOriginalOrderInfo(this.getOrderDetail(orderInfo));
        //填充处理订单详情信息
        if (Objects.nonNull(orderInfoDTO.getResVoucherInfo())) {
            GlobalOrderInfo resGlobalOrderInfo = domainOperationService.getGlobalOrderInfo(orderInfoDTO.getResVoucherInfo().getVoucherNo(), orderInfoDTO.getResVoucherInfo().getVoucherType());
            relationDetailResponse.setHandlerOrderInfo(this.getOrderDetail(resGlobalOrderInfo));
        }
        return ResultUtil.success(relationDetailResponse);
    }

    private List<RelationDetailResponse.OrderDetailInfo> getOrderDetail(GlobalOrderInfo globalOrderInfo) {
        return Lists.newArrayList(infoDomainAssembler.toRelationOrderDetailInfo(globalOrderInfo));
    }

    private CorrectionRelationInfo wrapRelationInfo(Map<String, String> allStrategyMapping, Map<String, String> allReasonInfoMapping, Map<String, CorrectionBasicInfoDTO> parentReasonInfo
            , CorrectionOrderInfoDTO orderInfoDTO, GlobalOrderInfo oriGlobalOrderInfo, GlobalOrderInfo resGlobalOrderInfo) {
        CorrectionOperationBasicInfo basicInfo = infoDomainAssembler.toCorrectionOperation(orderInfoDTO, allStrategyMapping, allReasonInfoMapping, parentReasonInfo);
        OrderInfo oriOrderInfo = Optional.of(DomainAssemblerUtils.getOrderInfo(oriGlobalOrderInfo)).filter(e -> e.size() > 0 && ListSizeUtils.listSizeEqualOne(e,"matchOrderInfo")).map(e -> e.get(0)).orElse(Nullable.getNullVal());//CHECKED
        OrderInfo resOrderInfo = Optional.of(DomainAssemblerUtils.getOrderInfo(resGlobalOrderInfo)).filter(e -> e.size() > 0 && ListSizeUtils.listSizeEqualOne(e,"matchResOrderInfo")).map(e -> e.get(0)).orElse(Nullable.getNullVal());//CHECKED
        return new CorrectionRelationInfo(basicInfo, oriOrderInfo, resOrderInfo, DomainAssemblerUtils.dateTimeToStr(orderInfoDTO.getOpBasicInfo().getCompleteTime()));
    }

}
