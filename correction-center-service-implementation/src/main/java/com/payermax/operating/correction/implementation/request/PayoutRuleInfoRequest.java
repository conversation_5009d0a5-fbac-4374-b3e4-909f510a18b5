package com.payermax.operating.correction.implementation.request;

import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsCoreInfo;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsRuleInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @desc 出款规则信息
 * @date 2022/12/15
 */
@Data
@NoArgsConstructor
public class PayoutRuleInfoRequest {

    private String correctionNo;

    private PayoutsCoreInfo payoutsCoreInfo;

    private PayoutsRuleInfo payoutsRuleInfo;
}
