package com.payermax.operating.correction.implementation.task;

import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 对账出款退款自动登记处理
 * 出款失败， 对账成功，且对账发现退票
 */
@Component
@Slf4j
@JobHandler(value = "payoutBouncebackByReconcileJobHandler")
public class PayoutBouncebackByReconcileJobHandlerJobHandler extends IJobHandler {
    @Autowired
    private DomainBizService domainBizService;

    @Autowired
    private IDomainRepository iDomainRepository;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        List<String> correctionNos = iDomainRepository.queryDataCenterOrderNo(CorrectionConstant.TEMPLATE_ID_PAYOUT_BOUNCEBACK_BY_RECONCILE, "correction_no");
        for (String correctionNo : correctionNos) {
            try {
                log.info("payoutBouncebackByReconcileJobHandler correctionNo:{}", correctionNo);
                domainBizService.jobAutoHandler(correctionNo);
            } catch (Exception e) {
                log.error("payoutBouncebackByReconcileJobHandler error {} correctionNo:{}", correctionNo,e.getMessage(), e);
            }
        }

        return ReturnT.SUCCESS;
    }
}
