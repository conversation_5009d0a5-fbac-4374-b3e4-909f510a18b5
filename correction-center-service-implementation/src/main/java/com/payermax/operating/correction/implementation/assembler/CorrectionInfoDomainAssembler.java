package com.payermax.operating.correction.implementation.assembler;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.dto.OperationManualFillIn;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.ExtendEnum;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.enums.UserInfoEnum;
import com.payermax.operating.correction.core.common.utils.ObjectsUtils;
import com.payermax.operating.correction.domain.dto.ChannelReconcileRedundantDTO;
import com.payermax.operating.correction.domain.dto.EventTransferInfo;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domain.dto.RetryInfo;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domainservice.processor.utils.OrderStatusUtils;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.facade.common.*;
import com.payermax.operating.correction.facade.request.*;
import com.payermax.operating.correction.facade.response.CorrectionOrderDetailInfoResponse;
import com.payermax.operating.correction.facade.response.CorrectionOrderHandlerBasicInfo;
import com.payermax.operating.correction.facade.response.RelationDetailResponse;
import com.payermax.operating.correction.facade.response.UnionCorrectionOrderInfoResponse;
import com.payermax.operating.correction.implementation.message.dto.CorrectionEventMessageInfo;
import com.payermax.operating.correction.integration.config.nacos.model.CorrectionPayoutsCountryInfo;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyProcessorInfo;
import com.payermax.operating.correction.integration.enums.ChannelOrderStatus;
import com.payermax.operating.correction.integration.enums.RefundTypeSource;
import com.payermax.operating.correction.integration.persistence.rdms.dto.*;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * The interface Correction info domain assembler.
 *
 * <AUTHOR>
 * @desc 差错对象转换类
 * @date 2022/4/1
 */
@Mapper(componentModel = "spring", imports = {Optional.class, Date.class, CorrectionAmountInfo.class, ProcessStatusEnum.class,
        DCVoucherType.class, CorrectionEventInfo.class, Collectors.class, DomainAssemblerUtils.class, StrategyProcessorInfo.class,
        TradeType.class, OperationManualFillIn.class, ExtendEnum.class, RelationDetailResponse.MerchantInfo.class, StringUtils.class,
        UserInfoEnum.class, Money.class, BigDecimal.class, Currency.class, RefundTypeSource.class, ObjectUtils.class, RetryInfo.class,
        ObjectsUtils.class, Symbols.class, EventTransferInfo.class, ChannelOrderStatus.class, OrderStatusUtils.class,ObjectsUtils.class})
public interface CorrectionInfoDomainAssembler {

    /**
     * To correction info dto.
     *
     * @param eventMessageInfo mqCorrectionInfo
     * @return the correction info dto
     */
    @Mappings({
            @Mapping(target = "baseInfo.voucherInfo.voucherNo", source = "correctionInfo.voucherNo"),
            @Mapping(target = "baseInfo.correctionCode", source = "correctionInfo.correctionCode"),
            @Mapping(target = "channelReconcileRedundant", expression = "java(toChannelReconcile(eventMessageInfo.getReconcileRedundantInfo()))"),
            @Mapping(target = "payTotalMoney", expression = "java(Optional.ofNullable(eventMessageInfo.getAmountInfo()).map(CorrectionAmountInfo::getPayTotalMoney).orElse(null))"),
            @Mapping(target = "retryInfo.msgRetry", expression = "java(ObjectUtils.defaultIfNull(eventMessageInfo.getRetryNum(),0))"),
    })
    OperationDomainCorrectionInfo toOperationDomainCorrectionInfo(CorrectionEventMessageInfo.EventMessageInfo eventMessageInfo);

    /**
     * To correction info dto correction info dto.
     *
     * @param strategyCodeHandlerInfo the strategy code handler info
     * @return the correction info dto
     */
    @Mappings({
            @Mapping(target = "baseInfo.correctionNo", source = "correctionNo"),
            @Mapping(target = "strategyCode", source = "strategyCode"),
            @Mapping(target = "operator", source = "operator"),
            @Mapping(target = "operationManual.extraUserInfo", source = "extraInfoJson"),
            @Mapping(target = "operationManual.memo", source = "memo"),
            @Mapping(target = "transferInfo", expression = "java(DomainAssemblerUtils.transferInfo(strategyCodeHandlerInfo.getForwardInfo()))"),
    })
    OperationDomainCorrectionInfo toOperationDomainCorrectionInfo(CorrectionStrategyCodeHandlerInfo strategyCodeHandlerInfo);

    /**
     * To operation strategy info correction operation strategy info dto.
     *
     * @param strategyInfo the strategy info
     * @return the correction operation strategy info dto
     */
    @Mappings({
            @Mapping(target = "strategyCode", source = "strategyInfo.strategyCode"),
            @Mapping(target = "strategyName", source = "strategyInfo.strategyName"),
            @Mapping(target = "extendShow", expression = "java(ExtendEnum.getByName(strategyInfo.getExtendShow()))"),
            @Mapping(target = "extraUserInfo", expression = "java(UserInfoEnum.getByName(strategyInfo.getExtraUserInfo()))"),
            @Mapping(target = "opBasicInfo", expression = "java(DomainAssemblerUtils.getOperationBasicInfo(strategyInfo.getRecordInfo(),strategyInfo.getValid()))"),
    })
    CorrectionOperationStrategyInfoDTO toOperationStrategyInfo(StrategyInfo strategyInfo);

    /**
     * To strategy info
     *
     * @param operationStrategy the operation strategy info dto
     * @return the strategy info
     */
    @Mappings({
            @Mapping(target = "valid", expression = "java(operationStrategy.getOpBasicInfo().getValid().name())"),
            @Mapping(target = "extendShow", expression = "java(Optional.ofNullable(operationStrategy.getExtendShow()).map(ExtendEnum::name).orElse(null))"),
            @Mapping(target = "extraUserInfo", expression = "java(Optional.ofNullable(operationStrategy.getExtraUserInfo()).map(UserInfoEnum::name).orElse(null))"),
            @Mapping(target = "recordInfo", expression = "java(DomainAssemblerUtils.getRecordInfo(operationStrategy.getOpBasicInfo()))")
    })
    StrategyInfo toStrategyInfo(CorrectionOperationStrategyInfoDTO operationStrategy);

    /**
     * To reason info correction basic info dto.
     *
     * @param saveReasonBasicInfo the save reason basic info
     * @return the correction basic info dto
     */
    @Mappings({
            @Mapping(target = "correctionCode", source = "basicInfo.correctionCode"),
            @Mapping(target = "correctionName", source = "basicInfo.correctionName"),
            @Mapping(target = "parentCorrectionCode", source = "parentBasicInfo.correctionCode"),
            @Mapping(target = "tradeType", expression = "java(TradeType.valueOf(saveReasonBasicInfo.getTradeType()))"),
            @Mapping(target = "opBasicInfo", expression = "java(DomainAssemblerUtils.getOperationBasicInfo(saveReasonBasicInfo.getRecordInfo(),saveReasonBasicInfo.getValid()))"),
            @Mapping(target = "execValidateRules", expression = "java(DomainAssemblerUtils.getValidateRule(saveReasonBasicInfo.getExecCondition()))")
    })
    CorrectionBasicInfoDTO toReasonInfo(SaveReasonBasicInfo saveReasonBasicInfo);

    /**
     * To reason basic info reason basic info.
     *
     * @param basicInfoDTO the basic info dto
     * @param parentMap    the parent map
     * @return the reason basic info
     */
    @Mappings({
            @Mapping(target = "basicInfo.correctionCode", source = "basicInfoDTO.correctionCode"),
            @Mapping(target = "basicInfo.correctionName", source = "basicInfoDTO.correctionName"),
            @Mapping(target = "valid", expression = "java(basicInfoDTO.getOpBasicInfo().getValid().name())"),
            @Mapping(target = "tradeType", expression = "java(basicInfoDTO.getTradeType().name())"),
            @Mapping(target = "execCondition", expression = "java(DomainAssemblerUtils.getExecCondition(basicInfoDTO.getExecValidateRules()))"),
            @Mapping(target = "recordInfo", expression = "java(DomainAssemblerUtils.getRecordInfo(basicInfoDTO.getOpBasicInfo()))"),
            @Mapping(target = "parentBasicInfo", expression = "java(DomainAssemblerUtils.getParentBasicInfo(basicInfoDTO.getParentCorrectionCode(),parentMap))"),

    })
    ReasonBasicInfo toReasonBasicInfo(CorrectionBasicInfoDTO basicInfoDTO, Map<String, CorrectionBasicInfoDTO> parentMap);


    /**
     * To order handler basic info correction order handler basic info.
     *
     * @param orderInfoDTO the order info dto
     * @return the correction order handler basic info
     */
    @Mappings({
            @Mapping(target = "correctionInfo.correctionNo", source = "baseInfo.correctionNo"),
            @Mapping(target = "correctionInfo.correctionCode", source = "baseInfo.correctionCode"),
            @Mapping(target = "correctionInfo.tradeType", expression = "java(Optional.ofNullable(correctionBaseInfo.getTradeType()).map(TradeType::name).orElse(StringUtils.EMPTY))"),
            @Mapping(target = "correctionInfo.strategyCode", source = "strategyCode"),
            @Mapping(target = "status", source = "processStatus"),
            @Mapping(target = "recordInfo", expression = "java(DomainAssemblerUtils.getRecordInfo(orderInfoDTO.getOpBasicInfo()))"),
            @Mapping(target = "merchantNo", source = "originalTradeRedundantInfo.merchantNo"),
            @Mapping(target="redundantInfo",expression = "java(toChannelReconcileInfo(orderInfoDTO.getOriginalTradeRedundantInfo()))"),
            @Mapping(target = "memo", expression = "java(Optional.ofNullable(orderInfoDTO.getOperationManual()).map(OperationManualFillIn::getMemo).orElse(null))"),
    })
    CorrectionOrderHandlerBasicInfo toOrderHandlerBasicInfo(CorrectionOrderInfoDTO orderInfoDTO);

    /**
     * To order detail info correction order detail info response.
     *
     * @param correctionOrderInfo the correction order info
     * @return the correction order detail info response
     */
    @Mappings({
            @Mapping(target = "correctionInfo.correctionNo", source = "correctionNo"),
            @Mapping(target = "correctionInfo.correctionName", source = "basicInfoDTO.correctionName"),
            @Mapping(target = "correctionInfo.correctionCode", source = "basicInfoDTO.correctionCode"),
            @Mapping(target = "correctionInfo.voucherNo", source = "oriVoucherInfo.voucherNo"),
            @Mapping(target = "memo", source = "memo"),
            @Mapping(target = "orderInfoList", expression = "java(DomainAssemblerUtils.getOrderInfo(correctionOrderInfo.getGlobalOrderInfo()))"),
            @Mapping(target = "uploadList", expression = "java(DomainAssemblerUtils.getUploadList(correctionOrderInfo.getExtendInfoList()))"),
            @Mapping(target = "refundInfoList", expression = "java(DomainAssemblerUtils.getRefundOrderInfo(correctionOrderInfo.getGlobalOrderInfo()))"),
    })
    CorrectionOrderDetailInfoResponse toOrderDetailInfo(DomainCorrectionInfo correctionOrderInfo);

    /**
     * To handler record info handler record info.
     *
     * @param handlerRecordDTO   the handler record dto
     * @param allStrategyMapping the all strategy mapping
     * @return the handler record info
     */
    @Mappings({
            @Mapping(target = "strategyCode", source = "handlerRecordDTO.handlerInfo.strategyCode"),
            @Mapping(target = "strategyName", expression = "java(allStrategyMapping.get(handlerRecordDTO.getHandlerInfo().getStrategyCode()))"),
            @Mapping(target = "handlerEvent", source = "handlerRecordDTO.handlerInfo.handlerType"),
            @Mapping(target = "handlerStatus", expression = "java(handlerRecordDTO.getStatus().name())"),
            @Mapping(target = "desc", source = "handlerRecordDTO.processResult"),
            @Mapping(target = "operator", source = "handlerRecordDTO.opBasicInfo.operator"),
            @Mapping(target = "utcCreateTimeStamp", source = "handlerRecordDTO.opBasicInfo.utcCreate"),
    })
    HandlerRecordInfo toHandlerRecordInfo(CorrectionOrderHandlerRecordDTO handlerRecordDTO, Map<String, String> allStrategyMapping);

    /**
     * To correction info dto correction info dto.
     *
     * @param approvalEvent the approval event
     * @return the correction info dto
     */
    @Mappings({
            @Mapping(target = "baseInfo.correctionNo", source = "correctionNo"),
            @Mapping(target = "operationManual", expression = "java(new OperationManualFillIn(approvalEvent.getDesc()))"),
            @Mapping(target = "strategyCode", source = "strategyCode"),
            @Mapping(target = "operationCorrectionCode", source = "operationCorrectionCode"),
            @Mapping(target = "operationManual.memo", source = "memo"),
            @Mapping(target = "operator", source = "operator"),
    })
    OperationDomainCorrectionInfo toOperationDomainCorrectionInfo(ApprovalEvent approvalEvent);

    /**
     * Manual to correction info dto.
     *
     * @param baseInfo the baseInfo
     * @return the correction info dto
     */
    @Mappings({
            @Mapping(target = "baseInfo.correctionCode", source = "correctionInfo.correctionCode"),
            @Mapping(target = "baseInfo.detailDesc", source = "correctionInfo.detailDesc"),
            @Mapping(target = "baseInfo.detailCode", source = "correctionInfo.detailCode"),
            @Mapping(target = "sysSource", source = "sysSource"),
            @Mapping(target = "retryType", source = "retryType"),
            @Mapping(target = "payTotalMoney", expression = "java(Optional.ofNullable(baseInfo.getAmountInfo()).map(CorrectionAmountInfo::getPayTotalMoney).orElse(null))"),
            @Mapping(target = "retryInfo", expression = "java(new RetryInfo())"),
    })
    OperationDomainCorrectionInfo toCorrectionInfo(OrderCorrectionBaseInfo baseInfo);

    /**
     * To relation info correction relation info.
     *
     * @param orderInfoDTO            the order info dto
     * @param allStrategyMapping      the all strategy mapping
     * @param allReasonInfoMapping    the all reason info mapping
     * @param parentReasonInfoMapping the parent reason info mapping
     * @return the correction relation info
     */
    @Mappings({
            @Mapping(target = "correctionNo", source = "orderInfoDTO.baseInfo.correctionNo"),
            @Mapping(target = "voucherNo", source = "orderInfoDTO.oriVoucherInfo.voucherNo"),
            @Mapping(target = "correctionName", expression = "java(parentReasonInfoMapping.get(orderInfoDTO.getBaseInfo().getCorrectionCode()).getCorrectionName())"),
            @Mapping(target = "operationCorrectionName", expression = "java(allReasonInfoMapping.get(StringUtils.defaultIfBlank(orderInfoDTO.getOperationCorrectionCode(),StringUtils.EMPTY)))"),
            @Mapping(target = "strategyName", expression = "java(allStrategyMapping.get(orderInfoDTO.getStrategyCode()))"),
    })
    CorrectionOperationBasicInfo toCorrectionOperation(CorrectionOrderInfoDTO orderInfoDTO, Map<String, String> allStrategyMapping, Map<String, String> allReasonInfoMapping, Map<String, CorrectionBasicInfoDTO> parentReasonInfoMapping);

    /**
     * To relation order detail info relation detail response . order detail info.
     *
     * @param globalOrderInfo the global order info
     * @return the relation detail response . order detail info
     */
    @Mappings({
            @Mapping(target = "merchantInfo", expression = "java(DomainAssemblerUtils.getMerchantInfo(globalOrderInfo.getOriginalMerchantInfo()))"),
            @Mapping(target = "tradeInfo", expression = "java(DomainAssemblerUtils.getTradeInfo(globalOrderInfo.getTradeInfo()))"),
            @Mapping(target = "assetInfo", expression = "java(DomainAssemblerUtils.getAssetInfo(globalOrderInfo.getAssetInfo()))"),
            @Mapping(target = "channelInfo", expression = "java(DomainAssemblerUtils.getChannelInfo(globalOrderInfo.getChannelInfo()))"),
    })
    RelationDetailResponse.OrderDetailInfo toRelationOrderDetailInfo(GlobalOrderInfo globalOrderInfo);

    /**
     * To supported payouts country supported payouts country request.
     *
     * @param correctionPayoutsCountryInfo the correction payouts country info
     * @return the supported payouts country request
     */
    @Mappings({
            @Mapping(target = "value", source = "en"),
            @Mapping(target = "paymentMethodList", source = "paymentMethodList"),
    })
    SupportedPayoutsCountryRequest toSupportedPayoutsCountry(CorrectionPayoutsCountryInfo correctionPayoutsCountryInfo);

    /**
     * To channel reconcile channel reconcile redundant dto.
     *
     * @param info the info
     * @return the channel reconcile redundant dto
     */
    @Mappings({
            @Mapping(target = "payAmount", expression = "java(ObjectsUtils.buildMoney(info.getAmount(),info.getCurrency()))"),
    })
    ChannelReconcileRedundantDTO toChannelReconcile(ChannelReconcileRedundantInfo info);

    @Mappings({
            @Mapping(target = "productCode", source = "channelReconcileRedundantDTO.productCode"),
            @Mapping(target = "refundTypeSource", expression = "java(Optional.ofNullable(RefundTypeSource.getByCode(channelReconcileRedundantDTO.getRefundTypeSource())).map(RefundTypeSource::getType).orElse(null))"),
            @Mapping(target = "retryType", source = "channelReconcileRedundantDTO.retryType"),
            @Mapping(target = "detailCode", source = "channelReconcileRedundantDTO.errorCode"),
            @Mapping(target = "detailMsg", source = "channelReconcileRedundantDTO.errorMsg"),
            @Mapping(target = "channelCompleteTime", source = "channelReconcileRedundantDTO.channelCompleteTime"),
            @Mapping(target = "currency", expression = "java(Optional.ofNullable(channelReconcileRedundantDTO.getPayAmount()).map(Money::getCurrency).map(Currency::toString).orElse(null))"),
            @Mapping(target = "amount", expression = "java(Optional.ofNullable(channelReconcileRedundantDTO.getPayAmount()).map(Money::getAmount).orElse(null))"),
    })
    ChannelReconcileRedundantInfo toChannelReconcileInfo(ReconcileRedundantDTO channelReconcileRedundantDTO);

    /**
     * To union correction union correction order info response.
     *
     * @param correctionOrderInfoDTO the order info dto
     * @param globalOrderInfo        the global order info
     * @param basicInfo              the basic info
     * @param strategyInfo           the strategy info
     * @param systemInfos            the system infos
     * @return the union correction order info response
     */
    @Mappings({
            @Mapping(target = "channelCommitNo", source = "globalOrderInfo.channelInfo.lastChannelCommit.commitInfo.orderNo"),
            @Mapping(target = "channelCommitStatus", expression = "java(Optional.ofNullable(ChannelOrderStatus.getChannelByVal(OrderStatusUtils.channelCommitStatus(globalOrderInfo))).map(ChannelOrderStatus::getName).orElse(null))"),
            @Mapping(target = "payAmount", source = "globalOrderInfo.channelInfo.lastChannelCommit.payAmount"),
            @Mapping(target = "tradeType", expression = "java(correctionOrderInfoDTO.getBaseInfo().getTradeType().getDesc())"),
            @Mapping(target = "strategyCodeName", source = "strategyInfo.dbInfo.strategyName"),
            @Mapping(target = "correctionCodeName", source = "basicInfo.correctionName"),
            @Mapping(target = "sysSourceName", source = "systemInfos"),
            @Mapping(target = "correctionNo", source = "correctionOrderInfoDTO.baseInfo.correctionNo"),
            @Mapping(target = "thirdOrderNo", source = "globalOrderInfo.channelInfo.lastChannelCommit.thirdOrderNo"),
            @Mapping(target = "statusName", expression = "java(ProcessStatusEnum.getByName(correctionOrderInfoDTO.getProcessStatus()).getMsg())"),
            @Mapping(target = "correctionInfo.voucherNo", expression = "java(correctionOrderInfoDTO.getOriVoucherInfo().getVoucherNo())"),
            @Mapping(target = "correctionInfo.voucherType", expression = "java(correctionOrderInfoDTO.getOriVoucherInfo().getVoucherType().getName())"),
            @Mapping(target = "merchantNo", source = "globalOrderInfo.originalMerchantInfo.merchantNo"),
            @Mapping(target = "merchantOrderNo", source = "globalOrderInfo.originalMerchantInfo.merOrderNo"),
            @Mapping(target = "payFinishTime", source = "globalOrderInfo", qualifiedByName = "getPayFinishTimeFromGlobalOrderInfo")
    })
    UnionCorrectionOrderInfoResponse toUnionCorrection(CorrectionOrderInfoDTO correctionOrderInfoDTO, GlobalOrderInfo globalOrderInfo,
                                                       CorrectionBasicInfoDTO basicInfo, StrategyProcessorInfo strategyInfo, List<String> systemInfos);

    @Named("getPayFinishTimeFromGlobalOrderInfo")
    default String getPayFinishTimeFromGlobalOrderInfo(GlobalOrderInfo globalOrderInfo) {
        //多层级 判空处理
        return Optional.ofNullable(globalOrderInfo)
                .map(GlobalOrderInfo::getChannelInfo)
                .map(GlobalOrderInfo.ChannelInfo::getChannelCommit)
                .filter(channelCommits -> !channelCommits.isEmpty())
                .map(channelCommits -> channelCommits.get(0))
                .map(GlobalOrderInfo.ChannelCommitInfo::getCommitInfo)
                .map(GlobalOrderInfo.OrderInfo::getPayFinishTime)
                .orElse(null);
    }

    /**
     * To union correction object.
     *
     * @param correctionOrderInfoDTO the correction order info dto
     * @param basicInfo              the basic info
     * @param reconcileRedundantDTO  the reconcile redundant dto
     * @param sysSources             the sys sources
     * @return the object
     */
    @Mappings({
            @Mapping(target = "tradeType", expression = "java(correctionOrderInfoDTO.getBaseInfo().getTradeType().getDesc())"),
            @Mapping(target = "strategyCodeName", source = "correctionOrderInfoDTO.operationManual.memo"),
            @Mapping(target = "correctionCodeName", source = "basicInfo.correctionName"),
            @Mapping(target = "sysSourceName", source = "sysSources"),
            @Mapping(target = "correctionNo", source = "correctionOrderInfoDTO.baseInfo.correctionNo"),
            @Mapping(target = "statusName", expression = "java(ProcessStatusEnum.getByName(correctionOrderInfoDTO.getProcessStatus()).getMsg())"),
            @Mapping(target = "correctionInfo.voucherNo", expression = "java(correctionOrderInfoDTO.getOriVoucherInfo().getVoucherNo())"),
            @Mapping(target = "correctionInfo.voucherType", expression = "java(correctionOrderInfoDTO.getOriVoucherInfo().getVoucherType().getName())"),
    })
    UnionCorrectionOrderInfoResponse toUnionCorrection(CorrectionOrderInfoDTO correctionOrderInfoDTO, CorrectionBasicInfoDTO basicInfo, ReconcileRedundantDTO reconcileRedundantDTO, List<String> sysSources);

    @Mappings({
            @Mapping(target = "merchantNo", source = "merchantNo"),
    })
    ReconcileRedundantDTO toRedundantInfo(ChannelReconcileRedundantInfo redundantInfo,String merchantNo);

}
