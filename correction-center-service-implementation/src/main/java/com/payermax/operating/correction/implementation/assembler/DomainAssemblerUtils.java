package com.payermax.operating.correction.implementation.assembler;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.payermax.common.lang.model.dto.Page;
import com.payermax.operating.correction.core.common.dto.ConditionInfo;
import com.payermax.operating.correction.core.common.dto.MerchantInfo;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.enums.*;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.EventTransferInfo;
import com.payermax.operating.correction.domain.dto.ExtendInfoDTO;
import com.payermax.operating.correction.facade.common.*;
import com.payermax.operating.correction.facade.request.ForwardInfo;
import com.payermax.operating.correction.facade.response.RelationDetailResponse;
import com.payermax.operating.correction.integration.enums.*;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.ValidationRuleConditionDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.order.common.utils.DateUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 转换工具类
 * @date 2022/11/1
 */
public class DomainAssemblerUtils {
    /**
     * @desc 生成操作记录
     * <AUTHOR>
     * @date 2022/11/1
     */
    public static OperationRecordInfo getRecordInfo(OperationBasicInfo basicInfo) {
        return new OperationRecordInfo(basicInfo.getUtcCreate(), basicInfo.getUtcModified(),basicInfo.getCompleteTime(), basicInfo.getOperator());
    }

    /**
     * @desc 生成操作基础信息
     * <AUTHOR>
     * @date 2022/11/1
     */
    public static OperationBasicInfo getOperationBasicInfo(OperationRecordInfo recordInfo, String valid) {
        return new OperationBasicInfo(recordInfo.getOperator(), valid);
    }

    /**
     * @desc 校验规则转化器
     * <AUTHOR>
     * @date 2022/11/16
     */
    public static ValidationRuleConditionDTO getValidateRule(ReasonBasicInfo.ExecCondition exec) {
        if (Objects.isNull(exec)) {
            return Nullable.getNullVal();
        }
        ConditionInfo channelCon = convertConInfo(exec.getChannelCode());
        ConditionInfo merchantCon = convertConInfo(exec.getMerchantNo());
        ConditionInfo productCon = convertConInfo(exec.getProductCode());
        return new ValidationRuleConditionDTO(channelCon, merchantCon, productCon);
    }

    /**
     * 执行规则校验器
     */
    public static ReasonBasicInfo.ExecCondition getExecCondition(ValidationRuleConditionDTO conditionDTO) {
        if (Objects.isNull(conditionDTO)) {
            return Nullable.getNullVal();
        }
        ReasonBasicInfo.Condition channelCon = convertCondition(conditionDTO.getChannelCodeCon());
        ReasonBasicInfo.Condition merchantCon = convertCondition(conditionDTO.getMerchantNoCon());
        ReasonBasicInfo.Condition productCon = convertCondition(conditionDTO.getProductCodeCon());
        return new ReasonBasicInfo.ExecCondition(channelCon, merchantCon, productCon);
    }

    public static BaseReasonBasicInfo getParentBasicInfo(String parentCorrectionCode, Map<String, CorrectionBasicInfoDTO> parentMap) {
        CorrectionBasicInfoDTO parent = parentMap.get(parentCorrectionCode);
        if (Objects.isNull(parent)) {
            return Nullable.getNullVal();
        }
        return new BaseReasonBasicInfo(parent.getCorrectionCode(), parent.getCorrectionName());
    }

    /**
     * 订单信息校验器
     */
    public static List<OrderInfo> getOrderInfo(GlobalOrderInfo globalOrderInfo) {
        if (Objects.isNull(globalOrderInfo)) {
            return Lists.newArrayList();
        }
        OrderInfo orderInfo = new OrderInfo(globalOrderInfo.getProductCode());
        //商户信息
        MerchantInfo originalMerchantInfo = globalOrderInfo.getOriginalMerchantInfo();
        if (Objects.nonNull(originalMerchantInfo)) {
            orderInfo.setMerchantOrderNo(originalMerchantInfo.getMerOrderNo());
        }
        //交易信息
        GlobalOrderInfo.TradeInfo tradeInfo = globalOrderInfo.getTradeInfo();
        if (Objects.nonNull(tradeInfo)) {
            orderInfo.setTradeStatus(Optional.ofNullable(tradeInfo.getTradeOrder()).map(GlobalOrderInfo.OrderInfo::getStatus).orElse(Nullable.getNullVal()));
            orderInfo.setTradeOrder(Optional.ofNullable(tradeInfo.getTradeOrder()).map(GlobalOrderInfo.OrderInfo::getOrderNo).orElse(Nullable.getNullVal()));
        }
        //支付单信息
        GlobalOrderInfo.AssetInfo assetInfo = globalOrderInfo.getAssetInfo();
        if (Objects.nonNull(assetInfo)) {
            orderInfo.setPayOrderNo(assetInfo.getAssetOrder().getOrderNo());
            orderInfo.setPayOrderState(AssetPayOrderStatus.getByVal(assetInfo.getAssetOrder().getStatus()).name());
            orderInfo.setPayAmount(assetInfo.getPayMoney().getAmount().toString());
            orderInfo.setPayCurrency(assetInfo.getPayMoney().getCurrency().getCurrencyCode());
        }
        //渠道信息
        GlobalOrderInfo.ChannelInfo channelInfo = globalOrderInfo.getChannelInfo();
        if (Optional.ofNullable(channelInfo).map(GlobalOrderInfo.ChannelInfo::getLastChannelCommit).map(GlobalOrderInfo.ChannelCommitInfo::getCommitInfo).isPresent()) {
            orderInfo.setChannelCommitNo(channelInfo.getLastChannelCommit().getCommitInfo().getOrderNo());
            orderInfo.setChannelStatus(ChannelOrderStatus.getChannelByVal(channelInfo.getLastChannelCommit().getCommitInfo().getStatus()).getName());
            orderInfo.setThirdOrderNo(channelInfo.getLastChannelCommit().getThirdOrderNo());
        }
        return Lists.newArrayList(orderInfo);
    }

    public static List<RefundOrderInfo> getRefundOrderInfo(GlobalOrderInfo globalOrderInfo) {
        if (Objects.isNull(globalOrderInfo) || Objects.isNull(globalOrderInfo.getTradeInfo()) || Objects.isNull(globalOrderInfo.getTradeInfo().getRefundInfo()) || Objects.isNull(globalOrderInfo.getTradeInfo().getRefundInfo().getRefundOrder())) {
            return Nullable.getNullVal();
        }
        GlobalOrderInfo.RefundOrderInfo refundInfo = globalOrderInfo.getTradeInfo().getRefundInfo();
        GlobalOrderInfo.OrderInfo refundOrder = refundInfo.getRefundOrder();
        RefundOrderInfo refundOrderInfo = new RefundOrderInfo();
        //退款单信息
        refundOrderInfo.setTradeNo(refundOrder.getOrderNo());
        refundOrderInfo.setTradeStatus(RefundStatusEnum.getByVal(refundOrder.getStatus()).getName());
        refundOrderInfo.setRefundType(RefundTypeSource.getByCode(refundInfo.getRefundType()).getType());

        //支付单信息
        GlobalOrderInfo.AssetInfo assetInfo = globalOrderInfo.getAssetInfo();
        if (Objects.nonNull(assetInfo)) {
            refundOrderInfo.setRefundAmount(assetInfo.getPayMoney().getAmount().toString());
            refundOrderInfo.setRefundCurrency(assetInfo.getPayMoney().getCurrency().getCurrencyCode());
        }
        //渠道信息
        GlobalOrderInfo.ChannelInfo channelInfo = globalOrderInfo.getChannelInfo();
        if (Optional.ofNullable(channelInfo).map(GlobalOrderInfo.ChannelInfo::getChannelRequest).isPresent()) {
            refundOrderInfo.setChannelRequestNo(channelInfo.getChannelRequest().getOrderNo());
            refundOrderInfo.setChannelRequestStatus(ChannelOrderStatus.getChannelByVal(channelInfo.getChannelRequest().getStatus()).getName());
            refundOrderInfo.setChannelRespCode(channelInfo.getRespCode());
            refundOrderInfo.setChannelRespMsg(channelInfo.getRespMsg());
        }
        if (Optional.ofNullable(channelInfo).map(GlobalOrderInfo.ChannelInfo::getLastChannelCommit).map(GlobalOrderInfo.ChannelCommitInfo::getCommitInfo).isPresent()) {
            refundOrderInfo.setChannelCommitNo(channelInfo.getLastChannelCommit().getCommitInfo().getOrderNo());
            refundOrderInfo.setChannelStatus(ChannelOrderStatus.getChannelByVal(channelInfo.getLastChannelCommit().getCommitInfo().getStatus()).getName());
        }
        return Lists.newArrayList(refundOrderInfo);
    }

    public static List<UploadInfo> getUploadList(List<ExtendInfoDTO> extendInfoList) {
        if (CollectionUtils.isEmpty(extendInfoList)) {
            return Nullable.getNullVal();
        }
        return extendInfoList.stream().filter(e -> e.getOperation() == ExtendEnum.UPLOAD).map(e -> JSONObject.parseObject(e.getJson(), UploadInfo.class)).collect(Collectors.toList());
    }

    public static VoucherInfo getVoucherInfo(com.payermax.operating.correction.core.common.dto.VoucherInfo voucherInfo) {
        if (Objects.isNull(voucherInfo)) {
            return Nullable.getNullVal();
        }
        return new VoucherInfo(Optional.ofNullable(voucherInfo.getVoucherType()).map(DCVoucherType::name).orElse(Nullable.getNullVal()), voucherInfo.getVoucherNo());
    }

    public static String dateTimeToStr(Long time) {
        if (Objects.isNull(time)) {
            return Nullable.getNullVal();
        }
        return DateUtils.dateFormatForTime(new Date(time));
    }

    public static RelationDetailResponse.MerchantInfo getMerchantInfo(MerchantInfo merchantInfo) {
        if (Objects.isNull(merchantInfo)) {
            return Nullable.getNullVal();
        }
        return new RelationDetailResponse.MerchantInfo(merchantInfo.getMerchantNo(), merchantInfo.getMerOrderNo());
    }

    public static RelationDetailResponse.TradeInfo getTradeInfo(GlobalOrderInfo.TradeInfo tradeInfo) {
        if (Objects.isNull(tradeInfo)) {
            return Nullable.getNullVal();
        }
        RelationDetailResponse.OrderInfo tradeOrder = new RelationDetailResponse.OrderInfo(Optional.ofNullable(tradeInfo.getTradeOrder()).map(GlobalOrderInfo.OrderInfo::getOrderNo).orElse(Nullable.getNullVal()),
                Optional.ofNullable(tradeInfo.getTradeOrder()).map(e -> TradeOrderStatus.getByCode(e.getStatus()).getDesc()).orElse(Nullable.getNullVal()));
        RelationDetailResponse.OrderInfo refundOrder = null;
        if (Objects.nonNull(tradeInfo.getRefundInfo()) && Objects.nonNull(tradeInfo.getRefundInfo().getRefundOrder())) {
            refundOrder = new RelationDetailResponse.OrderInfo(tradeInfo.getRefundInfo().getRefundOrder().getOrderNo(), PayOrderStatusEnum.getByVal(tradeInfo.getRefundInfo().getRefundOrder().getStatus()).getName());
        }
        return new RelationDetailResponse.TradeInfo(tradeOrder, refundOrder);
    }

    public static RelationDetailResponse.AssetInfo getAssetInfo(GlobalOrderInfo.AssetInfo assetInfo) {
        if (Objects.isNull(assetInfo)) {
            return Nullable.getNullVal();
        }
        RelationDetailResponse.OrderInfo assetOrder = new RelationDetailResponse.OrderInfo(Optional.ofNullable(assetInfo.getAssetOrder()).map(GlobalOrderInfo.OrderInfo::getOrderNo).orElse(Nullable.getNullVal()),
                Optional.ofNullable(assetInfo.getAssetOrder()).map(e -> AssetPayOrderStatus.getByVal(e.getStatus()).getName()).orElse(Nullable.getNullVal()));

        return new RelationDetailResponse.AssetInfo(assetOrder, assetInfo.getPayMoney(), assetInfo.getTradeType().getDesc());
    }

    public static RelationDetailResponse.ChannelInfo getChannelInfo(GlobalOrderInfo.ChannelInfo channelInfo) {
        if (Objects.isNull(channelInfo)) {
            return Nullable.getNullVal();
        }
        //渠道请求单
        RelationDetailResponse.OrderInfo channelRequestNo = new RelationDetailResponse.OrderInfo(Optional.ofNullable(channelInfo.getChannelRequest()).map(GlobalOrderInfo.OrderInfo::getOrderNo).orElse(Nullable.getNullVal()),
                Optional.ofNullable(channelInfo.getChannelRequest()).map(e -> ChannelOrderStatus.getChannelByVal(e.getStatus()).getName()).orElse(Nullable.getNullVal()));

        //最后一条渠道提交单
        RelationDetailResponse.ChannelCommitInfo lastChannelCommit = Nullable.getNullVal();
        if (Objects.nonNull(channelInfo.getLastChannelCommit()) && Objects.nonNull(channelInfo.getLastChannelCommit().getCommitInfo())) {
            lastChannelCommit = new RelationDetailResponse.ChannelCommitInfo();
            lastChannelCommit.setChannelCode(channelInfo.getLastChannelCommit().getChannelCode());
            lastChannelCommit.setCommitInfo(new RelationDetailResponse.OrderInfo(channelInfo.getLastChannelCommit().getCommitInfo().getOrderNo(),
                    ChannelOrderStatus.getChannelByVal(channelInfo.getLastChannelCommit().getCommitInfo().getStatus()).getName()));
        }

        //渠道提交单list
        List<RelationDetailResponse.ChannelCommitInfo> list = Lists.newArrayList();
        Optional.ofNullable(channelInfo.getChannelCommit()).ifPresent(channelCommitInfos -> {
            for (GlobalOrderInfo.ChannelCommitInfo channelCommitInfo : channelCommitInfos) {
                GlobalOrderInfo.OrderInfo commitInfo = channelCommitInfo.getCommitInfo();
                RelationDetailResponse.OrderInfo orderInfo = Nullable.getNullVal();
                if (Objects.nonNull(commitInfo)) {
                    orderInfo = new RelationDetailResponse.OrderInfo(commitInfo.getOrderNo(), ChannelOrderStatus.getChannelByVal(commitInfo.getStatus()).getName());
                }
                list.add(new RelationDetailResponse.ChannelCommitInfo(orderInfo, channelCommitInfo.getChannelCode()));
            }
        });
        return new RelationDetailResponse.ChannelInfo(list, lastChannelCommit, channelRequestNo);
    }


    public static Page buildPage(int current, int total, int size, List list) {
        Page page = new Page();
        page.setPageIndex(current);
        page.setTotal(total);
        page.setLimit(size);
        page.setResults(list);
        return page;
    }

    public static ReasonBasicInfo.Condition convertCondition(ConditionInfo condition) {
        if (Objects.isNull(condition)) {
            return Nullable.getNullVal();
        }
        String conditionVal = Optional.ofNullable(condition.getCondition()).map(ConditionEnum::name).orElse(Nullable.getNullVal());
        return new ReasonBasicInfo.Condition(conditionVal, condition.getValue());
    }

    public static ConditionInfo convertConInfo(ReasonBasicInfo.Condition condition) {
        if (Objects.isNull(condition)) {
            return Nullable.getNullVal();
        }
        return new ConditionInfo(condition.getConditionVal(), condition.getValue());
    }

    public static EventTransferInfo transferInfo(ForwardInfo forwardInfo){
        if (Objects.isNull(forwardInfo)){
            return Nullable.getNullVal();
        }
        return new EventTransferInfo(forwardInfo.getForwardType(),forwardInfo.getReviewer(),forwardInfo.getCorrectionNo(),forwardInfo.getOperator());
    }

}
