package com.payermax.operating.correction.implementation.facade;

import com.payermax.common.lang.model.dto.Page;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.operating.correction.core.common.annotation.FacadeMethod;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.enums.ValidType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.payermax.operating.correction.domainservice.service.DomainOperationService;
import com.payermax.operating.correction.facade.common.QueryPageRequest;
import com.payermax.operating.correction.facade.provider.operation.CorrectionOperationStrategyFacade;
import com.payermax.operating.correction.facade.request.StrategyInfo;
import com.payermax.operating.correction.implementation.assembler.CorrectionInfoDomainAssembler;
import com.payermax.operating.correction.implementation.assembler.DomainAssemblerUtils;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOperationStrategyInfoDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 差错运营平台facade实现类
 * @date 2022/10/18
 */
@SuppressWarnings("unused")
@DubboService(version = "1.0")
@Slf4j
public class CorrectionOperationStrategyFacadeImpl implements CorrectionOperationStrategyFacade {

    @Resource
    private DomainBizService domainBizService;

    @Resource
    private DomainOperationService domainOperationService;

    @Resource
    private CorrectionInfoDomainAssembler infoDomainAssembler;

    @Resource
    private IDomainRepository domainRepository;

    @Override
    @FacadeMethod
    public Result storeStrategyInfo(StrategyInfo strategyInfo) {
        domainOperationService.storeStrategyInfo(infoDomainAssembler.toOperationStrategyInfo(strategyInfo));
        return ResultUtil.success();
    }

    @Override
    @FacadeMethod
    public Result<StrategyInfo> queryStrategyInfo(String strategyCode) {
        return ResultUtil.success(infoDomainAssembler.toStrategyInfo(domainOperationService.getStrategyInfo(strategyCode)));
    }

    @Override
    @FacadeMethod
    public Result<Page<StrategyInfo>> queryStrategyInfoByPage(QueryPageRequest queryPage) {
        //解析Page对象
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<CorrectionOperationStrategyInfoDTO> page = domainOperationService.getStrategyInfoByPage(queryPage.getPage(), ValidType.getValidByName(queryPage.getValid()));
        List<StrategyInfo> list = page.getRecords().stream().map(infoDomainAssembler::toStrategyInfo).collect(Collectors.toList());
        //构建Page信息
        return ResultUtil.success(DomainAssemblerUtils.buildPage((int) page.getCurrent(), (int) page.getTotal(), (int) page.getSize(), list));
    }

    @Override
    @FacadeMethod
    public Result<List<StrategyInfo>> filterStrategyCode(String operationCorrectionCode,String tradeType) {
        List<String> configStrategyCode = domainOperationService.getConfigStrategyCode(operationCorrectionCode, TradeType.getTradeTypeByName(tradeType));
        Map<String, CorrectionOperationStrategyInfoDTO> allStrategyMapping = domainOperationService.getAllMappingStrategyCode();
        return ResultUtil.success(configStrategyCode.stream().map(e -> Objects.isNull(allStrategyMapping.get(e)) ? Nullable.getNullVal() : infoDomainAssembler.toStrategyInfo(allStrategyMapping.get(e))).filter(Objects::nonNull).collect(Collectors.toList()));
    }


}
