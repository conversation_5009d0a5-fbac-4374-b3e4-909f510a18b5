package com.payermax.operating.correction.implementation.facade;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.payermax.common.lang.model.dto.Page;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.util.ResultUtil;
import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.annotation.FacadeMethod;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.dto.CorrectionBaseInfo;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.*;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.core.common.utils.ObjectsUtils;
import com.payermax.operating.correction.domain.dto.ExtendInfoDTO;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domain.enums.CorrectionEvent;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domainservice.matcher.CorrectionHandleMatch;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.payermax.operating.correction.domainservice.service.DomainOperationService;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.facade.common.*;
import com.payermax.operating.correction.facade.provider.operation.CorrectionOperationOrderHandlerFacade;
import com.payermax.operating.correction.facade.request.*;
import com.payermax.operating.correction.facade.response.CorrectionOrderDetailInfoResponse;
import com.payermax.operating.correction.facade.response.CorrectionOrderHandlerBasicInfo;
import com.payermax.operating.correction.implementation.assembler.CorrectionInfoDomainAssembler;
import com.payermax.operating.correction.implementation.assembler.DomainAssemblerUtils;
import com.payermax.operating.correction.integration.config.nacos.model.CorrectionSystemInfo;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyProcessorInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.QueryCorrectionOrderPageDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.merchant.MerchantReository;
import com.payermax.operating.correction.integration.rpc.merchant.dto.BatchQueryMerchantBaseInfoReps;
import com.payermax.operating.correction.integration.utils.ListSizeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 差错运营平台facade实现类
 * @date 2022/10/18
 */
@SuppressWarnings("unused")
@DubboService(version = "1.0")
@Slf4j
public class CorrectionOperationOrderHandlerFacadeImpl implements CorrectionOperationOrderHandlerFacade {
    @Resource
    private DomainBizService domainBizService;

    @Resource
    private DomainOperationService domainOperationService;

    @Resource
    private CorrectionInfoDomainAssembler infoDomainAssembler;

    @Resource
    private IDomainRepository domainRepository;

    @Resource
    private CorrectionHandleMatch handleMatch;

    @Resource(name = "bizAsyncExecutor")
    private ThreadPoolTaskExecutor asyncExecutor;

    @Override
    @FacadeMethod
    public Result queryCorrectionHandlerList(CorrectionOrderHandlerPageRequest handlerPageRequest) {
        if (handlerPageRequest.getRedundantInfo() != null && !CollectionUtils.isEmpty(handlerPageRequest.getRedundantInfo().getOwnerList())){
            handlerPageRequest.getRedundantInfo().setOwner(StringUtils.strip(handlerPageRequest.getRedundantInfo().getOwnerList().toString(),"[]"));
        }
        if (handlerPageRequest.getRedundantInfo() != null && !CollectionUtils.isEmpty(handlerPageRequest.getRedundantInfo().getOrgNameList())){
            handlerPageRequest.getRedundantInfo().setOrgName(StringUtils.strip(handlerPageRequest.getRedundantInfo().getOrgNameList().toString(),"[]"));
        }
        QueryCorrectionOrderPageDTO queryPage = QueryCorrectionOrderPageDTO.builder()
                .page(handlerPageRequest.getPage())
                .baseInfo(new CorrectionBaseInfo(handlerPageRequest.getCorrectionNo(),
                        handlerPageRequest.getEventInfo().getCorrectionCode(),
                        new VoucherInfo(handlerPageRequest.getEventInfo().getVoucherNo(), DCVoucherType.getVoucherTypeByName(handlerPageRequest.getEventInfo().getVoucherType()))))
                .operationCorrectionCode(handlerPageRequest.getOperationCorrectionCode())
                .status(handlerPageRequest.getStatus())
                .channelCode(handlerPageRequest.getChannelCode())
                .merchantOrderNo(handlerPageRequest.getMerOrderNo())
                .sysSource(ObjectsUtils.strToList(handlerPageRequest.getSysSource(), Symbols.HASHTAG))
                .tradeType(TradeType.getTradeTypeByName(handlerPageRequest.getTradeType()))
                .redundantInfo(infoDomainAssembler.toRedundantInfo(handlerPageRequest.getRedundantInfo(), handlerPageRequest.getMerchantNo()))
                .operator(handlerPageRequest.getOperator())
                .build();
        return this.wrapOrderHandlerList(queryPage);
    }

    @Override
    @FacadeMethod
    public Result<CorrectionOrderDetailInfoResponse> queryCorrectionOrderDetailInfo(String correctionNo) {
        DomainCorrectionInfo correctionOrderInfo = domainRepository.getValidCorrectionOrderInfo(correctionNo, OrderDetailType.All_QUERY, GlobalOrderReadEvent.DISPLAY);
        //build
        CorrectionOrderDetailInfoResponse orderDetailInfo = infoDomainAssembler.toOrderDetailInfo(correctionOrderInfo);
        //交易类型
        orderDetailInfo.getCorrectionInfo().setTradeType(Optional.ofNullable(correctionOrderInfo.getTradeType()).map(TradeType::name).orElse(StringUtils.EMPTY));
        //来源系统集合
        List<CorrectionSystemInfo> systemInfos = domainRepository.getSystemInfos(correctionOrderInfo.getBasicInfoDTO().getCorrectionCode(), correctionOrderInfo.getSysSource());
        orderDetailInfo.setSysSources(systemInfos.stream().map(CorrectionSystemInfo::getName).collect(Collectors.toList()));

        orderDetailInfo.getCorrectionInfo().setCorrectionAmount(Optional.ofNullable(correctionOrderInfo.getPayTotalMoney()).map(Money::getAmount).map(BigDecimal::toString).orElse(Nullable.getNullVal()));
        orderDetailInfo.getCorrectionInfo().setCorrectionCurrency(Optional.ofNullable(correctionOrderInfo.getPayTotalMoney()).map(Money::getCurrency).map(Currency::getCurrencyCode).orElse(null));

        orderDetailInfo.getCorrectionInfo().setStrategyCode(Optional.ofNullable(correctionOrderInfo.getStrategyProcessor()).map(StrategyProcessorInfo::getStrategyCode).orElse(Nullable.getNullVal()));
        this.wrapBasicInfo(orderDetailInfo, correctionOrderInfo.getTradeType());
        //先判断数据库中是否存在，如果存在取数据库中的记录
        String operationCode = Objects.isNull(correctionOrderInfo.getOperationBasic()) || StringUtils.isBlank(correctionOrderInfo.getOperationBasic().getCorrectionCode()) ?
                getOperationCode(correctionOrderInfo) :
                correctionOrderInfo.getOperationBasic().getCorrectionCode();//CHECKED
        orderDetailInfo.getCorrectionInfo().setOperationCorrectionCode(operationCode);
        List<CorrectionOrderHandlerRecordDTO> correctionOrderHandlerRecord = domainOperationService.getCorrectionOrderHandlerRecord(correctionNo, OrderDetailType.All_QUERY);
        orderDetailInfo.setHandlerRecordList(this.getHandlerRecordInfoList(correctionOrderHandlerRecord));
        asyncExecutor.execute(() -> domainBizService.execStrategy(correctionNo, CorrectionConstant.MINUS_ONE));
        return ResultUtil.success(orderDetailInfo);
    }

    /**
     * 获取策略code
     * @param correctionOrderInfo
     * @return
     */
    public String getOperationCode(DomainCorrectionInfo correctionOrderInfo){
        List<String> matchRule = handleMatch.matchRule(correctionOrderInfo.getBasicInfoDTO().getCorrectionCode(), correctionOrderInfo.getGlobalOrderInfo());
        if(Objects.nonNull(matchRule) && ListSizeUtils.listSizeEqualOne(matchRule,"matchChildCorrectionCode")){
            return matchRule.get(0);//CHECKED
        }
        return StringUtils.EMPTY;
    }

    @Override
    public Result<CorrectionOrderDetailInfoResponse> queryCorrectionOrderDetailInfo(CorrectionEventInfo voucherInfo) {
        GlobalOrderInfo globalOrderInfo = domainOperationService.getGlobalOrderInfo(voucherInfo.getVoucherNo(), DCVoucherType.getVoucherTypeByName(voucherInfo.getVoucherType()));
        List<OrderInfo> orderInfoList = DomainAssemblerUtils.getOrderInfo(globalOrderInfo);
        List<RefundOrderInfo> refundOrderInfoList = DomainAssemblerUtils.getRefundOrderInfo(globalOrderInfo);
        return ResultUtil.success(new CorrectionOrderDetailInfoResponse(orderInfoList, refundOrderInfoList));
    }

    @Override
    @FacadeMethod
    public Result addUploadInfo(AddUploadInfo addUploadInfo) {
        ExtendInfoDTO proofInfo = new ExtendInfoDTO(ExtendEnum.UPLOAD, JSONObject.toJSONString(addUploadInfo.getUploadInfo()));
        domainOperationService.storeProofInfo(addUploadInfo.getCorrectionNo(), proofInfo);
        return ResultUtil.success();
    }

    @Override
    @FacadeMethod
    public Result approval(ApprovalEvent approvalEvent) {
        OperationDomainCorrectionInfo operationDomainCorrectionInfo = infoDomainAssembler.toOperationDomainCorrectionInfo(approvalEvent);
        operationDomainCorrectionInfo.setCorrectionEvent(StringUtils.isBlank(approvalEvent.getApprovalAction()) ? Nullable.getNullVal() : CorrectionEvent.valueOf(approvalEvent.getApprovalAction()));
        domainBizService.approval(operationDomainCorrectionInfo);
        return ResultUtil.success();
    }

    @Override
    @FacadeMethod
    public Result addCorrectionEvent(OrderCorrectionManualInfoRequest manualInfo) {
        OperationDomainCorrectionInfo correctionInfo = infoDomainAssembler.toCorrectionInfo(manualInfo);
        correctionInfo.getBaseInfo().setVoucherInfo(new VoucherInfo(manualInfo.getCorrectionInfo().getVoucherNo(), DCVoucherType.getVoucherTypeByName(manualInfo.getCorrectionInfo().getVoucherType())));
        correctionInfo.getBaseInfo().setCorrectionNo(manualInfo.getCorrectionNo());
        correctionInfo.fillManualCorrectionInfoDTO(manualInfo.getOperator());
        domainBizService.addEvent(correctionInfo);
        return ResultUtil.success();
    }

    @Override
    @FacadeMethod
    public Result chooseStrategyInfo(CorrectionStrategyCodeHandlerInfo strategyCodeHandlerInfo) {
        log.info("CorrectionOperationOrderHandlerFacadeImpl chooseStrategyInfo [correctionNo] [{}]:",strategyCodeHandlerInfo.getCorrectionNo());
        OperationDomainCorrectionInfo operationDomainCorrectionInfo = infoDomainAssembler.toOperationDomainCorrectionInfo(strategyCodeHandlerInfo);
        domainBizService.chooseStrategyCode(operationDomainCorrectionInfo);
        return ResultUtil.success();
    }

    @Override
    @FacadeMethod
    @Deprecated
    public Result handlerRegistration(String memo, String correctionNo, String operator) {
        CorrectionOrderInfoDTO orderInfoDTO = domainRepository.getValidCorrectionOrderInfoDto(correctionNo);
        domainBizService.registrationCompleteEvent(orderInfoDTO.getOriVoucherInfo(), orderInfoDTO.getBaseInfo().getCorrectionCode(), memo, operator);
        return ResultUtil.success();
    }

    @Override
    @FacadeMethod
    public Result eventTransfer(ForwardInfo forwardInfo) {
        domainBizService.transfer(DomainAssemblerUtils.transferInfo(forwardInfo));
        return ResultUtil.success();
    }

    @Override
    @FacadeMethod
    public Result<Page<CorrectionOrderHandlerBasicInfo>> queryPersonalCorrectionPool(CorrectionPrimaryQueryRequest primaryQuery) {
        CorrectionBaseInfo baseInfo = new CorrectionBaseInfo(
                StringUtils.defaultIfBlank(primaryQuery.getCorrectionNos(), StringUtils.EMPTY),
                StringUtils.EMPTY,
                new VoucherInfo(StringUtils.defaultIfBlank(primaryQuery.getVoucherNos(), StringUtils.EMPTY), Nullable.getNullVal()));

        QueryCorrectionOrderPageDTO queryPage = QueryCorrectionOrderPageDTO.builder()
                .page(primaryQuery.getPage())
                .reviewer(primaryQuery.getOperator())
                .status(Lists.newArrayList(ProcessStatusEnum.REVIEWED.name(), ProcessStatusEnum.VERIFICATION.name(),ProcessStatusEnum.REVIEWING.name()))
                .baseInfo(baseInfo)
                .build();
        return this.wrapOrderHandlerList(queryPage);
    }

    @Override
    public Result<List<String>> queryAllRegisterHandlerReason() {
        return ResultUtil.success(domainRepository.getAllRegisterHandlerReason());
    }

    @Autowired
    private MerchantReository merchantReository;
    /**
     * 在差错列表&个人列表会使用到该方法
     * @param queryPage
     * @return
     */
    public Result<Page<CorrectionOrderHandlerBasicInfo>> wrapOrderHandlerList(QueryCorrectionOrderPageDTO queryPage) {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<CorrectionOrderInfoDTO> page = domainOperationService.getCorrectionOrderByPage(queryPage);
        Map<String, String> allMappingReasonInfo = domainOperationService.getAllChildrenMappingReasonInfo();
        Map<String, String> allStrategyMapping = domainOperationService.getAllMappingStrategyCodeInfo();

        List<String> collect = page.getRecords().stream().map(x -> x.getOriginalTradeRedundantInfo().getMerchantNo()).collect(Collectors.toList());
        BatchQueryMerchantBaseInfoReps batchQueryMerchantBaseInfoReps = merchantReository.batchQueryMerchantBaseInfo(collect);

        //build 返回所需list
        List<CorrectionOrderHandlerBasicInfo> list = page.getRecords().stream().map(e -> {
            CorrectionOrderHandlerBasicInfo orderHandlerBasicInfo = infoDomainAssembler.toOrderHandlerBasicInfo(e);
            orderHandlerBasicInfo.getCorrectionInfo().setCorrectionAmount(Optional.ofNullable(e.getPayTotalMoney()).map(Money::getAmount).map(BigDecimal::toString).orElse(Nullable.getNullVal()));
            orderHandlerBasicInfo.getCorrectionInfo().setCorrectionCurrency(Optional.ofNullable(e.getPayTotalMoney()).map(Money::getCurrency).map(Currency::getCurrencyCode).orElse(Nullable.getNullVal()));

            //来源系统集合
            List<CorrectionSystemInfo> systemInfos = domainRepository.getSystemInfos(e.getBaseInfo().getCorrectionCode(), e.getSysSource());
            orderHandlerBasicInfo.setSystemSources(systemInfos.stream().map(CorrectionSystemInfo::getName).collect(Collectors.toList()));
            orderHandlerBasicInfo.setProcessStatusName(ProcessStatusEnum.getByName(e.getProcessStatus()).getMsg());
            orderHandlerBasicInfo.setTradeTypeName(e.getBaseInfo().getTradeType().getDesc());
            CorrectionBasicInfoDTO basicInfo = domainRepository.getValidParentReasonBasicInfo(e.getBaseInfo().getCorrectionCode());
            orderHandlerBasicInfo.getCorrectionInfo().setCorrectionName(basicInfo.getCorrectionName());

            orderHandlerBasicInfo.getCorrectionInfo().setVoucherNo(e.getBaseInfo().getVoucherInfo().getVoucherNo());
            orderHandlerBasicInfo.getCorrectionInfo().setOperationCorrectionCode(e.getOperationCorrectionCode());
            orderHandlerBasicInfo.getCorrectionInfo().setStrategyName(allStrategyMapping.get(e.getStrategyCode()));
            orderHandlerBasicInfo.getCorrectionInfo().setOperationCorrectionName(allMappingReasonInfo.get(e.getOperationCorrectionCode()));

            orderHandlerBasicInfo.setMerchantName(batchQueryMerchantBaseInfoReps.getInnerName(orderHandlerBasicInfo.getMerchantNo()));
            return orderHandlerBasicInfo;
        }).collect(Collectors.toList());
        return ResultUtil.success(DomainAssemblerUtils.buildPage((int) page.getCurrent(), (int) page.getTotal(), (int) page.getSize(), list));
    }

    private List<HandlerRecordInfo> getHandlerRecordInfoList(List<CorrectionOrderHandlerRecordDTO> orderHandlerRecordDtoList) {
        Map<String, String> allStrategyMapping = domainOperationService.getAllMappingStrategyCodeInfo();
        return orderHandlerRecordDtoList.stream().map(e -> infoDomainAssembler.toHandlerRecordInfo(e, allStrategyMapping)).collect(Collectors.toList());
    }

    private void wrapBasicInfo(CorrectionOrderDetailInfoResponse orderDetailInfo, TradeType tradeType) {
        List<CorrectionBasicInfoDTO> basicInfoList = domainOperationService.getBasicInfoByParentCode(orderDetailInfo.getCorrectionInfo().getCorrectionCode(), tradeType);
        if (Objects.isNull(basicInfoList)) {
            return;
        }
        List<BaseReasonBasicInfo> list = basicInfoList.stream().map(e -> new BaseReasonBasicInfo(e.getCorrectionCode(), e.getCorrectionName())).collect(Collectors.toList());
        orderDetailInfo.setBasicInfoList(list);
    }
}
