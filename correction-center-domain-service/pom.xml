<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fin-correction-center</artifactId>
        <groupId>com.payermax.operating</groupId>
        <version>1.0.0-RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>correction-center-domain-service</artifactId>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.payermax.operating</groupId>
            <artifactId>common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.payermax.operating</groupId>
            <artifactId>correction-center-integration</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>ionia-log-digest-http</artifactId>
                    <groupId>com.payermax.infra</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.payermax.operating</groupId>
            <artifactId>correction-center-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>cola-statemachine</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
