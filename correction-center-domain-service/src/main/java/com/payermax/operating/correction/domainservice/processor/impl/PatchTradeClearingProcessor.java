package com.payermax.operating.correction.domainservice.processor.impl;


import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.processor.utils.OrderStatusUtils;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.enums.AssetPayOrderStatus;
import com.payermax.operating.correction.integration.enums.ChannelOrderStatus;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.settle.repository.SettleRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc 补交易清分处理器
 * @date 2023/4/17
 */
@Component(value = "patchTradeClearingProcessor")
@Slf4j
public class PatchTradeClearingProcessor implements Processor {

    @Resource
    private SettleRepository settleRepository;

    @Override
    public void preCheck(DomainCorrectionInfo correctionInfo) {
        //校验原退款单状态
        ChannelOrderStatus channelOrderStatus = ChannelOrderStatus.getChannelByVal(OrderStatusUtils.channelCommitStatus(correctionInfo.getGlobalOrderInfo()));
        AssetPayOrderStatus assetPayOrderStatus = AssetPayOrderStatus.getByVal(OrderStatusUtils.assetExchangePayOrderStatus(correctionInfo.getGlobalOrderInfo()));
        AssertUtil.isTrue(channelOrderStatus == ChannelOrderStatus.FAILED && AssetPayOrderStatus.FAILED == assetPayOrderStatus, ReturnCode.PROCESSOR_VALID_EXCEPTION.getCode(), ReturnMsg.ASSET_STATUS_MATCH_FAIL);
    }

    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {
        String productCode = Optional.ofNullable(correctionInfo)
                .map(DomainCorrectionInfo::getGlobalOrderInfo)
                .map(GlobalOrderInfo::getTradeInfo)
                .map(GlobalOrderInfo.TradeInfo::getTradeOrder)
                .map(GlobalOrderInfo.OrderInfo::getProductCode)
                .orElse(Nullable.getNullVal());
        String refundOrderNo =  Optional.ofNullable(correctionInfo)
                .map(DomainCorrectionInfo::getGlobalOrderInfo)
                .map(GlobalOrderInfo::getTradeInfo)
                .map(GlobalOrderInfo.TradeInfo::getRefundInfo)
                .map(GlobalOrderInfo.RefundOrderInfo::getRefundOrder)
                .map(GlobalOrderInfo.OrderInfo::getOrderNo)
                .orElse(Nullable.getNullVal());
        //网关模式 不去调用二次清分接口 直接返回成功
        if(StringUtils.isNotBlank(productCode) && CorrectionConstant.GATEWAY_PRODUCT_CODE.equals(productCode)){
            return BaseResProcess.builder()
                    .status(CommonStatusEnum.SUCCESS)
                    .resVoucherInfo(VoucherInfo.builder().voucherType(DCVoucherType.REFUND_TRADE_NO).voucherNo(refundOrderNo).build())
                    .build();
        }
        BaseResProcess baseResProcess = settleRepository.patchTradeClearing(correctionInfo.getCorrectionNo(), refundOrderNo);
        if (CommonStatusEnum.SUCCESS != baseResProcess.getStatus()) {
            return Nullable.getNullVal();
        }
        return baseResProcess;
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo globalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        return ObjectUtils.defaultIfNull(correctionHandlerRecordInfo.getStatus(), CommonStatusEnum.PENDING);
    }

}
