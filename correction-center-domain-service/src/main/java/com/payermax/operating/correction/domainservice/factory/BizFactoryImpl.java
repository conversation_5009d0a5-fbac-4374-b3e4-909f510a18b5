package com.payermax.operating.correction.domainservice.factory;

import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.*;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.ChannelReconcileRedundantDTO;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domainservice.assembler.CorrectionDomainServiceAssembler;
import com.payermax.operating.correction.domainservice.repository.impl.IDomainRepositoryImpl;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.ReconcileRedundantDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 业务对象准备工厂类
 * @date 2022/9/28
 */
@Slf4j
@Component
public class BizFactoryImpl implements BizFactory {

    @Resource
    private IDomainRepositoryImpl domainRepository;

    @Resource
    private CorrectionDomainServiceAssembler domainAssembler;

    @Override
    public final DomainCorrectionInfo addCorrectionEventPrepareFactory(OperationDomainCorrectionInfo correctionInfo) {
        DomainCorrectionInfo domainCorrectionInfo = domainAssembler.toDomainCorrection(correctionInfo);
        //填充差错基础信息
        CorrectionBasicInfoDTO basicInfo = domainRepository.getValidBasicInfo(correctionInfo.getBaseInfo().getCorrectionCode());
        domainCorrectionInfo.setBasicInfoDTO(basicInfo);

        //填充差错系统信息
        domainCorrectionInfo.setSystemInfo(domainRepository.getSystemInfos(correctionInfo.getBaseInfo().getCorrectionCode(), correctionInfo.getSysSource()));

        //渲染订单信息
        this.renderOrderInfo(domainCorrectionInfo, correctionInfo);
        //默认使用原始凭证
        domainCorrectionInfo.setVoucherNo(domainCorrectionInfo.getOriVoucherInfo().getVoucherNo());
        domainCorrectionInfo.setVoucherType(domainCorrectionInfo.getOriVoucherInfo().getVoucherType());
        return domainCorrectionInfo;
    }

    @Override
    public DomainCorrectionInfo prepareCorrectionInfoFactory(String correctionNo, GlobalOrderReadEvent readEvent) {
        return domainRepository.getValidCorrectionOrderInfo(correctionNo, OrderDetailType.STRATEGY_QUERY, readEvent);
    }

    private String getTradeChannelCode(GlobalOrderInfo orderInfo) {
        if (TradeType.REFUND == orderInfo.getAssetInfo().getTradeType()) {
            GlobalOrderInfo originalOrderInfo = domainRepository.getGlobalOrderInfo(new VoucherInfo(orderInfo.getOriginalMerchantInfo().getMerOrderNo(), DCVoucherType.R_MERCHANT_ORDER_NO), GlobalOrderReadEvent.OPERATION);
            originalOrderInfo.channelCodeValidation();
            return originalOrderInfo.getChannelInfo().getLastChannelCommit().getChannelCode();
        }
        orderInfo.channelCodeValidation();
        return orderInfo.getChannelInfo().getLastChannelCommit().getChannelCode();
    }


    private void renderOrderInfo(DomainCorrectionInfo domainCorrectionInfo, OperationDomainCorrectionInfo correctionInfo) {
        //根据来源系统初步获取渠道交易类型
        TradeType tradeType = CorrectionConstant.CHANNEL_RECONCILE.equals(correctionInfo.getSysSource()) ? getChannelTradeType(correctionInfo.getChannelReconcileRedundant(), correctionInfo.getBaseInfo().getCorrectionCode()) : Nullable.getNullVal();
        //若是交易对账和业务系统推送无交集，则直接不做渲染
        if (!domainRepository.handlerIntersectionMark(correctionInfo.getBaseInfo().getCorrectionCode(),tradeType)) {
            AssertUtil.notNull(correctionInfo.getChannelReconcileRedundant(), ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.RECONCILE_INVALID);
            domainCorrectionInfo.setChannelCode(correctionInfo.getChannelReconcileRedundant().getChannelCode());
            domainCorrectionInfo.setTradeType(tradeType);
            //填充唯一差错no
            String correctionNo = domainRepository.getValidCorrectionNo(domainCorrectionInfo.getOriVoucherInfo().getVoucherNo(), domainCorrectionInfo.getTradeType(), domainCorrectionInfo.getBasicInfoDTO().getCorrectionCode());
            domainCorrectionInfo.setCorrectionNo(correctionNo);
            domainCorrectionInfo.setRedundantInfo(domainAssembler.toRedundantInfo(correctionInfo.getChannelReconcileRedundant()));
            return;
        }
        //填充商户信息
        GlobalOrderInfo orderInfo = domainRepository.getGlobalOrderInfo(domainCorrectionInfo.getOriVoucherInfo(), GlobalOrderReadEvent.OPERATION);

        //填充唯一差错no
        String correctionNo = domainRepository.getValidCorrectionNo(domainCorrectionInfo.getOriVoucherInfo().getVoucherNo(), orderInfo.getAssetInfo().getTradeType(), domainCorrectionInfo.getBasicInfoDTO().getCorrectionCode());
        domainCorrectionInfo.setCorrectionNo(correctionNo);

        //填充支付金额
        if (Objects.isNull(domainCorrectionInfo.getPayTotalMoney()) && Objects.nonNull(orderInfo.getChannelInfo()) && Objects.nonNull(orderInfo.getChannelInfo().getPayAmount())) {
            domainCorrectionInfo.setPayTotalMoney(orderInfo.getChannelInfo().getPayAmount());
        }

        domainCorrectionInfo.setGlobalOrderInfo(orderInfo);
        domainCorrectionInfo.setMerchantInfo(orderInfo.getOriginalMerchantInfo());

        //如果是交易对账，需要将交易对账以及联合的信息共同存起来
        ReconcileRedundantDTO tradeRedundantInfo = domainAssembler.toRedundantInfo(orderInfo, correctionInfo);
        if (CorrectionConstant.CHANNEL_RECONCILE.equals(correctionInfo.getSysSource()) && Objects.nonNull(correctionInfo.getChannelReconcileRedundant())) {
            ReconcileRedundantDTO reconcileRedundantDTO = domainAssembler.toRedundantInfo(correctionInfo.getChannelReconcileRedundant());
            this.wrapReconcileRedundant(reconcileRedundantDTO,tradeRedundantInfo);
            domainCorrectionInfo.setRedundantInfo(reconcileRedundantDTO);
            domainCorrectionInfo.setTradeType(tradeType);
            domainCorrectionInfo.setChannelCode(correctionInfo.getChannelReconcileRedundant().getChannelCode());
        } else {
            //校验
            orderInfo.validation();
            //填充交易类型
            domainCorrectionInfo.setTradeType(orderInfo.getAssetInfo().getTradeType());
            domainCorrectionInfo.setChannelCode(this.getTradeChannelCode(orderInfo));
            domainCorrectionInfo.setRedundantInfo(tradeRedundantInfo);
        }
    }

    private void wrapReconcileRedundant(ReconcileRedundantDTO reconcileRedundantDTO,ReconcileRedundantDTO tradeRedundantInfo){
        reconcileRedundantDTO.setProductCode(tradeRedundantInfo.getProductCode());
        reconcileRedundantDTO.setMerchantNo(tradeRedundantInfo.getMerchantNo());
        reconcileRedundantDTO.setRefundTypeSource(tradeRedundantInfo.getRefundTypeSource());
        reconcileRedundantDTO.setRetryType(tradeRedundantInfo.getRetryType());
        reconcileRedundantDTO.setErrorCode(tradeRedundantInfo.getErrorCode());
        reconcileRedundantDTO.setErrorMsg(tradeRedundantInfo.getErrorMsg());
        reconcileRedundantDTO.setChannelCompleteTime(tradeRedundantInfo.getChannelCompleteTime());
    }

    private TradeType getChannelTradeType(ChannelReconcileRedundantDTO channelReconcileRedundant,String correctionCode){
        if (Objects.isNull(channelReconcileRedundant)|| StringUtils.isBlank(correctionCode)){
            return Nullable.getNullVal();
        }
        String tradeTypeStr = CorrectionConstant.EXTERNAL_DIFFERENCE_CORRECTION_CODE.contains(correctionCode) ? channelReconcileRedundant.getChannelTradeType() : channelReconcileRedundant.getTradeType();
        return TradeType.getTradeTypeByName(tradeTypeStr);
    }
}
