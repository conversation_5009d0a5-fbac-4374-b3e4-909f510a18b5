package com.payermax.operating.correction.domainservice.matcher.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.AutoHandleCorrectionOrderDTO;
import com.payermax.operating.correction.domain.dto.CorrectionAutoStrategyCodeHandlerDTO;
import com.payermax.operating.correction.domainservice.matcher.AbstractMatcher;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyExpressionInfo;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyRule;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.ReconcileRedundantDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @desc 内部单边匹配器
 * @date 2023/8/30
 */
@Slf4j
@Component(value = CorrectionConstant.INTERNAL_UNILATERAL_BEAN)
public class InternalUnilateralMatcher extends AbstractMatcher {

    @Resource
    private IDomainRepository domainRepository;

    @Override
    public List<CorrectionAutoStrategyCodeHandlerDTO> matchAutoHandlerStrategyInfo(StrategyRule strategyRule, AutoHandleCorrectionOrderDTO autoHandleCorrectionOrderDTO, GlobalOrderInfo globalOrderInfo) {
        List<CorrectionAutoStrategyCodeHandlerDTO> list = Lists.newArrayList();
        if (Objects.isNull(strategyRule.getStrategyExpressions()) || strategyRule.getStrategyExpressions().isEmpty()) {
            return Nullable.getNullVal();
        }

        CorrectionOrderInfoDTO checkingOrderInfo = domainRepository.getValidCorrectionOrderInfoDto(autoHandleCorrectionOrderDTO.getCorrectionNo());

        Set<Map.Entry<String, List<StrategyExpressionInfo>>> entries = strategyRule.getStrategyExpressions().entrySet();
        StrategyExpressionInfo strategyExpressionInfo  = Nullable.getNullVal();
        //按照凭证规则去遍历
        for (Map.Entry<String, List<StrategyExpressionInfo>> entry : entries) {
            String key = entry.getKey();
            List<StrategyExpressionInfo> strategyExpressionInfoList = entry.getValue();
            //如果没配置规则，或者匹配规则不满足，则返回空
            if (CollectionUtils.isEmpty(strategyExpressionInfoList)) {
                continue;
            }
            //定制化处理器，并不支持多个维度策略的配置，这里只会配置一个策略，因此可以直接取第一个
            if (strategyExpressionInfoList.size() > 1) {
                continue;
            }
            strategyExpressionInfo = strategyExpressionInfoList.get(0);//CHECKED
            if (!super.strategyMatch(strategyExpressionInfo.getExps(), autoHandleCorrectionOrderDTO)) {
                continue;
            }
            DCVoucherType voucherType = DCVoucherType.getVoucherTypeByName(key);
            ReconcileRedundantDTO checkingChannelRedundantInfo = checkingOrderInfo.getOriginalTradeRedundantInfo();
            //查询提交单号
            List<CorrectionOrderInfoDTO> verifiedCommitOrderInfo = domainRepository.getCorrectionOrderInfos(strategyExpressionInfo.getMappingCorrectionCode(), checkingOrderInfo, checkingChannelRedundantInfo, voucherType);
            log.info("InternalUnilateralMatcher matchAutoHandlerStrategyInfo verifiedCommitOrderInfo [voucherType:[{}], checkingChannelRedundantInfo:[{}],]", voucherType.name(), JSONObject.toJSONString(checkingChannelRedundantInfo));
            if (CollectionUtils.isNotEmpty(verifiedCommitOrderInfo)) {
                //包装匹配上的数据
                super.wrapAutoHandleCorrectionInfoList(list, verifiedCommitOrderInfo, strategyExpressionInfo);
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            CorrectionBasicInfoDTO basicInfo = domainRepository.getValidBasicInfo(autoHandleCorrectionOrderDTO.getCorrectionCode(), autoHandleCorrectionOrderDTO.getTradeType());
            //将自己也添加为自动处理对象
            checkingOrderInfo.setOperationCorrectionCode(basicInfo.getCorrectionCode());
            super.addSelfCorrectionOrderInfo(list, checkingOrderInfo, strategyExpressionInfo);
        }

        return list;
    }

}
