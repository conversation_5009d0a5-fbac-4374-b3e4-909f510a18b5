package com.payermax.operating.correction.domainservice.matcher;

import com.payermax.operating.correction.domain.dto.AutoHandleCorrectionOrderDTO;
import com.payermax.operating.correction.domain.dto.CorrectionAutoStrategyCodeHandlerDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;

import java.util.List;

/**
 * The interface Correction auto handle match.
 *
 * <AUTHOR>
 * @desc 差错自动处理匹配器
 * @date 2023 /4/20
 */
public interface CorrectionAutoHandleStrategyMatch {

    /**
     * Strategy match rule delay correction strategy code handler dto.
     *
     * @param globalOrderInfo              the global order info
     * @param autoHandleCorrectionOrderDTO the auto handle correction order dto
     * @return the delay correction strategy code handler dto
     */
    List<CorrectionAutoStrategyCodeHandlerDTO> strategyMatchRule(GlobalOrderInfo globalOrderInfo, AutoHandleCorrectionOrderDTO autoHandleCorrectionOrderDTO);
}
