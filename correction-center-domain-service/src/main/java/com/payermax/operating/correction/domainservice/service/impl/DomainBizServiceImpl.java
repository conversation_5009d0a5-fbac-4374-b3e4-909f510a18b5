package com.payermax.operating.correction.domainservice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.CorrectionBaseInfo;
import com.payermax.operating.correction.core.common.dto.OperationManualFillIn;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.GlobalOrderReadEvent;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.CorrectionAutoStrategyCodeHandlerDTO;
import com.payermax.operating.correction.domain.dto.EventTransferInfo;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domain.dto.RetryInfo;
import com.payermax.operating.correction.domain.enums.CorrectionEvent;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domain.enums.SourceEnum;
import com.payermax.operating.correction.domainservice.assembler.CorrectionDomainServiceAssembler;
import com.payermax.operating.correction.domainservice.factory.BizFactory;
import com.payermax.operating.correction.domainservice.machine.CorrectionOrderStateMachine;
import com.payermax.operating.correction.domainservice.machine.context.CorrectionOrderStateMachineContext;
import com.payermax.operating.correction.domainservice.matcher.CorrectionAutoHandleStrategyMatch;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.payermax.operating.correction.domainservice.service.DomainOperationService;
import com.payermax.operating.correction.domainservice.template.CorrectionHandlerTemplate;
import com.payermax.operating.correction.domainservice.utils.CommonDomainUtil;
import com.payermax.operating.correction.domainservice.utils.ProcessorBeanUtils;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.config.nacos.constant.NacosGlobalConfigProperties;
import com.payermax.operating.correction.integration.config.nacos.repository.ConfigRepository;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.queue.MqResultNotifyRepository;
import com.payermax.operating.correction.integration.queue.dto.CorrectionEventMessageInfo;
import com.payermax.operating.correction.integration.rpc.merchant.MerchantReository;
import com.payermax.operating.correction.integration.rpc.merchant.dto.MerchantBaseInfo;
import com.payermax.operating.correction.integration.rpc.merchant.dto.MerchantBaseInfoRequest;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc 门面模板实现类
 * @date 2022/9/28
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DomainBizServiceImpl implements DomainBizService {

    private final BizFactory bizFactory;

    private final CorrectionHandlerTemplate template;

    private final CorrectionOrderStateMachine orderStateMachine;

    private final CorrectionHandlerTemplate correctionHandlerTemplate;

    private final IDomainRepository domainRepository;

    private final DomainOperationService domainOperationService;

    private final NacosGlobalConfigProperties globalConfigProperties;

    @Resource
    private CorrectionAutoHandleStrategyMatch autoHandleStrategyMatch;

    @Resource
    private CorrectionDomainServiceAssembler domainServiceAssembler;

    @Resource
    private MqResultNotifyRepository notifyRepository;

    @Resource
    private ConfigRepository configRepository;
    @Resource
    private MerchantReository merchantReository;


    @Override
    @SneakyThrows
    public void addEvent(OperationDomainCorrectionInfo operationDomainCorrectionInfo) {
        DomainCorrectionInfo domainCorrectionInfo = Nullable.getNullVal();
        try {
            if (CorrectionConstant.DELAY_CORRECTION_CODE.contains(operationDomainCorrectionInfo.getBaseInfo().getCorrectionCode())) {
                Thread.sleep(globalConfigProperties.getRefundDelayTime());
            }
            domainCorrectionInfo = bizFactory.addCorrectionEventPrepareFactory(operationDomainCorrectionInfo);
            template.addCorrectionEvent(domainCorrectionInfo);

            //调用自动处理能力
            this.autoHandler(domainCorrectionInfo);

        } catch (DuplicateKeyException e) {
            //先更新后查询
            domainRepository.updateCorrectionIrrelevantInfo(domainCorrectionInfo);

            DomainCorrectionInfo correctionInfo = correctionHandlerTemplate.queryCorrectionOrderInfo(operationDomainCorrectionInfo.getBaseInfo());

            //如果是CC003则重试
            if (CorrectionConstant.RETRY_CHANNEL_CODE.contains(operationDomainCorrectionInfo.getBaseInfo().getCorrectionCode())) {
                this.resetHandleRecord(correctionInfo);
            }

            //判断是否满足自动处理策略
            this.autoHandler(domainCorrectionInfo);

            //如果已经是终态，则发起处理结果通知
            domainRepository.handlerResultNotify(correctionInfo);
        } catch (Exception e) {
            log.error("addEvent run exception:", e);
            if (SourceEnum.SYSTEM == operationDomainCorrectionInfo.getSource() && operationDomainCorrectionInfo.getRetryInfo().getMsgRetry() < CorrectionConstant.NUM_FIVE) {
                CorrectionEventMessageInfo correctionEventMessageInfo = domainServiceAssembler.toCorrectionEventMessageInfo(operationDomainCorrectionInfo);
                correctionEventMessageInfo.getMessageBody().setRetryNum(operationDomainCorrectionInfo.getRetryInfo().getMsgRetry());
                correctionEventMessageInfo.getMessageBody().setReconcileRedundantInfo(domainServiceAssembler.toChannelReconcileRedundantInfo(operationDomainCorrectionInfo.getChannelReconcileRedundant()));
                notifyRepository.delayPushEvent(correctionEventMessageInfo);
                return;
            }
            log.error("add correctionEvent exception:,eventInfo :{}", JSONObject.toJSONString(operationDomainCorrectionInfo));
        }
    }

    @Override
    public void chooseStrategyCode(OperationDomainCorrectionInfo operationDomainCorrectionInfo) {
        if (CommonDomainUtil.getTransferFlag(operationDomainCorrectionInfo.getTransferInfo())) {
            template.eventTransfer(operationDomainCorrectionInfo);
            return;
        }
        template.chooseProcessStrategy(operationDomainCorrectionInfo);
    }

    @Override
    public void approval(OperationDomainCorrectionInfo operationDomainCorrectionInfo) {
        template.approval(operationDomainCorrectionInfo);
    }

    @Override
    public void execStrategy(String correctionNo, Integer retry) {
        CorrectionOrderInfoDTO orderInfoDTO = domainRepository.getValidCorrectionOrderInfoDto(correctionNo);
        if (ProcessStatusEnum.PENDING == ProcessStatusEnum.getByName(orderInfoDTO.getProcessStatus())) {
            DomainCorrectionInfo orderInfoVo = bizFactory.prepareCorrectionInfoFactory(correctionNo, GlobalOrderReadEvent.OPERATION);
            RetryInfo retryInfo = Optional.ofNullable(orderInfoVo.getRetryInfo()).orElseGet(RetryInfo::new);
            retryInfo.setHandleRetry(retry);
            orderInfoVo.setRetryInfo(retryInfo);
            template.execStrategy(orderInfoVo);
        }
    }

    @Override
    public DomainCorrectionInfo eventQuery(CorrectionBaseInfo correctionBaseInfo) {
        return template.queryCorrectionOrderInfo(correctionBaseInfo);
    }

    @Override
    public void bounceBackEvent(String correctionNo) {
        DomainCorrectionInfo correctionInfo = bizFactory.prepareCorrectionInfoFactory(correctionNo, GlobalOrderReadEvent.OPERATION);
        CorrectionOrderHandlerRecordDTO handlerRecordDTO = correctionInfo.getHandlerRecordMap().get(ProcessorBeanUtils.getBeanNameUniqueKey(CorrectionConstant.CORRECTION_PAYOUTS_PROCESSOR, CommonStatusEnum.SUCCESS));
        correctionInfo.setCurrentHandlerRecord(handlerRecordDTO);
        if (Objects.nonNull(handlerRecordDTO)) {
            log.info("bounceBackEvent,correctionNo : [{}]", correctionNo);
            orderStateMachine.sendEvent(ProcessStatusEnum.SUCCESS, CorrectionEvent.NOTIFY_FAILED, new CorrectionOrderStateMachineContext(correctionInfo));
        }
    }


    @Override
    public void autoCorrectionHandle(CorrectionAutoStrategyCodeHandlerDTO correctionAutoStrategyCodeHandle) {
        //这里需要策略模式去执行
        OperationDomainCorrectionInfo correctionInfoDTO = domainServiceAssembler.toOperationDomainCorrectionInfo(correctionAutoStrategyCodeHandle);
        this.chooseStrategyCode(correctionInfoDTO);
    }

    @Override
    public void registrationCompleteEvent(VoucherInfo voucherInfo, String correctionCode, String memo, String operator) {
        CorrectionOrderInfoDTO orderInfoDTO = domainRepository.getValidCorrectionOrderInfoDto(correctionCode, voucherInfo.getVoucherNo());
        if (domainRepository.handlerIntersectionMark(orderInfoDTO.getBaseInfo().getCorrectionCode(), orderInfoDTO.getBaseInfo().getTradeType())) {
            return;
        }
        OperationManualFillIn operationManual = new OperationManualFillIn();
        operationManual.setMemo(memo);
        OperationDomainCorrectionInfo operationDomainCorrectionInfo = new OperationDomainCorrectionInfo(orderInfoDTO.getBaseInfo().getCorrectionNo(), CorrectionConstant.REGISTRATION_PROCESS, operator, operationManual);
        //添加操作人
        template.registerHandler(operationDomainCorrectionInfo);
    }

    @Override
    public void transfer(EventTransferInfo transferInfo) {
        OperationDomainCorrectionInfo info = domainServiceAssembler.toOperationDomainCorrectionInfo(transferInfo);
        template.eventTransfer(info);
    }

    @Override
    public void resetHandleRecord(DomainCorrectionInfo correctionInfo) {
        if (Objects.isNull(correctionInfo)) {
            return;
        }
        //寻找当前处理记录
        CorrectionOrderHandlerRecordDTO handlerRecordDTO = correctionInfo.getHandlerRecordMap().get(ProcessorBeanUtils.getBeanNameUniqueKey(CorrectionConstant.ORIGINAL_CHANNEL_REFUND_RETRY_PROCESSOR, CommonStatusEnum.PENDING));
        correctionInfo.setCurrentHandlerRecord(handlerRecordDTO);
        if (Objects.nonNull(handlerRecordDTO)) {
            orderStateMachine.sendEvent(ProcessStatusEnum.PENDING, CorrectionEvent.HANDLER_FAILED, new CorrectionOrderStateMachineContext(correctionInfo));
        }
    }

    @Override
    public void jobAutoHandler(String correctionNo) {
        CorrectionOrderInfoDTO orderInfoDTO = domainRepository.getValidCorrectionOrderInfoDto(correctionNo);/*获取差错订单信息，已有存在性校验*/
        if (ProcessStatusEnum.SUCCESS.name().equals(orderInfoDTO.getProcessStatus())) { /*状态校验  成功的则不再处理*/
            log.info("jobAutoHandler,correctionNo : [{}],processStatus : [{}]", correctionNo, orderInfoDTO.getProcessStatus());
            return;
        }

        /*构造成符合自动处理的入参*/
        OperationDomainCorrectionInfo operationDomainCorrection = domainServiceAssembler.toCorrectionInfo(orderInfoDTO);
        operationDomainCorrection.getBaseInfo().setVoucherInfo(new VoucherInfo(orderInfoDTO.getOriVoucherInfo().getVoucherNo(), orderInfoDTO.getOriVoucherInfo().getVoucherType()));
        operationDomainCorrection.fillSystemCorrectionInfoDTO();

        DomainCorrectionInfo domainCorrectionInfo = bizFactory.addCorrectionEventPrepareFactory(operationDomainCorrection);

        //调用自动处理能力
        autoHandler(domainCorrectionInfo);
    }

    private void autoHandler(DomainCorrectionInfo domainCorrectionInfo) {
        if (Objects.isNull(domainCorrectionInfo)) {
            return;
        }
        //获取商户业务类型
        if (Objects.nonNull(domainCorrectionInfo.getMerchantInfo())) {
            MerchantBaseInfo merchantBaseInfo = merchantReository.getMerchantBaseInfo(MerchantBaseInfoRequest.builder()
                    .merchantNo(domainCorrectionInfo.getMerchantInfo().getMerchantNo())
                    .appId(domainCorrectionInfo.getMerchantInfo().getMerchantAppId())
                    .build());
            domainCorrectionInfo.richMerchantInfo(merchantBaseInfo.getBizType());
        }

        //自动处理处理器调用
        List<CorrectionAutoStrategyCodeHandlerDTO> autoStrategyCodeHandlers = autoHandleStrategyMatch.strategyMatchRule(domainCorrectionInfo.getGlobalOrderInfo(), domainServiceAssembler.toAutoHandleCorrectionOrder(domainCorrectionInfo));
        if (CollectionUtils.isNotEmpty(autoStrategyCodeHandlers)) {
            for (CorrectionAutoStrategyCodeHandlerDTO autoStrategyCodeHandler : autoStrategyCodeHandlers) {
                this.autoCorrectionHandle(autoStrategyCodeHandler);
            }
        }
    }
}