package com.payermax.operating.correction.domainservice.utils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.enums.ExtendEnum;
import com.payermax.operating.correction.core.common.enums.UserInfoEnum;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.EventTransferInfo;
import com.payermax.operating.correction.domain.dto.ExtendInfoDTO;
import com.payermax.operating.correction.domain.dto.ExtraUserInfoDTO;
import com.payermax.operating.correction.domain.enums.FlagEnum;
import com.payermax.operating.correction.domain.enums.RiskStatusEnum;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyProcessorInfo;
import com.payermax.operating.correction.integration.dto.PayPaymentInstanceInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.risk.dto.RiskEventInfo;
import com.payermax.operating.correction.integration.rpc.risk.enums.RiskType;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 通用工具类
 * @date 2022/12/21
 */
public class CommonDomainUtil {

    public static List<ExtraUserInfoDTO> getExtraUserInfoList(String extraUserInfoStr, UserInfoEnum userInfoEnum) {
        extraUserInfoStr = StringUtils.defaultString(extraUserInfoStr, Symbols.ARRAY_STR);
        List<ExtraUserInfoDTO> userInfoList = JSONObject.parseObject(extraUserInfoStr, new TypeReference<List<ExtraUserInfoDTO>>() {
        });
        return userInfoList.stream().filter(e -> e.getUserEnum() == userInfoEnum).collect(Collectors.toList());
    }

    public static List<ExtendInfoDTO> getExtendInfoList(String extendInfoStr, ExtendEnum extendEnum) {
        extendInfoStr = StringUtils.defaultString(extendInfoStr, Symbols.ARRAY_STR);
        List<ExtendInfoDTO> extendInfoDTOS = JSONObject.parseObject(extendInfoStr, new TypeReference<List<ExtendInfoDTO>>() {
        });
        return extendInfoDTOS.stream().filter(e -> e.getOperation() == extendEnum).collect(Collectors.toList());
    }

    public static void buildEventBody(RiskEventInfo eventInfo, GlobalOrderInfo globalOrderInfo, Money correctionAmount, ExtraUserInfoDTO extraUserInfoDTO, RiskStatusEnum riskStatus){
        PayPaymentInstanceInfoDTO paymentInstanceInfoDTO = JSONObject.parseObject(extraUserInfoDTO.getExtraInfo(), new TypeReference<PayPaymentInstanceInfoDTO>() {
        });
        JSONObject json = eventInfo.getEventBody();
        json.put("refundRequestNo",globalOrderInfo.getTradeInfo().getRefundInfo().getRefundOrder().getOrderNo());
        json.put("tradeOrderNo",globalOrderInfo.getTradeInfo().getTradeOrder().getOrderNo());
        json.put("refundAmout",correctionAmount.getAmount().toString());
        json.put("refundCurrency",correctionAmount.getCurrency().getCurrencyCode());
        json.put("payAmout",globalOrderInfo.getAssetInfo().getPayMoney().getAmount());
        json.put("payCurrency",globalOrderInfo.getAssetInfo().getPayMoney().getCurrency().getCurrencyCode());
        json.put("paymentMethod",paymentInstanceInfoDTO.getPaymentInstanceDTO().getPaymentMethodNo());
        json.put("country",paymentInstanceInfoDTO.getCountry());
        json.put("merchantNo",globalOrderInfo.getOriginalMerchantInfo().getMerchantNo());
        json.put("refundCreateTime",globalOrderInfo.getTradeInfo().getRefundInfo().getCreateTime());
        if (eventInfo.getRiskType()== RiskType.REFUND_TO_PAYOUTS_EVENT_AFTER){
            json.put("refundCreateTime",globalOrderInfo.getChannelInfo().getCompleteTime());
            json.put("status", Optional.ofNullable(riskStatus).map(RiskStatusEnum::getCode).orElse(Nullable.getNullVal()));
        }

    }

    public static Boolean getTransferFlag(EventTransferInfo info){
        return Objects.nonNull(info) && FlagEnum.YES == info.getFlag();
    }

    /**
     * <AUTHOR>
     * @desc 根据策略其返回策略码公用类
     * @date 2023/8/29
     */
    public static String getStrategyCode(StrategyProcessorInfo strategyProcessor){
        return Optional.ofNullable(strategyProcessor).map(StrategyProcessorInfo::getStrategyCode).orElse(StringUtils.EMPTY);
    }

    public static String getUUID(){
        return UUID.randomUUID().toString().replace(Symbols.LINE, StringUtils.EMPTY).toLowerCase();
    }

    /**
     * 根据差错单，判断当前差错单是处于第几笔
     * @param collect
     * @param correctionInfo
     * @return
     */
    public static   int getCorrectSuffixFromOrderInfo(List<CorrectionOrderInfoDTO> collect, DomainCorrectionInfo correctionInfo){

        //根据推入差错的时间进行排序，并取对应的顺序
        int order = 1;
        for (int i = 0; i < collect.size(); i++) {
            if (correctionInfo.getCorrectionNo().equals(collect.get(i).getBaseInfo().getCorrectionNo())) {
                order = i + 1;
                break;
            }
        }

        return order;
    }

    /**
     *获取差错后缀C1
     * @param correctOrder
     * @return
     */
    public static String getCorrectSuffix(int correctOrder){
        return CorrectionConstant.CORRECTION_FLAG + correctOrder;
    }
}
