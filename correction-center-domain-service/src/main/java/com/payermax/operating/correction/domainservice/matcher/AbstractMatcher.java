package com.payermax.operating.correction.domainservice.matcher;

import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.enums.ConditionEnum;
import com.payermax.operating.correction.domain.dto.AutoHandleCorrectionOrderDTO;
import com.payermax.operating.correction.domain.dto.CorrectionAutoStrategyCodeHandlerDTO;
import com.payermax.operating.correction.integration.config.nacos.model.Expression;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyExpressionInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.utils.MatchUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public abstract class AbstractMatcher implements StrategyMatcher {

    protected static final ExpressionParser PARSER = new SpelExpressionParser();

    protected Boolean strategyMatch(List<Expression> exps, AutoHandleCorrectionOrderDTO autoHandleCorrectionOrderDTO) {
        if (CollectionUtils.isEmpty(exps)) {
            return Boolean.FALSE;
        }
        for (Expression exp : exps) {
            String value = (String) PARSER.parseExpression(exp.getExp()).getValue(autoHandleCorrectionOrderDTO);
             boolean matchFlag = MatchUtils.conditionRule(ConditionEnum.getBySymbol(exp.getSymbol()), exp.getConditionVal(), value);
            //如果有一个没匹配上 直接返回false
            if (!matchFlag) {
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    public  Boolean strategyMatch(List<Expression> exps, GlobalOrderInfo globalOrderInfo) {
        if (CollectionUtils.isEmpty(exps)) {
            return Boolean.FALSE;
        }
        for (Expression exp : exps) {
            String value = (String) PARSER.parseExpression(exp.getExp()).getValue(globalOrderInfo);
            boolean matchFlag = MatchUtils.conditionRule(ConditionEnum.getBySymbol(exp.getSymbol()), exp.getConditionVal(), value);
            //任意一个匹配上 直接返回
            if (matchFlag) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    protected void wrapAutoHandleCorrectionInfoList(List<CorrectionAutoStrategyCodeHandlerDTO> list, List<CorrectionOrderInfoDTO> verifiedCommitOrderInfo, StrategyExpressionInfo strategyExpressionInfo) {
        verifiedCommitOrderInfo.forEach(e -> {
            CorrectionAutoStrategyCodeHandlerDTO strategyCodeHandler = CorrectionAutoStrategyCodeHandlerDTO.builder()
                    .strategyCode(strategyExpressionInfo.getStrategyProcessor())
                    .childStrategyCode(strategyExpressionInfo.getChildStrategyProcessor())
                    .operator(CorrectionConstant.SYSTEM_OPERATION)
                    .correctionNo(e.getBaseInfo().getCorrectionNo())
                    .operationCorrectionCode(strategyExpressionInfo.getChildCorrectionCode())
                    .build();
            list.add(strategyCodeHandler);
        });
    }

    /**
     * 将自己标记为自动出来对象
     */
    protected void addSelfCorrectionOrderInfo(List<CorrectionAutoStrategyCodeHandlerDTO> list, CorrectionOrderInfoDTO checkingOrderInfo, StrategyExpressionInfo strategyExpressionInfo) {
        CorrectionAutoStrategyCodeHandlerDTO strategyCodeHandler = CorrectionAutoStrategyCodeHandlerDTO.builder()
                .strategyCode(strategyExpressionInfo.getStrategyProcessor())
                .childStrategyCode(strategyExpressionInfo.getChildStrategyProcessor())
                .operator(CorrectionConstant.SYSTEM_OPERATION)
                .correctionNo(checkingOrderInfo.getBaseInfo().getCorrectionNo())
                .operationCorrectionCode(checkingOrderInfo.getOperationCorrectionCode())
                .build();
        list.add(strategyCodeHandler);
    }
}
