package com.payermax.operating.correction.domainservice.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Sets;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.StringUtil;
import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.*;
import com.payermax.operating.correction.core.common.utils.EventCenter;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.core.common.utils.ObjectsUtils;
import com.payermax.operating.correction.domain.dto.ExtendInfoDTO;
import com.payermax.operating.correction.domain.dto.ExtraUserInfoDTO;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domainservice.assembler.CorrectionDomainServiceAssembler;
import com.payermax.operating.correction.domainservice.events.ResultNotifyEvent;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.utils.CommonDomainUtil;
import com.payermax.operating.correction.domainservice.utils.ProcessorBeanUtils;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.config.nacos.constant.NacosGlobalConfigProperties;
import com.payermax.operating.correction.integration.config.nacos.model.CorrectionSystemInfo;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyProcessorInfo;
import com.payermax.operating.correction.integration.config.nacos.repository.ConfigRepository;
import com.payermax.operating.correction.integration.config.persistence.repository.RdmsConfigRepository;
import com.payermax.operating.correction.integration.dto.PaymentInstanceDTO;
import com.payermax.operating.correction.integration.persistence.kvstore.repository.KVRepository;
import com.payermax.operating.correction.integration.persistence.rdms.dto.*;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionOperationRepository;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionRepository;
import com.payermax.operating.correction.integration.rpc.cashiercore.dto.CashierCorePaymentInstanceInfo;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.datacenter.repository.DataCenterRepository;
import com.payermax.operating.correction.integration.rpc.dingtalk.dto.DingTalkMessageInfo;
import com.payermax.operating.correction.integration.rpc.dingtalk.repository.DingTalkRepository;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsCoreInfo;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsRuleInfo;
import com.payermax.operating.correction.integration.rpc.payouts.repository.PayoutsRepository;
import com.payermax.operating.correction.integration.rpc.productcenter.dto.CashierProductInfo;
import com.payermax.operating.correction.integration.rpc.productcenter.repository.ProductCenterRepository;
import com.payermax.operating.correction.integration.rpc.voucher.repository.VoucherRepository;
import com.payermax.operating.correction.integration.rpc.voucher.repository.dto.VoucherRequestDTO;
import com.payermax.operating.correction.integration.utils.MatchUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 获取资源并对资源做空校验
 * @date 2022/10/8
 */
@Repository
@Slf4j
@AllArgsConstructor
public class IDomainRepositoryImpl implements IDomainRepository {

    private final RdmsConfigRepository rdmsConfigRepository;

    private final ConfigRepository configRepository;

    private final CorrectionRepository correctionRepository;

    private final CorrectionDomainServiceAssembler domainServiceAssembler;

    private final DataCenterRepository dataCenterRepository;

    private final ProductCenterRepository productCenterRepository;

    private final PayoutsRepository payoutsRepository;

    private final VoucherRepository voucherRepository;

    private final KVRepository kvRepository;

    private final CorrectionOperationRepository operationRepository;

    private final NacosGlobalConfigProperties globalConfigProperties;

    private final DingTalkRepository dingTalkRepository;

    @Resource(name = "bizAsyncExecutor")
    private ThreadPoolTaskExecutor bizAsyncExecutor;

    @Override
    public CorrectionBasicInfoDTO getValidBasicInfo(String correctionCode) {
        CorrectionBasicInfoDTO basicInfo = rdmsConfigRepository.getBasicInfo(correctionCode);
        AssertUtil.notNull(basicInfo, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.CORRECTION_CODE_INVALID);
        return basicInfo;
    }

    @Override
    public String getValidCorrectionNo(String voucherNo, TradeType tradeType, String correctionCode) {
        AssertUtil.notNull(tradeType, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.TRADE_TYPE_INVALID);
        return voucherRepository.getCorrectionVoucherNo(new VoucherRequestDTO(voucherNo, tradeType, correctionCode));
    }

    @Override
    public String getValidCorrectionNo(String voucherNo, String correctionCode, Money payoutMoney, String account) {
        return voucherRepository.getCorrectionVoucherNo(new VoucherRequestDTO(voucherNo, correctionCode, TradeType.PAYOUTS, payoutMoney, account));
    }

    @Override
    public CorrectionSystemInfo getValidCorrectionSys(String correctionCode, String sysSource) {
        CorrectionSystemInfo correctionSys = configRepository.getCorrectionSys(correctionCode, sysSource);
        AssertUtil.notNull(correctionSys, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.CORRECTION_SYSTEM_INFO_INVALID);
        return correctionSys;
    }

    @Override
    public StrategyProcessorInfo getValidStrategyInfo(String strategyCode) {
        CorrectionOperationStrategyInfoDTO dbInfo = rdmsConfigRepository.getCorrectionOperationStrategyInfo(strategyCode);
        AssertUtil.notNull(dbInfo, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.STRATEGY_INFO_INVALID_IN_DB);
        StrategyProcessorInfo strategyProcessor = configRepository.getStrategyProcessor(strategyCode);
        AssertUtil.notNull(strategyProcessor, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.STRATEGY_INFO_INVALID_IN_NACOS);
        strategyProcessor.setDbInfo(dbInfo);
        return strategyProcessor;
    }

    @Override
    public DomainCorrectionInfo getValidCorrectionOrderInfo(String correctionNo, OrderDetailType queryType, GlobalOrderReadEvent readEvent) {
        CorrectionOrderInfoDTO orderInfoDTO = this.getValidCorrectionOrderInfoDto(correctionNo);
        //convert orderInfoVo 信息
        DomainCorrectionInfo orderInfoVo = domainServiceAssembler.toDomainCorrectionInfo(orderInfoDTO);
        if (StringUtils.isNotBlank(orderInfoDTO.getOperationManual().getExtendInfo())) {
            orderInfoVo.setExtendInfoList(JSONObject.parseObject(orderInfoDTO.getOperationManual().getExtendInfo(), new TypeReference<List<ExtendInfoDTO>>() {
            }));
        }
        if (StringUtils.isNotBlank(orderInfoDTO.getOperationManual().getExtraUserInfo())) {
            orderInfoVo.setExtraUserInfoList(JSONObject.parseObject(orderInfoDTO.getOperationManual().getExtraUserInfo(), new TypeReference<List<ExtraUserInfoDTO>>() {
            }));
        }
        //填充策略信息
        if (StringUtils.isNotBlank(orderInfoDTO.getStrategyCode())) {
            orderInfoVo.setStrategyProcessor(this.getValidStrategyInfo(orderInfoDTO.getStrategyCode()));
        }
        //填充差错基础信息
        orderInfoVo.setBasicInfoDTO(this.getValidParentReasonBasicInfo(orderInfoDTO.getBaseInfo().getCorrectionCode()));
        if (StringUtils.isNotBlank(orderInfoDTO.getOperationCorrectionCode())) {
            orderInfoVo.setOperationBasic(this.getValidBasicInfo(orderInfoDTO.getOperationCorrectionCode()));
        }
        //填充差错系统信息
        orderInfoVo.setSystemInfo(this.getSystemInfos(orderInfoDTO.getBaseInfo().getCorrectionCode(), orderInfoDTO.getSysSource()));
        if (CorrectionConstant.EXTERNAL_DIFFERENCE_CORRECTION_CODE.contains(orderInfoVo.getBasicInfoDTO().getCorrectionCode())) {
            return orderInfoVo;
        }
        this.fillInHandlerRecord(orderInfoVo, queryType);
        //俩种场景需要渲染联合查询，
        // 1.非交易对账推送
        // 2.交易对账推送，但是确是CC001-payment
        if (!orderInfoVo.getSysSource().contains(CorrectionConstant.CHANNEL_RECONCILE) ||
                this.handlerIntersectionMark(orderInfoVo.getBasicInfoDTO().getCorrectionCode(), orderInfoVo.getTradeType())) {
            orderInfoVo.setGlobalOrderInfo(this.getGlobalOrderInfo(orderInfoVo.getOriVoucherInfo(), readEvent));
        }
        return orderInfoVo;
    }

    @Override
    public DomainCorrectionInfo getValidCorrectionOrderInfo(String correctionCode, String voucherNo) {
        AssertUtil.isTrue(!StringUtils.isAnyBlank(correctionCode, voucherNo), ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.CORRECTION_SYSTEM_INFO_INVALID);
        CorrectionOrderInfoDTO orderInfoDTO = correctionRepository.loadCorrectionOrderInfo(correctionCode, voucherNo);
        AssertUtil.notNull(orderInfoDTO, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.CORRECTION_SYSTEM_INFO_INVALID);
        DomainCorrectionInfo domainCorrectionInfo = domainServiceAssembler.toDomainCorrectionInfo(orderInfoDTO);
        //填充差错基础信息
        domainCorrectionInfo.setBasicInfoDTO(this.getValidParentReasonBasicInfo(orderInfoDTO.getBaseInfo().getCorrectionCode()));
        domainCorrectionInfo.setSystemInfo(this.getSystemInfos(orderInfoDTO.getBaseInfo().getCorrectionCode(), domainCorrectionInfo.getSysSource()));
        this.fillInHandlerRecord(domainCorrectionInfo, OrderDetailType.STRATEGY_QUERY);
        return domainCorrectionInfo;
    }

    @Override
    public CorrectionOrderInfoDTO getValidCorrectionOrderInfoDto(String correctionNo) {
        CorrectionOrderInfoDTO orderInfoDTO = correctionRepository.loadCorrectionOrderInfo(correctionNo);
        AssertUtil.notNull(orderInfoDTO, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.CORRECTION_NO_INVALID);
        return orderInfoDTO;
    }

    @Override
    public CorrectionOrderInfoDTO getValidCorrectionOrderInfoDto(String correctionCode, String voucherNo) {
        CorrectionOrderInfoDTO orderInfoDTO = correctionRepository.loadCorrectionOrderInfo(correctionCode, voucherNo);
        AssertUtil.notNull(orderInfoDTO, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.CORRECTION_SYSTEM_INFO_INVALID);
        return orderInfoDTO;
    }

    @Override
    public Boolean validChooseStrategyCode(String strategyCode, String correctionCode, TradeType tradeType) {
        List<CorrectionOperationUniqueDTO> mappingValidList = correctionRepository.getCorrectionCodeMappingValidList(correctionCode, tradeType);
        if (CollectionUtils.isEmpty(mappingValidList)) {
            return Boolean.FALSE;
        }
        CorrectionOperationUniqueDTO uniqueDTO = mappingValidList.stream().filter(e -> strategyCode.equals(e.getStrategyCode())).findFirst().orElse(Nullable.getNullVal());//CHECKED 根据主键筛选出来必然只有一个
        return Objects.nonNull(uniqueDTO);
    }

    @Override
    public CorrectionBasicInfoDTO getValidParentReasonBasicInfo(String correctionCode) {
        CorrectionBasicInfoDTO basicInfoDTO = configRepository.getParentReasonBasicMap().get(correctionCode);
        AssertUtil.notNull(basicInfoDTO, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.PARENT_BASIC_INVALID);
        return basicInfoDTO;
    }

    @Override
    public CashierProductInfo getValidCashierProductInfo(CashierCorePaymentInstanceInfo paymentInstanceInfo) {
        List<CashierProductInfo> cashierProduct = null;
        if(BooleanUtils.isTrue(globalConfigProperties.getSwitchFlag())){
            cashierProduct= productCenterRepository.getCashierProductV2(paymentInstanceInfo);
        }else{
            cashierProduct= productCenterRepository.getCashierProduct(paymentInstanceInfo);
        }

        if (cashierProduct.size() != CorrectionConstant.NUM_ONE) {
            throw new BusinessException(ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.PAYMENT_INSTANCE_INVALID);
        }
        return cashierProduct.get(0);//CHECKED
    }

    @Override
    public void validationPayoutRule(PayoutsRuleInfo payoutsRuleInfo, PayoutsCoreInfo payoutsCoreInfo) {
        AssertUtil.isTrue(payoutsRepository.payoutsRuleCheck(payoutsRuleInfo, payoutsCoreInfo), ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.PAYOUT_RULE_INVALID);
    }

    @Override
    public void validOperationCode(String operationCorrectionCode, String parentCorrectionCode) {
        CorrectionBasicInfoDTO basicInfo = this.getValidBasicInfo(operationCorrectionCode);
        AssertUtil.isTrue(parentCorrectionCode.equals(basicInfo.getParentCorrectionCode()), ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.CORRECTION_CODE_INVALID);
    }

    @Override
    public void updateCorrectionIrrelevantInfo(DomainCorrectionInfo domainCorrectionInfo) {
        if (Objects.isNull(domainCorrectionInfo)) {
            return;
        }
        CorrectionOrderInfoDTO dcOrderInfoDto = correctionRepository.loadCorrectionOrderInfo(domainCorrectionInfo.getBasicInfoDTO().getCorrectionCode(), domainCorrectionInfo.getVoucherNo());

        CorrectionOrderInfoDTO orderInfoDTO = domainServiceAssembler.toDBCorrectionOrderInfo(domainCorrectionInfo);
        orderInfoDTO.getBaseInfo().setCorrectionNo(dcOrderInfoDto.getBaseInfo().getCorrectionNo());
        String sysSourceStr = this.getSysSourceStr(dcOrderInfoDto.getSysSource(), domainCorrectionInfo.getSysSource());
        orderInfoDTO.setSysSource(sysSourceStr);
        correctionRepository.updateIrrelevantInfo(orderInfoDTO);
        //更新内存值
        domainCorrectionInfo.setSystemInfo(this.getSystemInfos(domainCorrectionInfo.getBasicInfoDTO().getCorrectionCode(), sysSourceStr));
    }

    @Override
    public List<CorrectionSystemInfo> getSystemInfos(String correctionCode, String sysSources) {
        //填充差错系统信息
        List<String> sysSourceList = ObjectsUtils.strToList(sysSources, Symbols.COMMA);
        return sysSourceList.stream().map(e -> this.getValidCorrectionSys(correctionCode, e)).collect(Collectors.toList());
    }

    @Override
    public GlobalOrderInfo getGlobalOrderInfo(VoucherInfo voucherInfo, GlobalOrderReadEvent readEvent) {
        GlobalOrderInfo orderInfo = Nullable.getNullVal();
        if (GlobalOrderReadEvent.DISPLAY == readEvent) {
            orderInfo = kvRepository.queryGlobalOrder(voucherInfo);
        }
        if (Objects.isNull(orderInfo)) {
            try {
                orderInfo = dataCenterRepository.dcGlobalQuery(voucherInfo.getVoucherType(), voucherInfo.getVoucherNo());
            } catch (Exception e) {
                //发送钉钉告警通知
                this.sendDataCenterDingTalkAlertMsg(voucherInfo.getVoucherType(), voucherInfo.getVoucherNo(), e.getMessage());
                throw e;
            }
            AssertUtil.notNull(orderInfo, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.INVALID_CORRECTION_INFO);
            final GlobalOrderInfo cacheOrderInfo = orderInfo;
            bizAsyncExecutor.execute(() -> kvRepository.storeGlobalOrder(voucherInfo, cacheOrderInfo));
        }
        return orderInfo;
    }

    @Override
    public List<String> getAllRegisterHandlerReason() {
        return configRepository.getAllRegisterHandlerList();
    }

    @Override
    public void refreshParentBasicInfoCache() {
        configRepository.refreshAllParentBasicInfo();
    }

    @Override
    public void handlerResultNotify(DomainCorrectionInfo correctionInfo) {
        if (Optional.ofNullable(correctionInfo).map(DomainCorrectionInfo::getProcessStatus).orElse(Nullable.getNullVal()) != ProcessStatusEnum.SUCCESS) {
            return;
        }
        correctionInfo.getSystemInfo().forEach(sysSource -> {
            EventCenter.publish(Publisher.HANDLE_RESULT.name(), new ResultNotifyEvent(correctionInfo.getOriVoucherInfo(), sysSource, CommonDomainUtil.getStrategyCode(correctionInfo.getStrategyProcessor()), correctionInfo.getMemo(), correctionInfo.getRedundantInfo()));
        });
    }

    @Override
    public List<CorrectionOrderInfoDTO> getCorrectionOrderInfos(String correctionCode, CorrectionOrderInfoDTO checkingOrderInfo, ReconcileRedundantDTO checkingChannelRedundantInfo, DCVoucherType dcVoucherType) {
        List<CorrectionOrderInfoDTO> list = Nullable.getNullVal();
        //凭证类型只能为提交单号或者三方单号
        if (!ObjectsUtils.anyMatch(dcVoucherType, DCVoucherType.CHANNEL_COMMIT_NO, DCVoucherType.THIRD_ORDER_NO)) {
            return list;
        } else if (DCVoucherType.CHANNEL_COMMIT_NO == dcVoucherType && StringUtil.isBlank(checkingChannelRedundantInfo.getChannelCommitNo())) {
            return list;
        } else if (DCVoucherType.THIRD_ORDER_NO == dcVoucherType && StringUtil.isBlank(checkingChannelRedundantInfo.getThirdOrderNo())) {
            return list;
        }
        String orgNameStr = checkingChannelRedundantInfo.queryOrgNameStr();
        String voucherInfoStr = DCVoucherType.CHANNEL_COMMIT_NO == dcVoucherType ? checkingChannelRedundantInfo.queryChannelCommitNoStr() : checkingChannelRedundantInfo.queryThirdOrderNoStr();
        String redundantInfoStr = orgNameStr + voucherInfoStr;
        QueryCheckingOrderDTO queryCheckingOrderDTO = QueryCheckingOrderDTO.builder()
                .processStatus(ProcessStatusEnum.PROCESSED.name())
                .tradeType(checkingOrderInfo.getBaseInfo().getTradeType())
                .correctionCode(correctionCode)
                .redundantInfoStr(redundantInfoStr)
                .build();
        //这里查询的结果只是相似匹配
        List<CorrectionOrderInfoDTO> correctionOrderInfoDTOS = correctionRepository.loadCorrectionOrderInfo(queryCheckingOrderDTO);
        //根据checking单号去做精准的过滤
        if (DCVoucherType.CHANNEL_COMMIT_NO == dcVoucherType) {
            list = correctionOrderInfoDTOS.stream().filter(e -> (e.getOriginalTradeRedundantInfo().getChannelCommitNo().equals(checkingChannelRedundantInfo.getChannelCommitNo())
                    && e.getOriginalTradeRedundantInfo().getOrgName().equals(checkingChannelRedundantInfo.getOrgName()))).collect(Collectors.toList());
        } else if (DCVoucherType.THIRD_ORDER_NO == dcVoucherType) {
            list = correctionOrderInfoDTOS.stream().filter(e -> (e.getOriginalTradeRedundantInfo().getThirdOrderNo().equals(checkingChannelRedundantInfo.getThirdOrderNo())
                    && e.getOriginalTradeRedundantInfo().getOrgName().equals(checkingChannelRedundantInfo.getOrgName()))).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public CorrectionBasicInfoDTO getValidBasicInfo(String parentCorrectionCode, TradeType tradeType) {
        if (StringUtils.isBlank(parentCorrectionCode) && Objects.isNull(tradeType)) {
            return Nullable.getNullVal();
        }
        List<CorrectionBasicInfoDTO> correctionBasicInfo = operationRepository.loadChildrenReasonBasicByParent(parentCorrectionCode, tradeType);
        if (correctionBasicInfo.size() == CorrectionConstant.NUM_ONE) {
            return correctionBasicInfo.get(CorrectionConstant.NUM_ZERO);
        }
        return Nullable.getNullVal();
    }

    /**
     * 是否存在处理交集
     */
    @Override
    public Boolean handlerIntersectionMark(String correctionCode, TradeType tradeType) {
        AssertUtil.isTrue(StringUtils.isNotBlank(correctionCode), ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.CORRECTION_CODE_INVALID);
        if (Objects.isNull(tradeType)) {
            return Boolean.TRUE;
        }
        return MatchUtils.conditionRule(ConditionEnum.getBySymbol(globalConfigProperties.getChannelReconcileSymbol()), globalConfigProperties.getChannelReconcileConditionValue(), configRepository.getMatchUniqueKey(correctionCode, tradeType));
    }

    private void fillInHandlerRecord(DomainCorrectionInfo orderInfoVo, OrderDetailType queryType) {
        //填充历史处理记录
        List<CorrectionOrderHandlerRecordDTO> recordList = correctionRepository.loadCorrectionRecordList(new CorrectionHandlerUniInfo(orderInfoVo.getCorrectionNo(), Optional.ofNullable(orderInfoVo.getStrategyProcessor()).map(StrategyProcessorInfo::getStrategyCode).orElse(Nullable.getNullVal()), StringUtils.EMPTY, StringUtils.EMPTY), queryType);
        //执行器重复，则使用最新的一条处理器记录
        Map<String, CorrectionOrderHandlerRecordDTO> recordMap = recordList.stream().collect(Collectors.toMap(e -> ProcessorBeanUtils.getBeanNameUniqueKey(e.getHandlerInfo().getHandlerType(), e.getStatus()), Function.identity(), (key1, key2) -> key1));
        orderInfoVo.setHandlerRecordMap(recordMap);
    }

    private String getSysSourceStr(String dbSysSource, String curSysSource) {
        List<String> systemList = ObjectsUtils.strToList(dbSysSource, Symbols.COMMA);
        HashSet<String> systemSets = Sets.newHashSet(systemList);
        if (systemSets.contains(curSysSource)) {
            return dbSysSource;
        }
        return dbSysSource + Symbols.COMMA + curSysSource;
    }

    private void sendDataCenterDingTalkAlertMsg(DCVoucherType voucherType, String orderId, String msg) {
        DingTalkMessageInfo dingTalkMessageInfo = buildDataCenterDingTalkMsg(voucherType, orderId, msg);

        Long errorNum = kvRepository.incr(CorrectionConstant.DATA_CENTER_ALERT_NOTIFY_LOCK);
        int num = errorNum.intValue();
        log.info("IDomainRepositoryImpl sendDataCenterDingTalkAlertMsg num [{}]:",num);
        //在5分钟范围内，控制出现告警频率以及噪音。若设置redis过期时间失败，则需要手动清除redis 缓存key
        if (num <= CorrectionConstant.NUM_ONE) {
            Boolean expireFlag = kvRepository.expire(CorrectionConstant.DATA_CENTER_ALERT_NOTIFY_LOCK, CorrectionConstant.NUM_FiVE_LONG, TimeUnit.MINUTES);
            this.sendCacheDingTalkAlertMsg(expireFlag);
        }
        //控制噪音，若次数大于5则不告警
        if (num <= CorrectionConstant.NUM_FIVE) {
            bizAsyncExecutor.execute(() -> dingTalkRepository.sendDingTalkMsg(dingTalkMessageInfo));
        }
    }

    private DingTalkMessageInfo buildDataCenterDingTalkMsg(DCVoucherType voucherType, String orderId, String msg) {
        String formatMsg = String.format(CorrectionConstant.DATA_CENTER_ERROR_MSG, voucherType.getDcType(), orderId, msg);
        DingTalkMessageInfo dingTalkMessageInfo = new DingTalkMessageInfo();
        dingTalkMessageInfo.setMsg(formatMsg);
        dingTalkMessageInfo.setToken(globalConfigProperties.getDataCenterAlertToken());
        dingTalkMessageInfo.setMsg(formatMsg);
        dingTalkMessageInfo.setTitle(CorrectionConstant.DATA_CENTER_ALERT_TITLE);
        dingTalkMessageInfo.setAtMobiles(ObjectsUtils.strToList(globalConfigProperties.getDataCenterErrorPhones(), Symbols.COMMA));
        return dingTalkMessageInfo;
    }

    private void sendCacheDingTalkAlertMsg(Boolean flag) {
        if (BooleanUtils.isFalse(flag)){
            // TODO: 2023/11/17 yesonglin 如果更新缓存失败，需要告警出来，及时解决
        }
    }

    @Override
    public List<String> queryDataCenterOrderNo(String templateId, String fieldName) {
        List<Map<String, Object>> maps = dataCenterRepository.queryDoris(templateId, null);
        List<String> orderNoList = maps.stream().map(data -> (String) data.get(fieldName)).collect(Collectors.toList());

        return orderNoList;
    }
}
