package com.payermax.operating.correction.domainservice.vo;

import com.payermax.common.lang.util.AssertUtil;
import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.dto.MerchantInfo;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.*;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.ExtendInfoDTO;
import com.payermax.operating.correction.domain.dto.ExtraUserInfoDTO;
import com.payermax.operating.correction.domain.dto.RetryInfo;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domain.enums.SourceEnum;
import com.payermax.operating.correction.integration.config.nacos.model.CorrectionSystemInfo;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyProcessorInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.ReconcileRedundantDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.utils.ListSizeUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 差错核心对象信息
 * @date 2022/9/28
 */
@Data
@NoArgsConstructor
public class DomainCorrectionInfo {
    /**
     * 差错唯一单号
     */
    private String correctionNo;

    /**
     * 订单凭证号
     */
    private String voucherNo;

    /**
     * 凭证类型
     */
    private DCVoucherType voucherType;

    /**
     * 原始凭证信息
     */
    private VoucherInfo oriVoucherInfo;

    /**
     * 响应凭证信息
     */
    private VoucherInfo resVoucherInfo;

    /**
     * 交易类型
     */
    private TradeType tradeType;

    /**
     * 商户信息
     */
    private MerchantInfo merchantInfo;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 来源
     */
    private SourceEnum source;

    /**
     * 系统来源
     */
    private String sysSource;

    /**
     * 支付总金额
     */
    private Money payTotalMoney;

    /**
     * 处理状态
     */
    private ProcessStatusEnum processStatus;

    /**
     * 处理策略
     */
    private StrategyProcessorInfo strategyProcessor;

    /**
     * 差错原因基础信息
     */
    private CorrectionBasicInfoDTO basicInfoDTO;

    /**
     * 差错原因基础信息
     */
    private CorrectionBasicInfoDTO operationBasic;

    /**
     * 差错系统配置
     */
    private List<CorrectionSystemInfo> systemInfo;

    /**
     * 推入详情描述
     */
    private String detailDesc;

    /**
     * 推入详情描述
     */
    private String detailCode;

    /**
     * 原交易冗余信息
     */
    private ReconcileRedundantDTO redundantInfo;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String memo;

    /**
     * 批语
     */
    private String approvalCommit;

    /**
     * 扩展信息
     */
    private List<ExtendInfoDTO> extendInfoList;

    /**
     * 用户敏感信息
     */
    private List<ExtraUserInfoDTO> extraUserInfoList;

    /**
     * 当前处理记录集合
     */
    private Map<String, CorrectionOrderHandlerRecordDTO> handlerRecordMap;

    /**
     * 当前处理记录
     */
    private CorrectionOrderHandlerRecordDTO currentHandlerRecord;

    /**
     * 全局订单信息
     */
    private GlobalOrderInfo globalOrderInfo;

    /**
     * 重试次数
     */
    private RetryInfo retryInfo;

    /**
     * 复核人
     */
    private String reviewer;

    /**
     * 差错处理防御逻辑
     */
    public void handlerDefence() {
        AssertUtil.isTrue(processStatus==ProcessStatusEnum.PENDING,ReturnCode.BUSINESS_EXCEPTION.getCode(),ReturnMsg.CORRECTION_STATUS_MATCH_FAIL);
        AssertUtil.notNull(strategyProcessor, ReturnCode.CONFIG_ERROR.getCode(), ReturnMsg.HANDLER_STRATEGY_INVALID);
    }

    /**
     * 获取扩展用户信息
     */
    public ExtraUserInfoDTO getExtraUserInfo(UserInfoEnum userEnum) {
        if (CollectionUtils.isEmpty(extraUserInfoList)) {
            return Nullable.getNullVal();
        }
        List<ExtraUserInfoDTO> list = extraUserInfoList.stream().filter(e -> userEnum == e.getUserEnum()).collect(Collectors.toList());
        ListSizeUtils.log(list.stream().filter(e -> e.getSeqNo().equals(list.size())),"matchGetExtraUserInfo");
        return list.stream().filter(e -> e.getSeqNo().equals(list.size())).findFirst().orElse(Nullable.getNullVal());//CHECKED 根据请求数匹配 匹配出来必然只有一个
    }

    public ExtendInfoDTO getExtendInfo(ExtendEnum extendEnum) {
        if (CollectionUtils.isEmpty(extendInfoList)) {
            return Nullable.getNullVal();
        }
        List<ExtendInfoDTO> list = extendInfoList.stream().filter(e -> extendEnum == e.getOperation()).collect(Collectors.toList());
        ListSizeUtils.log(list.stream().filter(e -> e.getSeqNo().equals(list.size())),"matchGetExtraUserInfo");
        return list.stream().filter(e -> e.getSeqNo().equals(list.size())).findFirst().orElse(Nullable.getNullVal());//CHECKED 根据请求数匹配 匹配出来必然只有一个
    }

    /**
     * 丰富商户基本信息
     * @param bizType
     */
    public void richMerchantInfo(String bizType){
        if(Objects.isNull(this.merchantInfo)){
            return;
        }
        merchantInfo.setBizType(bizType);
    }

}
