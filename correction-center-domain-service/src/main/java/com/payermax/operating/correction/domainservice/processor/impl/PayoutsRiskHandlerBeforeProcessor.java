package com.payermax.operating.correction.domainservice.processor.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.enums.UserInfoEnum;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.ExtraUserInfoDTO;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.utils.CommonDomainUtil;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.risk.dto.RiskEventInfo;
import com.payermax.operating.correction.integration.rpc.risk.enums.RiskType;
import com.payermax.operating.correction.integration.rpc.risk.repository.RiskRepository;
import com.payermax.operating.correction.integration.rpc.security.repository.SecurityRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 差错通知渠道失败
 * @date 2022/12/1
 */
@Component(value = "payoutsRiskHandlerBefore")
@Slf4j
public class PayoutsRiskHandlerBeforeProcessor implements Processor {

    @Resource
    private SecurityRepository securityRepository;

    @Resource
    private RiskRepository riskRepository;

    @Resource
    private IDomainRepository domainRepository;

    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {

        ExtraUserInfoDTO extraUserInfoDTO = correctionInfo.getExtraUserInfo(UserInfoEnum.PAYOUTS);
        String json = securityRepository.decrypt(extraUserInfoDTO.getToken());
        JSONObject jsonObject = JSONObject.parseObject(json);
        //1. 风控校验
        RiskEventInfo riskEventInfo = new RiskEventInfo();
        riskEventInfo.setRiskType(RiskType.REFUND_TO_PAYOUTS_EVENT_BEFORE);
        riskEventInfo.setRequestId(domainRepository.getValidCorrectionNo(correctionInfo.getCorrectionNo(), correctionInfo.getBasicInfoDTO().getCorrectionCode(),correctionInfo.getPayTotalMoney(),jsonObject.getString("payeeAccount")));
        riskEventInfo.setEventBody(jsonObject);
        CommonDomainUtil.buildEventBody(riskEventInfo, correctionInfo.getGlobalOrderInfo(), correctionInfo.getPayTotalMoney(), extraUserInfoDTO, Nullable.getNullVal());
        Boolean ret = riskRepository.riskEventReport(riskEventInfo);
        return new BaseResProcess(new VoucherInfo(riskEventInfo.getRequestId(), DCVoucherType.ASSET_PAY_ORDER), ret.toString());
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo globalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        String processResult = correctionHandlerRecordInfo.getProcessResult();
        BaseResProcess resProcess = JSONObject.parseObject(processResult, new TypeReference<BaseResProcess>() {
        });
        return BooleanUtils.toBoolean(resProcess.getResult()) ? CommonStatusEnum.SUCCESS : CommonStatusEnum.FAILURE;
    }

}
