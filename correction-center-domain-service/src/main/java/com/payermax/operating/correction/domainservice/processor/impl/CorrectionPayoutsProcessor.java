package com.payermax.operating.correction.domainservice.processor.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.enums.*;
import com.payermax.operating.correction.domain.dto.ExtraUserInfoDTO;
import com.payermax.operating.correction.domain.enums.RiskStatusEnum;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.processor.utils.OrderStatusUtils;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.utils.CommonDomainUtil;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.dto.CorrectionPayoutsInfo;
import com.payermax.operating.correction.integration.dto.PayPaymentInstanceInfoDTO;
import com.payermax.operating.correction.integration.enums.AssetPayOrderStatus;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.rpc.asset.repository.AssetRepository;
import com.payermax.operating.correction.integration.rpc.cashiercore.dto.CashierCorePaymentInstanceInfo;
import com.payermax.operating.correction.integration.rpc.cashiercore.repository.CashierCoreRepository;
import com.payermax.operating.correction.integration.rpc.contextcenter.repository.ContextCenterRepository;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.risk.dto.RiskEventInfo;
import com.payermax.operating.correction.integration.rpc.risk.enums.RiskType;
import com.payermax.operating.correction.integration.rpc.risk.repository.RiskRepository;
import com.payermax.operating.correction.integration.rpc.security.repository.SecurityRepository;
import com.payermax.operating.correction.integration.utils.ListSizeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 解冻并扣除处理器
 * @date 2022/10/17
 */
@Component(value = CorrectionConstant.CORRECTION_PAYOUTS_PROCESSOR)
@Slf4j
public class CorrectionPayoutsProcessor implements Processor {

    @Resource
    private AssetRepository assetRepository;

    @Resource
    private ContextCenterRepository contextCenterRepository;

    @Resource
    private SecurityRepository securityRepository;

    @Resource
    private CashierCoreRepository cashierCoreRepository;

    @Resource
    private RiskRepository riskRepository;

    @Resource
    private IDomainRepository domainRepository;

    @Override
    public void preCheck(DomainCorrectionInfo correctionInfo) {
        //原支付订单校验
        GlobalOrderInfo globalOrderInfo = domainRepository.getGlobalOrderInfo(correctionInfo.getOriVoucherInfo(), GlobalOrderReadEvent.OPERATION);
        globalOrderInfo.tradeRefundInfoValidation();
        globalOrderInfo.channelCommitInfoValidation();

        ExtraUserInfoDTO extraUserInfo = correctionInfo.getExtraUserInfo(UserInfoEnum.PAYOUTS);
        AssertUtil.notNull(extraUserInfo, ReturnCode.PROCESSOR_VALID_EXCEPTION.getCode(), ReturnMsg.PAYOUTS_USER_INFO_ERROR);
    }

    @Override
    public void fillInProcessRequest(CorrectionOrderHandlerRecordDTO handlerRecordDTO, DomainCorrectionInfo correctionInfo) {
        handlerRecordDTO.getHandlerInfo().setProcessRequest(correctionInfo.getResVoucherInfo().getVoucherNo());
    }


    @Override
    public void finalResultPostProcessing(DomainCorrectionInfo correctionInfo, CommonStatusEnum commonStatus) {
        ExtraUserInfoDTO extraUserInfoDTO = correctionInfo.getExtraUserInfo(UserInfoEnum.PAYOUTS);
        String json = securityRepository.decrypt(extraUserInfoDTO.getToken());
        JSONObject jsonObject = JSONObject.parseObject(json);
        //1. 风控校验
        RiskEventInfo riskEventInfo = new RiskEventInfo();
        riskEventInfo.setRiskType(RiskType.REFUND_TO_PAYOUTS_EVENT_AFTER);
        riskEventInfo.setRequestId(correctionInfo.getResVoucherInfo().getVoucherNo());
        riskEventInfo.setEventBody(jsonObject);
        CommonDomainUtil.buildEventBody(riskEventInfo, correctionInfo.getGlobalOrderInfo(), correctionInfo.getPayTotalMoney(), extraUserInfoDTO, RiskStatusEnum.getByCommonStatus(commonStatus));
        if (BooleanUtils.isNotTrue(riskRepository.riskEventReport(riskEventInfo))) {
            log.warn("refund to payouts event after failed ,correctionNo:{}", correctionInfo.getCorrectionNo());
        }
    }

    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {
        GlobalOrderInfo globalOrderInfo = correctionInfo.getGlobalOrderInfo();
        //获取出款支付实例信息
        ExtraUserInfoDTO extraUserInfo = correctionInfo.getExtraUserInfo(UserInfoEnum.PAYOUTS);
        PayPaymentInstanceInfoDTO paymentInstanceInfoDTO = JSONObject.parseObject(extraUserInfo.getExtraInfo(), new TypeReference<PayPaymentInstanceInfoDTO>() {
        });
        //去调收银核心获取最新的出款支付实例信息
        paymentInstanceInfoDTO.getPaymentInstanceDTO().setBizIdentify(globalOrderInfo.getBizIdentify());
        CashierCorePaymentInstanceInfo cashierCorePaymentInstanceInfo = this.buildPaymentInstance(paymentInstanceInfoDTO);
        cashierCoreRepository.getPaymentInstance(cashierCorePaymentInstanceInfo, correctionInfo.getPayTotalMoney(), globalOrderInfo.getBizIdentify(), globalOrderInfo.getOriginalMerchantInfo().getMerchantNo());
        //将收银核心的内容重写到所需信息
        paymentInstanceInfoDTO.setPaymentInstanceDTO(cashierCorePaymentInstanceInfo.getPaymentInstanceList().get(0));//CHECKED 构建时只会有一个
        ListSizeUtils.log(cashierCorePaymentInstanceInfo.getPaymentInstanceList(),"matchGetPaymentInstanceList");
        //封装出款对象
        CorrectionPayoutsInfo payoutsInfo = CorrectionPayoutsInfo.builder()
                .bizIdentify(globalOrderInfo.getBizIdentify())
                .merchantNo(globalOrderInfo.getOriginalMerchantInfo().getMerchantNo())
                .payAmount(correctionInfo.getPayTotalMoney())
                .payPaymentInstanceInfo(paymentInstanceInfoDTO)
                .payoutNo(correctionInfo.getCurrentHandlerRecord().getHandlerInfo().getProcessRequest())
                .build();
        //1.保存出款所需上下文
        String decrypt = securityRepository.decrypt(extraUserInfo.getToken());
        contextCenterRepository.savePayoutContextCenter(payoutsInfo, JSON.parseObject(decrypt), correctionInfo.getCurrentHandlerRecord().getHandlerInfo().getProcessRequest());
        //2.发起差错出款
        return assetRepository.correctionPayouts(payoutsInfo);
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo globalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        String payOrderStatus = OrderStatusUtils.assetExchangePayOrderStatus(globalOrderInfo);
        return this.convertStatus(payOrderStatus);
    }

    private CashierCorePaymentInstanceInfo buildPaymentInstance(PayPaymentInstanceInfoDTO paymentInstanceInfoDTO) {
        CashierCorePaymentInstanceInfo cashierCorePaymentInstanceInfo = new CashierCorePaymentInstanceInfo();
        cashierCorePaymentInstanceInfo.setPaymentType(CorrectionConstant.PAYOUTS_PAYMENT_METHODS);
        cashierCorePaymentInstanceInfo.setCountry(paymentInstanceInfoDTO.getCountry());
        cashierCorePaymentInstanceInfo.setPaymentInstanceList(Lists.newArrayList(paymentInstanceInfoDTO.getPaymentInstanceDTO()));
        return cashierCorePaymentInstanceInfo;
    }

    private CommonStatusEnum convertStatus(String payOrderStatus) {
        AssetPayOrderStatus orderStatus = AssetPayOrderStatus.getByVal(payOrderStatus);
        switch (orderStatus) {
            case FAILED:
                return CommonStatusEnum.FAILURE;
            case SUCCESS:
                return CommonStatusEnum.SUCCESS;
            default:
                return CommonStatusEnum.PENDING;
        }
    }
}
