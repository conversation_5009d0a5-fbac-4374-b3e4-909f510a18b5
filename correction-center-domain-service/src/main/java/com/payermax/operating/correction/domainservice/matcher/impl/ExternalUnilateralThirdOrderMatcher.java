package com.payermax.operating.correction.domainservice.matcher.impl;

import com.google.common.collect.Lists;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.AutoHandleCorrectionOrderDTO;
import com.payermax.operating.correction.domain.dto.CorrectionAutoStrategyCodeHandlerDTO;
import com.payermax.operating.correction.domainservice.matcher.AbstractMatcher;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyExpressionInfo;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyRule;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.ReconcileRedundantDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 外部单边匹配器
 * @date 2023/8/30
 */
@Slf4j
@Component(value = CorrectionConstant.EXTERNAL_UNILATERAL_THIRD_BEAN)
public class ExternalUnilateralThirdOrderMatcher extends AbstractMatcher {

    @Resource
    private IDomainRepository domainRepository;

    @Override
    public List<CorrectionAutoStrategyCodeHandlerDTO> matchAutoHandlerStrategyInfo(StrategyRule strategyRule, AutoHandleCorrectionOrderDTO autoHandleCorrectionOrderDTO, GlobalOrderInfo globalOrderInfo) {
        List<CorrectionAutoStrategyCodeHandlerDTO> list = Lists.newArrayList();
        List<StrategyExpressionInfo> strategyExpressionInfoList = strategyRule.getStrategyExpressions().get(CorrectionConstant.DEFAULT);
        //如果没配置规则，或者匹配规则不满足，则返回空
        if(CollectionUtils.isEmpty(strategyExpressionInfoList)){
            return Nullable.getNullVal();
        }
        if (strategyExpressionInfoList.size() > 1) {
            return Nullable.getNullVal();
        }
        //定制化处理器，并不支持多个维度策略的配置，这里只会配置一个策略，因此可以直接取第一个
        StrategyExpressionInfo strategyExpressionInfo = strategyExpressionInfoList.get(0); //CHECKED
        if (!super.strategyMatch(strategyExpressionInfo.getExps(), autoHandleCorrectionOrderDTO)) {
            return Nullable.getNullVal();
        }

        CorrectionOrderInfoDTO checkingOrderInfo = domainRepository.getValidCorrectionOrderInfoDto(autoHandleCorrectionOrderDTO.getCorrectionNo());
        ReconcileRedundantDTO checkingChannelRedundantInfo = checkingOrderInfo.getOriginalTradeRedundantInfo();
        //查询提交单号
        List<CorrectionOrderInfoDTO> verifiedCommitOrderInfo = domainRepository.getCorrectionOrderInfos(strategyExpressionInfo.getMappingCorrectionCode(), checkingOrderInfo, checkingChannelRedundantInfo, DCVoucherType.THIRD_ORDER_NO);
        log.info("ExternalUnilateralThirdOrderMatcher matchAutoHandlerStrategyInfo [voucherType:[THIRD_ORDER_NO], checkingChannelRedundantInfo:[{}]:",checkingChannelRedundantInfo.queryThirdOrderNoStr());

        CorrectionBasicInfoDTO basicInfo = domainRepository.getValidBasicInfo(autoHandleCorrectionOrderDTO.getCorrectionCode(), autoHandleCorrectionOrderDTO.getTradeType());
        if (CollectionUtils.isNotEmpty(verifiedCommitOrderInfo) && Objects.nonNull(basicInfo)) {
            //包装匹配上的数据
            super.wrapAutoHandleCorrectionInfoList(list, verifiedCommitOrderInfo, strategyExpressionInfo);
            //将自己也添加为自动处理对象
            checkingOrderInfo.setOperationCorrectionCode(basicInfo.getCorrectionCode());
            super.addSelfCorrectionOrderInfo(list, checkingOrderInfo, strategyExpressionInfo);
        }
        return list;
    }
}
