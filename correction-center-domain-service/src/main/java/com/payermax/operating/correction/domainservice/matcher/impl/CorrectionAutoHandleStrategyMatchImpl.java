package com.payermax.operating.correction.domainservice.matcher.impl;

import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.AutoHandleCorrectionOrderDTO;
import com.payermax.operating.correction.domain.dto.CorrectionAutoStrategyCodeHandlerDTO;
import com.payermax.operating.correction.domainservice.matcher.CorrectionAutoHandleStrategyMatch;
import com.payermax.operating.correction.domainservice.matcher.StrategyHolder;
import com.payermax.operating.correction.integration.config.nacos.constant.NacosGlobalConfigProperties;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyRule;
import com.payermax.operating.correction.integration.config.nacos.repository.ConfigRepository;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 差错自动处理策略匹配实现器
 * @date 2023/4/20
 */
@Slf4j
@Component
public class CorrectionAutoHandleStrategyMatchImpl implements CorrectionAutoHandleStrategyMatch {

    @Resource
    private ConfigRepository configRepository;

    @Resource
    private NacosGlobalConfigProperties globalConfigProperties;

    @Resource
    private StrategyHolder strategyHolder;

    @Override
    public List<CorrectionAutoStrategyCodeHandlerDTO> strategyMatchRule(GlobalOrderInfo globalOrderInfo, AutoHandleCorrectionOrderDTO autoHandleCorrectionOrderDTO) {
        //1.先查看黑名单 击中则返还null
        if (isHitOutTradeNoOrMerchantNoBlank(autoHandleCorrectionOrderDTO)) {
            return Nullable.getNullVal();
        }
        //2.查看是否存在动态配置规则
        StrategyRule strategyRule = configRepository.getStrategyInfo(autoHandleCorrectionOrderDTO.getCorrectionCode(), autoHandleCorrectionOrderDTO.getTradeType());
        if (Objects.isNull(strategyRule)) {
            return Nullable.getNullVal();
        }
        return strategyHolder.getProcess(strategyRule.getStrategyPlan(), strategyRule.getBeanInstance())
                .matchAutoHandlerStrategyInfo(strategyRule, autoHandleCorrectionOrderDTO, globalOrderInfo);
    }

    /**
     * 命中商户订单号 或者 商户号 黑名单
     * @param autoHandleCorrectionOrderDTO
     * @return
     */
    public boolean isHitOutTradeNoOrMerchantNoBlank( AutoHandleCorrectionOrderDTO autoHandleCorrectionOrderDTO){
        if(StringUtils.isNotBlank(autoHandleCorrectionOrderDTO.getMerchantNo())
                && globalConfigProperties.isHitMerchantNoBlackList(autoHandleCorrectionOrderDTO.getMerchantNo())){
            return true;
        }
        return StringUtils.isNotBlank(autoHandleCorrectionOrderDTO.getMerchantOrderNo())
                && globalConfigProperties.getMerchantOrderNoBlackList().contains(autoHandleCorrectionOrderDTO.getMerchantOrderNo());
    }
}
