package com.payermax.operating.correction.domainservice.processor.utils;

import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.integration.enums.TradeOrderStatus;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;

import java.util.Optional;

/**
 * <AUTHOR>
 * @desc 订单状态工具类
 * @date 2023/4/18
 */
public class OrderStatusUtils {

    /**
     * 订单中心交易单状态
     */
    public static String orderCenterTradeStatus(GlobalOrderInfo globalOrderInfo) {
        return Optional.ofNullable(globalOrderInfo)
                .map(GlobalOrderInfo::getTradeInfo)
                .map(GlobalOrderInfo.TradeInfo::getTradeOrder)
                .map(GlobalOrderInfo.OrderInfo::getStatus)
                .orElse(TradeOrderStatus.INIT.getCode());
    }

    /**
     * 订单中心支付单状态
     */
    public static String orderCenterPayOrderStatus(GlobalOrderInfo globalOrderInfo) {
        return Optional.ofNullable(globalOrderInfo)
                .map(GlobalOrderInfo::getTradeInfo)
                .map(GlobalOrderInfo.TradeInfo::getPayRequest)
                .map(GlobalOrderInfo.OrderInfo::getStatus)
                .orElse(Nullable.getNullVal());
    }

    /**
     *资产交换支付状态
     */
    public static String assetExchangePayOrderStatus(GlobalOrderInfo globalOrderInfo) {
        return Optional.ofNullable(globalOrderInfo)
                .map(GlobalOrderInfo::getAssetInfo)
                .map(GlobalOrderInfo.AssetInfo::getAssetOrder)
                .map(GlobalOrderInfo.OrderInfo::getStatus)
                .orElse(Nullable.getNullVal());
    }

    /**
     *金融交换提交单状态
     */
    public static String channelCommitStatus(GlobalOrderInfo globalOrderInfo) {
        return Optional.ofNullable(globalOrderInfo)
                .map(GlobalOrderInfo::getChannelInfo)
                .map(GlobalOrderInfo.ChannelInfo::getLastChannelCommit)
                .map(GlobalOrderInfo.ChannelCommitInfo::getCommitInfo)
                .map(GlobalOrderInfo.OrderInfo::getStatus)
                .orElse(Nullable.getNullVal());
    }

    public static String channelRequestStatus(GlobalOrderInfo globalOrderInfo) {
        return Optional.ofNullable(globalOrderInfo)
                .map(GlobalOrderInfo::getChannelInfo)
                .map(GlobalOrderInfo.ChannelInfo::getChannelRequest)
                .map(GlobalOrderInfo.OrderInfo::getStatus)
                .orElse(Nullable.getNullVal());
    }
}
