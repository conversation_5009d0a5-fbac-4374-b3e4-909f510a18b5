package com.payermax.operating.correction.domainservice.checker;

import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;

/**
 * The interface Checker.
 *
 * <AUTHOR>
 * @desc 校验器
 * @date 2022 /10/20
 */
public interface Checker {

    /**
     * 业务校验器名称后缀
     */
    String CHECKER_SUFFIX = "CHECKER";

    /**
     * 业务校验器,主要check当前业务是否准入
     *
     * @param correctionInfo  当前业务场景支付业务单
     * @param globalOrderInfo the global order info
     */
    void check(CorrectionOrderInfoDTO correctionInfo, GlobalOrderInfo globalOrderInfo, OperationDomainCorrectionInfo info);
}
