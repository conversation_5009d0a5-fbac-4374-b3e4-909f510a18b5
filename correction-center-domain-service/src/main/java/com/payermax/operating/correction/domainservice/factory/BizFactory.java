package com.payermax.operating.correction.domainservice.factory;

import com.payermax.operating.correction.core.common.enums.GlobalOrderReadEvent;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;

/**
 * The interface Biz factory.
 *
 * <AUTHOR>
 * @desc 业务加工工厂
 * @date 2022 /10/18
 */
public interface BizFactory {

    /**
     * Add correction event prepare factory correction info vo.
     *
     * @param correctionInfo the correction info
     * @return the correction info vo
     */
    DomainCorrectionInfo addCorrectionEventPrepareFactory(OperationDomainCorrectionInfo correctionInfo);

    /**
     * Prepare correction info factory correction info vo.
     *
     * @param correctionNo the correction no
     * @return the correction info vo
     */
    DomainCorrectionInfo prepareCorrectionInfoFactory(String correctionNo,GlobalOrderReadEvent readEvent);
}
