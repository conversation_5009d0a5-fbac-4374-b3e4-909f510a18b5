package com.payermax.operating.correction.domainservice.processor.impl;

import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.UserInfoEnum;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.processor.utils.OrderStatusUtils;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.enums.ChannelOrderStatus;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.financial.dto.ChannelResultNotifyInfo;
import com.payermax.operating.correction.integration.rpc.financial.enums.ChannelNotifyType;
import com.payermax.operating.correction.integration.rpc.financial.repository.ChannelExchangeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 差错通知渠道失败
 * @date 2022/12/1
 */
@Component(value = "institutionOfflineRefundSuccessProcessor")
@Slf4j
public class InstitutionOfflineRefundSuccessProcessor implements Processor {

    @Resource
    private ChannelExchangeRepository channelExchangeRepository;

    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {
        String thirdOrderNo = correctionInfo.getExtraUserInfo(UserInfoEnum.INSTITUTION_REFUND).getExtraInfo();
        ChannelResultNotifyInfo resultNotifyInfo = ChannelResultNotifyInfo.builder()
                .channelCommitNo(correctionInfo.getOriVoucherInfo().getVoucherNo())
                .notifyType(ChannelNotifyType.REFUND)
                .processStatus(CommonStatusEnum.SUCCESS)
                .payAmount(correctionInfo.getPayTotalMoney())
                .thirdOrderNo(thirdOrderNo)
                .fourthOrderNo(correctionInfo.getCorrectionNo())
                .handleType(CorrectionConstant.INSTITUTION_OFFLINE_REFUND)
                .build();
        return channelExchangeRepository.financialResultNotify(resultNotifyInfo);
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo globalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        return ChannelOrderStatus.SUCCESS == ChannelOrderStatus.getChannelByVal(OrderStatusUtils.channelCommitStatus(globalOrderInfo)) ? CommonStatusEnum.SUCCESS : CommonStatusEnum.PENDING;
    }


}
