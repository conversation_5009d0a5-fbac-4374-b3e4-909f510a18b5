package com.payermax.operating.correction.domainservice.processor;

import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.*;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.enums.CorrectionEvent;
import com.payermax.operating.correction.domainservice.machine.CorrectionOrderStateMachine;
import com.payermax.operating.correction.domainservice.machine.context.CorrectionOrderStateMachineContext;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.utils.ProcessorBeanUtils;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.config.nacos.constant.NacosGlobalConfigProperties;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyProcessorInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionHandlerUniInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionRepository;
import com.payermax.operating.correction.integration.queue.MqResultNotifyRepository;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * The type Abs processor.
 *
 * <AUTHOR>
 * @date 2022 /9/22
 */
@Component
@Slf4j
public class TemplateProcessor implements StrategyTemplateProcessor {
    @Resource
    private CorrectionRepository correctionRepository;

    @Resource
    private ProcessorHolder processorHolder;

    @Resource
    private MqResultNotifyRepository mqResultNotifyRepository;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private CorrectionOrderStateMachine correctionOrderStateMachine;

    @Resource
    private IDomainRepository domainRepository;

    @Resource
    private NacosGlobalConfigProperties globalConfigProperties;

    /**
     * doAction.
     */
    @Override
    public void doAction(DomainCorrectionInfo correctionInfo) {
        //规则校验
        correctionInfo.handlerDefence();
        try {
            List<StrategyProcessorInfo.Processor> processors = ObjectUtils.defaultIfNull(correctionInfo.getStrategyProcessor().getProcessors(), CorrectionConstant.NULL_LIST);
            for (StrategyProcessorInfo.Processor processor : processors) {
                Processor processHandler = processorHolder.getProcess(processor.getBeanName());
                AssertUtil.notNull(processHandler, ReturnCode.CONFIG_ERROR.getCode(), String.format(ReturnMsg.BEAN_IS_NOT_EXIST, processor.getBeanName()));
                //先获取是否存在成功的处理记录，存在则退出当次处理器
                CorrectionOrderHandlerRecordDTO sucHandlerRecord = Optional.ofNullable(correctionInfo.getHandlerRecordMap())
                        .map(e -> e.get(ProcessorBeanUtils.getBeanNameUniqueKey(processor.getBeanName(), CommonStatusEnum.SUCCESS)))
                        .orElse(Nullable.getNullVal());
                if (Objects.nonNull(sucHandlerRecord)) {
                    continue;
                }
                //判定是否存在处理中的记录
                CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo = Optional.ofNullable(correctionInfo.getHandlerRecordMap())
                        .map(e -> e.get(ProcessorBeanUtils.getBeanNameUniqueKey(processor.getBeanName(), CommonStatusEnum.PENDING)))
                        .orElseGet(() -> this.buildProcessorRecord(processHandler, correctionInfo, processor));
                correctionInfo.setCurrentHandlerRecord(correctionHandlerRecordInfo);
                //没有明确的结果响应，则发起执行处理
                if (StringUtils.isBlank(correctionHandlerRecordInfo.getProcessResult())) {
                    this.execProcess(processHandler, correctionInfo, processor, correctionHandlerRecordInfo);
                }
                //如果是Pending中，则根据配置规则去进行逻辑判断处理
                CommonStatusEnum handlerResult = processHandler.handlerResult(this.consult(correctionInfo, processor), correctionHandlerRecordInfo);
                if (CommonStatusEnum.PENDING == handlerResult) {
                    //如果为处理中，则往延时队列里面扔,并终止当前处理流程，等到下次收到消息唤醒
                    mqResultNotifyRepository.delayHandlerNotify(correctionHandlerRecordInfo.getHandlerInfo().getCorrectionNo(), correctionInfo.getRetryInfo().getHandleRetry());
                    return;
                } else if (CommonStatusEnum.FAILURE == handlerResult) {
                    processHandler.finalResultPostProcessing(correctionInfo, handlerResult);
                    this.handlerFailedEvent(correctionHandlerRecordInfo, correctionInfo);
                    return;
                } else if (CommonStatusEnum.SUCCESS == handlerResult && correctionHandlerRecordInfo.getStatus() == CommonStatusEnum.PENDING) {
                    processHandler.finalResultPostProcessing(correctionInfo, handlerResult);
                    this.handlerSucEvent(correctionHandlerRecordInfo);
                }
            }
            //执行完for循环，则表示所有子流程都已完成
            correctionOrderStateMachine.sendEvent(correctionInfo.getProcessStatus(), CorrectionEvent.HANDLER_SUCCESS, new CorrectionOrderStateMachineContext(correctionInfo));
        } catch (Exception e) {
            log.error("templateProcessor execAction operation base system exception:", e);
        }
    }

    private GlobalOrderInfo consult(DomainCorrectionInfo correctionInfo, StrategyProcessorInfo.Processor processor) {
        //默认取请求凭证信息
        VoucherInfo voucherInfo = HandlerType.RESULT == processor.getHandler() ? correctionInfo.getResVoucherInfo() : correctionInfo.getOriVoucherInfo();
        return domainRepository.getGlobalOrderInfo(voucherInfo, GlobalOrderReadEvent.OPERATION);
    }

    private CorrectionOrderHandlerRecordDTO buildHandlerRecordInfo(DomainCorrectionInfo correctionInfo, StrategyProcessorInfo.Processor processor) {
        CorrectionOrderHandlerRecordDTO handlerRecordInfo = new CorrectionOrderHandlerRecordDTO();
        handlerRecordInfo.setHandlerInfo(new CorrectionHandlerUniInfo(correctionInfo.getCorrectionNo(), correctionInfo.getStrategyProcessor().getStrategyCode(), processor.getBeanName()));
        handlerRecordInfo.setStatus(CommonStatusEnum.PENDING);
        handlerRecordInfo.setOpBasicInfo(new OperationBasicInfo(Symbols.SYS_OPERATION));
        return handlerRecordInfo;
    }

    /**
     * 构建Processor
     */
    private CorrectionOrderHandlerRecordDTO buildProcessorRecord(Processor processHandler, DomainCorrectionInfo correctionInfo, StrategyProcessorInfo.Processor processor) {
        //前置校验
        if (globalConfigProperties.getValidationSwitch()) {
            processHandler.preCheck(correctionInfo);
        }
        //构建结果
        CorrectionOrderHandlerRecordDTO handlerRecordDTO = this.buildHandlerRecordInfo(correctionInfo, processor);
        processHandler.fillInProcessRequest(handlerRecordDTO, correctionInfo);
        correctionRepository.storeOrderHandlerRecordInfo(handlerRecordDTO);
        correctionInfo.getHandlerRecordMap().put(ProcessorBeanUtils.getBeanNameUniqueKey(processor.getBeanName(), CommonStatusEnum.PENDING), handlerRecordDTO);
        return handlerRecordDTO;
    }

    private void execProcess(Processor processHandler, DomainCorrectionInfo correctionInfo, StrategyProcessorInfo.Processor processor, CorrectionOrderHandlerRecordDTO handlerRecordDTO) {

        //事务提交
        Boolean result = transactionTemplate.execute(status -> {

            //执行子类处理逻辑（业务方接入时，需要支持幂等）
            BaseResProcess baseRes = processHandler.exec(correctionInfo);
            if (Objects.isNull(baseRes)) {
                return Boolean.TRUE;
            }
            //更新当次的处理结果
            correctionRepository.storeOrderHandlerRecordResultInfo(handlerRecordDTO.getHandlerInfo(), baseRes);

            //更新差错记录
            if (HandlerType.RESULT == processor.getHandler()) {
                //更新响应
                correctionRepository.storeCorrectionResVoucherInfo(correctionInfo.getCorrectionNo(), baseRes.getResVoucherInfo());
                correctionInfo.setResVoucherInfo(baseRes.getResVoucherInfo());
            }
            return Boolean.TRUE;
        });
        AssertUtil.isTrue(result, ReturnCode.SYS_EXCEPTION.getCode(), ReturnMsg.OPERATION_DATA_EXCEPTION);
    }

    private void handlerSucEvent(CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        //如果处理成功，且db钟处理器结果为Pending 则更新结果
        boolean ret = correctionRepository.restoreHandlerStatus(correctionHandlerRecordInfo.getHandlerInfo(), CommonStatusEnum.PENDING.name(),
                CommonStatusEnum.SUCCESS.name());
        AssertUtil.isTrue(ret, ReturnCode.STATE_MACHINE_PROCESS_FAIL.getCode(), ReturnCode.STATE_MACHINE_PROCESS_FAIL.getMsg());
    }

    private void handlerFailedEvent(CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo, DomainCorrectionInfo correctionInfo) {
        transactionTemplate.execute(status -> {
            //如果处理成功，且db钟处理器结果为Pending 则更新结果
            boolean ret = correctionRepository.restoreHandlerStatus(correctionHandlerRecordInfo.getHandlerInfo(), CommonStatusEnum.PENDING.name(),
                    CommonStatusEnum.FAILURE.name());
            AssertUtil.isTrue(ret, ReturnCode.STATE_MACHINE_PROCESS_FAIL.getCode(), ReturnCode.STATE_MACHINE_PROCESS_FAIL.getMsg());
            correctionOrderStateMachine.sendEvent(correctionInfo.getProcessStatus(), CorrectionEvent.HANDLER_FAILED, new CorrectionOrderStateMachineContext(correctionInfo));
            return Boolean.TRUE;
        });
    }
}
