package com.payermax.operating.correction.domainservice.processor.impl;


import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.OrderDetailType;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.processor.utils.OrderStatusUtils;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.enums.ChannelOrderStatus;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionRepository;
import com.payermax.operating.correction.integration.queue.MqResultNotifyRepository;
import com.payermax.operating.correction.integration.queue.dto.ResultNotifyDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 解冻并扣除处理器
 * @date 2022/10/17
 */
@Component(value = CorrectionConstant.ORIGINAL_CHANNEL_REFUND_RETRY_PROCESSOR)
@Slf4j
public class OriginalChannelRefundRetryProcessor implements Processor {

    @Resource
    private CorrectionRepository correctionRepository;

    @Resource
    private MqResultNotifyRepository mqResultNotifyRepository;

    @Override
    public void preCheck(DomainCorrectionInfo correctionInfo) {
        //渠道请求单信息必须是请求中
        String channelRequestStatus = OrderStatusUtils.channelRequestStatus(correctionInfo.getGlobalOrderInfo());
        AssertUtil.isTrue(ChannelOrderStatus.getChannelByVal(channelRequestStatus) == ChannelOrderStatus.PENDING, ReturnCode.PROCESSOR_VALID_EXCEPTION.getCode(), ReturnMsg.CHANNEL_REQUEST_STATUS_MATCH_FAIL);
    }

    @Override
    public void fillInProcessRequest(CorrectionOrderHandlerRecordDTO handlerRecordDTO, DomainCorrectionInfo correctionInfo) {
        List<CorrectionOrderHandlerRecordDTO> recordList = correctionRepository.loadCorrectionRecordList(handlerRecordDTO.getHandlerInfo(), OrderDetailType.STRATEGY_QUERY);
        handlerRecordDTO.getHandlerInfo().setProcessRequest(String.valueOf(recordList.size() + 1));
    }

    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {
        ResultNotifyDTO.ResultNotify resultNotify = ResultNotifyDTO.ResultNotify.builder()
                .status(CommonStatusEnum.PENDING.name())
                .voucherNo(correctionInfo.getOriVoucherInfo().getVoucherNo())
                .voucherType(correctionInfo.getOriVoucherInfo().getVoucherType().name())
                .correctionNo(correctionInfo.getCorrectionNo())
                .strategyProcess(CorrectionConstant.ORIGINAL_CHANNEL_RETRY)
                .tag(CorrectionConstant.TOPIC_TAG_CHANNEL)
                .build();
        mqResultNotifyRepository.handlerResultNotify(new ResultNotifyDTO(resultNotify));
        return new BaseResProcess(correctionInfo.getOriVoucherInfo(), Nullable.getNullVal());
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo globalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        return convertCommonStatus(OrderStatusUtils.channelRequestStatus(globalOrderInfo));
    }

    private CommonStatusEnum convertCommonStatus(String status) {
        ChannelOrderStatus orderStatus = ChannelOrderStatus.getChannelByVal(status);
        if (ChannelOrderStatus.SUCCESS == orderStatus || ChannelOrderStatus.FAILED == orderStatus) {
            return CommonStatusEnum.SUCCESS;
        } else {
            return CommonStatusEnum.PENDING;
        }

    }

}
