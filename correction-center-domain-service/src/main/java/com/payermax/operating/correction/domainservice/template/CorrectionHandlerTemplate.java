package com.payermax.operating.correction.domainservice.template;

import com.payermax.operating.correction.core.common.dto.CorrectionBaseInfo;
import com.payermax.operating.correction.domain.dto.EventTransferInfo;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;

/**
 * The interface Correction handler template.
 *
 * <AUTHOR>
 * @desc 差错执行器模板
 * @date 2022 /10/18
 */
public interface CorrectionHandlerTemplate {

    /**
     * Add correction event.
     *
     * @param domainCorrectionInfo the correction info vo
     */
    void addCorrectionEvent(DomainCorrectionInfo domainCorrectionInfo);

    /**
     * Choose process strategy.
     *
     * @param infoDTO the correction info vo
     */
    void chooseProcessStrategy(OperationDomainCorrectionInfo infoDTO);

    /**
     * Event transfer.
     *
     * @param infoDTO the info dto
     */
    void eventTransfer(OperationDomainCorrectionInfo infoDTO);

    /**
     * Approval.
     *
     * @param infoDTO the info dto
     */
    void approval(OperationDomainCorrectionInfo infoDTO);

    /**
     * Exec strategy.
     *
     * @param orderInfo the correction no
     */
    void execStrategy(DomainCorrectionInfo orderInfo);

    /**
     * Query correction order info domain correction info.
     *
     * @param correctionBaseInfo the correction base info
     * @return the domain correction info
     */
    DomainCorrectionInfo queryCorrectionOrderInfo(CorrectionBaseInfo correctionBaseInfo);


    /**
     * Register handler.
     *
     * @param operationDomainCorrectionInfo the operation domain correction info
     */
    void registerHandler(OperationDomainCorrectionInfo operationDomainCorrectionInfo);
}
