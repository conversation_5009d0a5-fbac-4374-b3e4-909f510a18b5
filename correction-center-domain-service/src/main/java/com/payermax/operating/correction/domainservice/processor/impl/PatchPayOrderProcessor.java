package com.payermax.operating.correction.domainservice.processor.impl;

import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.integration.enums.PayOrderStatusEnum;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.processor.assembler.ProcessorAssembler;
import com.payermax.operating.correction.domainservice.processor.utils.OrderStatusUtils;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderResponse;
import com.payermax.operating.correction.integration.rpc.ordercenter.repository.OrderCenterRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 补支付
 * @date 2022/10/16
 */
@Component(value = "patchPayOrderProcessor")
@Slf4j
public class PatchPayOrderProcessor implements Processor {

    @Resource
    private OrderCenterRepository orderCenterRepository;

    @Resource
    private ProcessorAssembler processorAssembler;

    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {
        TradeOrderPatchOrderResponse response = orderCenterRepository.patchPayOrder(processorAssembler.toPatchPayOrder(correctionInfo));
        response.defence();
        return processorAssembler.toBaseResProcess(response);
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo globalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        PayOrderStatusEnum payOrderStatusEnum = PayOrderStatusEnum.getByVal(OrderStatusUtils.orderCenterPayOrderStatus(globalOrderInfo));
        return PayOrderStatusEnum.PAY_SUCCESS == payOrderStatusEnum ? CommonStatusEnum.SUCCESS : CommonStatusEnum.PENDING;
    }

}
