package com.payermax.operating.correction.domainservice.template.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.payermax.common.lang.exception.BusinessException;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.dto.CorrectionBaseInfo;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.dto.OperationManualFillIn;
import com.payermax.operating.correction.core.common.dto.ValidationConditionInfo;
import com.payermax.operating.correction.core.common.enums.*;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.ExtraUserInfoDTO;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domain.enums.CorrectionEvent;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domainservice.assembler.CorrectionDomainServiceAssembler;
import com.payermax.operating.correction.domainservice.checker.Checker;
import com.payermax.operating.correction.domainservice.checker.CheckerHolder;
import com.payermax.operating.correction.domainservice.factory.BizFactory;
import com.payermax.operating.correction.domainservice.machine.CorrectionOrderStateMachine;
import com.payermax.operating.correction.domainservice.machine.context.CorrectionOrderStateMachineContext;
import com.payermax.operating.correction.domainservice.matcher.AbstractMatcher;
import com.payermax.operating.correction.domainservice.processor.StrategyTemplateProcessor;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.service.DomainOperationService;
import com.payermax.operating.correction.domainservice.template.CorrectionHandlerTemplate;
import com.payermax.operating.correction.domainservice.utils.CommonDomainUtil;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.config.nacos.constant.NacosGlobalConfigProperties;
import com.payermax.operating.correction.integration.config.nacos.model.DingTalkNotifyMappingInfo;
import com.payermax.operating.correction.integration.config.nacos.model.Expression;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyProcessorInfo;
import com.payermax.operating.correction.integration.persistence.kvstore.repository.KVRepository;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionHandlerUniInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionRepository;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.dingtalk.dto.DingTalkMessageInfo;
import com.payermax.operating.correction.integration.rpc.dingtalk.repository.DingTalkRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @desc 差错处理模板
 * @date 2022/10/18
 */
@Component
@Slf4j
public class CorrectionHandlerTemplateImpl implements CorrectionHandlerTemplate {

    @Resource
    private CorrectionRepository correctionRepository;

    @Resource
    private CorrectionDomainServiceAssembler domainServiceAssembler;

    @Resource
    private IDomainRepository domainRepository;

    @Resource
    private StrategyTemplateProcessor templateProcessor;

    @Resource
    private BizFactory bizFactory;

    @Resource
    private CorrectionOrderStateMachine correctionOrderStateMachine;

    @Resource(name = "bizAsyncExecutor")
    private ThreadPoolTaskExecutor asyncExecutor;

    @Resource
    private CheckerHolder checkerHolder;

    @Resource
    private DomainOperationService domainOperationService;

    @Resource
    private NacosGlobalConfigProperties globalConfigProperties;

    @Resource
    private DingTalkRepository dingTalkRepository;

    @Resource
    private KVRepository kvRepository;

    @Resource(name = CorrectionConstant.CONFIG_STRATEGY_BEAN)
    private AbstractMatcher abstractMatcher;

    @Resource
    private NacosGlobalConfigProperties nacosGlobalConfigProperties;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addCorrectionEvent(DomainCorrectionInfo domainCorrectionInfo) {
        //step 1 添加差错单信息
        correctionRepository.storeCorrectionOrderInfo(domainServiceAssembler.toDBCorrectionOrderInfo(domainCorrectionInfo));

        //step 2 添加操作记录
        correctionRepository.storeOrderHandlerRecordInfo(this.buildHandlerRecord(domainCorrectionInfo.getCorrectionNo(),
                StringUtils.EMPTY, CorrectionEvent.PUSH, domainCorrectionInfo.getOperator(), CommonStatusEnum.SUCCESS));
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void chooseProcessStrategy(OperationDomainCorrectionInfo info) {
        //1.更新扩展信息
        CorrectionOrderInfoDTO orderInfoDTO = domainRepository.getValidCorrectionOrderInfoDto(info.getBaseInfo().getCorrectionNo());
        this.storeUserInfo(info);

        //2.差错code有效性校验
        this.validationCorrectionCode(info, orderInfoDTO);

        //3.策略规则校验
        this.validationStrategyCode(orderInfoDTO);

        //4.checker 校验器
        GlobalOrderInfo globalOrderInfo = null;
        Checker checker = this.getCheck(orderInfoDTO.getBaseInfo().getCorrectionCode());
        if (Objects.nonNull(checker)) {
            orderInfoDTO.setOperationCorrectionCode(info.getOperationCorrectionCode());
            globalOrderInfo = domainRepository.getGlobalOrderInfo(orderInfoDTO.getOriVoucherInfo(), GlobalOrderReadEvent.OPERATION);
            globalOrderInfo.tradeTypeValidation();
            checker.check(orderInfoDTO, globalOrderInfo,info);
        }
        //临时代码 多维度配置代码上线之后需要删除该配置
        if(Objects.isNull(globalOrderInfo)){
            globalOrderInfo = domainRepository.getGlobalOrderInfo(orderInfoDTO.getOriVoucherInfo(), GlobalOrderReadEvent.OPERATION);
        }

        // 在入款（包含退款）有营销则不能处理。另外对于可处理的订单保留一个白名单，在客诉时可处理。
        if (Objects.nonNull(globalOrderInfo.getTradeInfo())
                && Objects.nonNull(globalOrderInfo.getTradeInfo().getTradeOrder())
                && Objects.nonNull(globalOrderInfo.getTradeInfo().getPayRequest())
                && globalOrderInfo.getTradeInfo().getPayRequest().isHaveDiscount()) { // 只有入款(包含退款)会有该字段,可排除出款（营销在支付单维度)
            if (!nacosGlobalConfigProperties.getWithListHaveDiscountOrderNoList() // 不在白名单中则进行拦截报错
                    .contains(globalOrderInfo.getTradeInfo().getTradeOrder().getOrderNo())) {
                throw new BusinessException(ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.DO_NOT_HANDEL_HAVE_DISCOUNT_ORDER);
            }
        }

        if (isHitRedeliveryTradeFailBlack(globalOrderInfo,info)) {
            throw new BusinessException(ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.HANDLER_STRATEGY_BLACKLIST);
        }

        if(hitStrategyBlacklist(info.getStrategyCode(),globalOrderInfo)){
            throw new BusinessException(ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.HANDLER_STRATEGY_BLACKLIST);
        }
        //5.先更新策略
        CorrectionOrderInfoDTO saveOrderInfoDto = domainServiceAssembler.toDBCorrectionOrderInfo(info);
        correctionRepository.storeCorrectionStrategyInfo(saveOrderInfoDto);

        //6.若系统来源包含金融交换，则直接处理，无需审核,或者是由系统自动处理策略
        CorrectionEvent event = CorrectionEvent.EVENT_CHOOSE_AND_REVIEWED;
        if (orderInfoDTO.getSysSource().contains(CorrectionConstant.TOPIC_TAG_CHANNEL) || CorrectionConstant.SYSTEM_OPERATION.equals(info.getOperator())) {
            event = CorrectionEvent.EVENT_CHOOSE;
        }
        //7.更新状态
        DomainCorrectionInfo orderInfo = bizFactory.prepareCorrectionInfoFactory(info.getBaseInfo().getCorrectionNo(), GlobalOrderReadEvent.DISPLAY);
        correctionOrderStateMachine.sendEvent(ProcessStatusEnum.getByName(orderInfoDTO.getProcessStatus()), event, new CorrectionOrderStateMachineContext(orderInfo));

    }

    /**
     * 判断选择的策略是否命中黑名单配置
     * @param strategyCode
     * @param globalOrderInfo
     * @return
     */
    private boolean hitStrategyBlacklist(String strategyCode, GlobalOrderInfo globalOrderInfo) {
        try {
            ConcurrentHashMap<String, List<Expression>> strategyBlacklist = domainOperationService.getStrategyBlacklist();
            //策略code配置了拦截规则
            if (strategyBlacklist.containsKey(strategyCode)) {
                List<Expression> expressions = strategyBlacklist.get(strategyCode);
                return abstractMatcher.strategyMatch(expressions, globalOrderInfo);
            }
            return false;
        } catch (Exception e) {
            log.error("hitStrategyBlacklistHandleFailed strategyCode:{} globalOrderInfo:{}", strategyCode, JSONObject.toJSONString(globalOrderInfo));
            return false;
        }
    }

    public boolean isHitRedeliveryTradeFailBlack(GlobalOrderInfo globalOrderInfo,OperationDomainCorrectionInfo info){
        try {
           return Objects.nonNull(globalOrderInfo.getOriginalMerchantInfo())
                    && globalConfigProperties.getRedeliveryTradeFailBlackMerchantList()
                    .contains(globalOrderInfo.getOriginalMerchantInfo().getMerchantNo())
                    && globalConfigProperties.getStrategyCodeList().contains(info.getStrategyCode());
        }catch (Exception e){
            log.error("isHitRedeliveryTradeFailBlack {}",e.getMessage());
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void eventTransfer(OperationDomainCorrectionInfo info) {
        //1.订单校验
        CorrectionOrderInfoDTO orderInfoDTO = domainRepository.getValidCorrectionOrderInfoDto(info.getBaseInfo().getCorrectionNo());
        info.getBaseInfo().setCorrectionCode(orderInfoDTO.getBaseInfo().getCorrectionCode());

        String uuid = CommonDomainUtil.getUUID();
        String lockKey = CorrectionConstant.CORRECTION_TRANSFER_LOCK + info.getBaseInfo().getCorrectionNo();

        //拿不到锁直接报错返回
        Boolean lock = kvRepository.applyDistributeLock(lockKey, uuid, CorrectionConstant.NUM_TEN);
        AssertUtil.isTrue(BooleanUtils.isTrue(lock), ReturnCode.DUPLICATE_ORDER.getCode(), ReturnCode.DUPLICATE_ORDER.getMsg());

        try {
            //2.更新扩展信息
            this.storeUserInfo(info);
            //3.如果选择差错原因类型则对其进行校验
            if (StringUtils.isNotBlank(info.getOperationCorrectionCode())) {
                domainRepository.validOperationCode(info.getOperationCorrectionCode(), orderInfoDTO.getBaseInfo().getCorrectionCode());
                //3.1 如果选择策略则对其进行校验
                if (StringUtils.isNotBlank(info.getStrategyCode())) {

                    //3.1 选择策略校验
                    AssertUtil.isTrue(domainRepository.validChooseStrategyCode(info.getStrategyCode(), info.getOperationCorrectionCode(), orderInfoDTO.getBaseInfo().getTradeType()), ReturnCode.ILLEGAL_PARAMS.getCode(), "请选择正确的策略处理方式");

                    //3.2策略规则校验
                    this.validationStrategyCode(orderInfoDTO);
                }
                //3.2 保存策略信息
                CorrectionOrderInfoDTO saveOrderInfoDto = domainServiceAssembler.toDBCorrectionOrderInfo(info);
                correctionRepository.storeCorrectionStrategyInfo(saveOrderInfoDto);
            }

            //4.事件转移
            CorrectionEvent event = this.transferEvent(info, orderInfoDTO);

            //5.更新状态
            DomainCorrectionInfo orderInfo = bizFactory.prepareCorrectionInfoFactory(info.getBaseInfo().getCorrectionNo(), GlobalOrderReadEvent.DISPLAY);
            correctionOrderStateMachine.sendEvent(ProcessStatusEnum.getByName(orderInfoDTO.getProcessStatus()), event, new CorrectionOrderStateMachineContext(orderInfo));
        } finally {
            kvRepository.releaseDistributeLock(lockKey, uuid);
        }

    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void approval(OperationDomainCorrectionInfo info) {
        if (info.getCorrectionEvent() == CorrectionEvent.APPROVAL_AGREE) {
            //1.订单校验
            CorrectionOrderInfoDTO orderInfoDTO = domainRepository.getValidCorrectionOrderInfoDto(info.getBaseInfo().getCorrectionNo());

            //2.差错code有效性校验
            this.validationCorrectionCode(info, orderInfoDTO);

            //3.策略规则校验
            this.validationStrategyCode(orderInfoDTO);

            //4.checker 校验器
            Checker checker = this.getCheck(orderInfoDTO.getBaseInfo().getCorrectionCode());
            if (Objects.nonNull(checker)) {
                orderInfoDTO.setOperationCorrectionCode(info.getOperationCorrectionCode());
                GlobalOrderInfo globalOrderInfo = domainRepository.getGlobalOrderInfo(orderInfoDTO.getOriVoucherInfo(), GlobalOrderReadEvent.OPERATION);
                globalOrderInfo.tradeTypeValidation();
                checker.check(orderInfoDTO, globalOrderInfo,info);
            }

            //5.先更新策略
            CorrectionOrderInfoDTO saveOrderInfoDto = domainServiceAssembler.toDBCorrectionOrderInfo(info);
            correctionRepository.storeCorrectionStrategyInfo(saveOrderInfoDto);

        }
        DomainCorrectionInfo orderInfo = bizFactory.prepareCorrectionInfoFactory(info.getBaseInfo().getCorrectionNo(), GlobalOrderReadEvent.DISPLAY);

        orderInfo.setApprovalCommit(Optional.ofNullable(info.getOperationManual()).map(OperationManualFillIn::getApprovalComments).orElse(Nullable.getNullVal()));
        orderInfo.setOperator(info.getOperator());
        correctionOrderStateMachine.sendEvent(orderInfo.getProcessStatus(), info.getCorrectionEvent(), new CorrectionOrderStateMachineContext(orderInfo));

    }

    @Override
    public void execStrategy(DomainCorrectionInfo orderInfoVo) {
        //loading 准备需要的数据信息
        asyncExecutor.execute(() -> templateProcessor.doAction(orderInfoVo));
    }

    @Override
    public DomainCorrectionInfo queryCorrectionOrderInfo(CorrectionBaseInfo correctionBaseInfo) {
        return domainRepository.getValidCorrectionOrderInfo(correctionBaseInfo.getCorrectionCode(), correctionBaseInfo.getVoucherInfo().getVoucherNo());
    }

    private CorrectionEvent transferEvent(OperationDomainCorrectionInfo info, CorrectionOrderInfoDTO orderInfoDTO) {
        if (Objects.nonNull(info.getTransferInfo())) {
            CorrectionOrderInfoDTO saveOrderInfoDto = new CorrectionOrderInfoDTO(info.getBaseInfo().getCorrectionNo());
            saveOrderInfoDto.setReviewer(info.getTransferInfo().getReviewer());
            saveOrderInfoDto.setOpBasicInfo(new OperationBasicInfo(info.getTransferInfo().getOperator()));
            //1.前置校验
            AssertUtil.isTrue(StringUtils.isBlank(orderInfoDTO.getReviewer()) ||
                            orderInfoDTO.getReviewer().equals(info.getTransferInfo().getOperator()) ||
                            !orderInfoDTO.getReviewer().equals(info.getTransferInfo().getReviewer()),
                    ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.INCORRECT_REVIEWER);

            //2.更新审核人
            correctionRepository.storeCorrectionReviewer(saveOrderInfoDto);

            //3.构建钉钉消息对象
            DingTalkMessageInfo dingTalkMessageInfo = buildDingTalkMsg(info);

            //4.同产品确定，在一定时间内，相同差错原因近发送一次钉钉通知,
            // 这里根据差错原因以及复核人拿到锁才进行通知，直到锁过期，不自动释放锁
            String uuid = CommonDomainUtil.getUUID();
            String lockKey = String.format(CorrectionConstant.CORRECTION_EVENT_TRANSFER_NOTIFY_LOCK, orderInfoDTO.getBaseInfo().getCorrectionCode(), info.getTransferInfo().getReviewer(), info.getTransferInfo().getOperator());
            Boolean notifyLock = kvRepository.applyDistributeLock(lockKey, uuid, globalConfigProperties.getDingTalkAlertIntervalTime());
            if (BooleanUtils.isTrue(notifyLock)) {
                asyncExecutor.execute(() -> dingTalkRepository.sendDingTalkMsg(dingTalkMessageInfo));
            }
            return info.getTransferInfo().getOperator().equals(info.getTransferInfo().getReviewer()) ? CorrectionEvent.EVENT_VERIFICATION : CorrectionEvent.EVENT_TRANSFER;
        }
        return Nullable.getNullVal();
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void registerHandler(OperationDomainCorrectionInfo operationDomainCorrectionInfo) {
        CorrectionOrderInfoDTO saveOrderInfoDto = domainServiceAssembler.toDBCorrectionOrderInfo(operationDomainCorrectionInfo);
        //更新处理策略
        correctionRepository.storeCorrectionStrategyInfo(saveOrderInfoDto);
        //发送状态机事件
        CorrectionOrderInfoDTO dbOrderInfo = domainRepository.getValidCorrectionOrderInfoDto(operationDomainCorrectionInfo.getBaseInfo().getCorrectionNo());
        DomainCorrectionInfo correctionInfo = domainServiceAssembler.toDomainCorrectionInfo(dbOrderInfo);
        //填充策略信息
        if (StringUtils.isNotBlank(dbOrderInfo.getStrategyCode())) {
            correctionInfo.setStrategyProcessor(domainRepository.getValidStrategyInfo(dbOrderInfo.getStrategyCode()));
        }
        //渲染系统List
        correctionInfo.setSystemInfo(domainRepository.getSystemInfos(dbOrderInfo.getBaseInfo().getCorrectionCode(), dbOrderInfo.getSysSource()));

        correctionOrderStateMachine.sendEvent(ProcessStatusEnum.PROCESSED, CorrectionEvent.REGISTRATION_COMPLETE, new CorrectionOrderStateMachineContext(correctionInfo));
    }

    private CorrectionOrderHandlerRecordDTO buildHandlerRecord(String correctionNo, String strategyCode, CorrectionEvent event, String operator, CommonStatusEnum status) {
        CorrectionOrderHandlerRecordDTO orderHandlerRecord = new CorrectionOrderHandlerRecordDTO();
        orderHandlerRecord.setHandlerInfo(new CorrectionHandlerUniInfo(correctionNo, strategyCode, event.name()));
        orderHandlerRecord.setStatus(status);
        orderHandlerRecord.setOpBasicInfo(new OperationBasicInfo(operator));
        return orderHandlerRecord;
    }

    private Boolean validationExtraUserInfo(UserInfoEnum userInfoEnum, String extraUserInfo) {
        if (Objects.isNull(userInfoEnum)) {
            return Boolean.TRUE;
        }
        List<ExtraUserInfoDTO> extraUserInfoList = CommonDomainUtil.getExtraUserInfoList(extraUserInfo, userInfoEnum);
        return CollectionUtils.isNotEmpty(extraUserInfoList);
    }

    private Checker getCheck(String correctionCode) {
        Checker checker = checkerHolder.getChecker(Joiner.on(Symbols.UNDERLINE).join(correctionCode, Checker.CHECKER_SUFFIX));
        if (Objects.nonNull(checker)) {
            return checker;
        }
        if (globalConfigProperties.getNeedCheckCorrectionCodes().contains(correctionCode)) {
            return checkerHolder.getChecker(CorrectionConstant.ABSTRACT_CHECKER);
        }
        return Nullable.getNullVal();
    }

    private void storeUserInfo(OperationDomainCorrectionInfo info) {
        if (Objects.nonNull(info) && Objects.nonNull(info.getOperationManual()) && StringUtils.isNotBlank(info.getOperationManual().getExtraUserInfo())) {
            ExtraUserInfoDTO extraInfoDTO = JSONObject.parseObject(info.getOperationManual().getExtraUserInfo(), ExtraUserInfoDTO.class);
            domainOperationService.storeUserInfo(info.getBaseInfo().getCorrectionNo(), extraInfoDTO);
            //避免被重写
            info.getOperationManual().setExtraUserInfo(Nullable.getNullVal());
        }
    }

    private void validationCorrectionCode(OperationDomainCorrectionInfo info, CorrectionOrderInfoDTO orderInfoDTO) {
        domainRepository.validOperationCode(info.getOperationCorrectionCode(), orderInfoDTO.getBaseInfo().getCorrectionCode());
        AssertUtil.isTrue(domainRepository.validChooseStrategyCode(info.getStrategyCode(), info.getOperationCorrectionCode(), orderInfoDTO.getBaseInfo().getTradeType()), ReturnCode.ILLEGAL_PARAMS.getCode(), "请选择正确的策略处理方式");
        StrategyProcessorInfo validStrategyInfo = domainRepository.getValidStrategyInfo(info.getStrategyCode());
        AssertUtil.isTrue(this.validationExtraUserInfo(validStrategyInfo.getDbInfo().getExtraUserInfo(), orderInfoDTO.getOperationManual().getExtraUserInfo())
                , ReturnCode.ILLEGAL_PARAMS.getCode(), "请填写扩展用户信息");
    }

    private void validationStrategyCode(CorrectionOrderInfoDTO orderInfoDTO) {
        CorrectionBasicInfoDTO basicInfo = domainRepository.getValidBasicInfo(orderInfoDTO.getBaseInfo().getCorrectionCode());
        ValidationConditionInfo validationCondInfo = new ValidationConditionInfo(orderInfoDTO.getOriginalTradeRedundantInfo().getMerchantNo(), orderInfoDTO.getChannelCode(), orderInfoDTO.getOriginalTradeRedundantInfo().getProductCode());
        AssertUtil.isTrue(basicInfo.validationRule(validationCondInfo), ReturnCode.BUSINESS_EXCEPTION.getCode(), "策略校验不通过");
    }

    private DingTalkMessageInfo buildDingTalkMsg(OperationDomainCorrectionInfo info) {
        DingTalkNotifyMappingInfo reviewerInfo = domainOperationService.getDingTalkMapping(info.getTransferInfo().getReviewer());
        DingTalkNotifyMappingInfo operatorInfo = domainOperationService.getDingTalkMapping(info.getTransferInfo().getOperator());
        //校验
        AssertUtil.notNull(reviewerInfo, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.INCORRECT_REVIEWER);
        AssertUtil.notNull(operatorInfo, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.INCORRECT_SENDER);

        List<String> mobiles = Lists.newArrayList(reviewerInfo.getPhone());
        CorrectionBasicInfoDTO parentReasonBasicInfo = domainRepository.getValidParentReasonBasicInfo(info.getBaseInfo().getCorrectionCode());
        String formatMsg = String.format(CorrectionConstant.TRANSFER_ALERT_MSG, parentReasonBasicInfo.getCorrectionName(), operatorInfo.getName(), reviewerInfo.getName());
        DingTalkMessageInfo dingTalkMessageInfo = new DingTalkMessageInfo();
        dingTalkMessageInfo.setMsg(formatMsg);
        dingTalkMessageInfo.setToken(globalConfigProperties.getTransferAlertToken());
        dingTalkMessageInfo.setTitle(CorrectionConstant.TRANSFER_ALERT_TITLE);
        dingTalkMessageInfo.setAtMobiles(mobiles);
        return dingTalkMessageInfo;
    }
}
