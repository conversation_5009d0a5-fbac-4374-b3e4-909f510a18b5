package com.payermax.operating.correction.domainservice.service;

import com.payermax.operating.correction.core.common.dto.CorrectionBaseInfo;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.domain.dto.CorrectionAutoStrategyCodeHandlerDTO;
import com.payermax.operating.correction.domain.dto.EventTransferInfo;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;

/**
 * The interface Domain biz service.
 *
 * <AUTHOR>
 * @desc 领域业务服务对接器
 * @date 2022 /10/18
 */
public interface DomainBizService {

    /**
     * Add event.
     *
     * @param operationDomainCorrectionInfo the correction info dto
     */
    void addEvent(OperationDomainCorrectionInfo operationDomainCorrectionInfo);

    /**
     * Strategy process.
     *
     * @param operationDomainCorrectionInfo the correction info dto
     */
    void chooseStrategyCode(OperationDomainCorrectionInfo operationDomainCorrectionInfo);


    /**
     * Approval.
     *
     * @param operationDomainCorrectionInfo the correction info dto
     */
    void approval(OperationDomainCorrectionInfo operationDomainCorrectionInfo);

    /**
     * Process strategy code.
     *
     * @param correctionNo the correction no
     * @param retryNum     the retry num
     */
    void execStrategy(String correctionNo,Integer retryNum);

    /**
     * Event query domain correction info.
     *
     * @param correctionBaseInfo the correction base info
     * @return the domain correction info
     */
    DomainCorrectionInfo eventQuery(CorrectionBaseInfo correctionBaseInfo);

    /**
     * Bounce back event.
     *
     * @param correctionNo the correction no
     */
    void bounceBackEvent(String correctionNo);


    /**
     * Auto correction handle.
     *
     * @param autoHandleCorrectionOrder the auto handle correction order
     */
    void autoCorrectionHandle(CorrectionAutoStrategyCodeHandlerDTO autoHandleCorrectionOrder);

    /**
     * 登记完成，可由外部渠道通知处理结果，也可以在差错平台自行登记处理
     *
     * @param voucherInfo    the voucher info
     * @param correctionCode the correction code
     * @param memo           the memo
     */
    void registrationCompleteEvent(VoucherInfo voucherInfo, String correctionCode, String memo, String operator);

    /**
     * Transfer.
     *
     * @param transferInfo the transfer info
     */
    void transfer(EventTransferInfo transferInfo);

    void resetHandleRecord(DomainCorrectionInfo correctionInfo);

    /**
     * 任务触发-自动执行
     * @param correctionNo 差错单号
     */
    void jobAutoHandler(String correctionNo);
}
