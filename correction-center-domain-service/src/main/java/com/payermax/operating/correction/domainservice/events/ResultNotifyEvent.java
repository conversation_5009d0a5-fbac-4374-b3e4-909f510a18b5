package com.payermax.operating.correction.domainservice.events;


import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.integration.config.nacos.model.CorrectionSystemInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.ReconcileRedundantDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 * @desc 结果通知事件
 * @date 2023/5/17
 */
@ToString
@Getter
@Setter
public class ResultNotifyEvent extends Event {

    private final static String EVENT_NAME = "resultNotify";

    private VoucherInfo voucherInfo;

    private CorrectionSystemInfo systemInfo;

    private String strategyCode;

    private String memo;

    private ReconcileRedundantDTO reconcileRedundantDTO;

    public ResultNotifyEvent(VoucherInfo voucherInfo, CorrectionSystemInfo systemInfo, String strategyCode, String memo, ReconcileRedundantDTO reconcileRedundantDTO) {
        super(EVENT_NAME);
        this.voucherInfo = voucherInfo;
        this.systemInfo = systemInfo;
        this.strategyCode = strategyCode;
        this.memo = memo;
        this.reconcileRedundantDTO = reconcileRedundantDTO;
    }
}
