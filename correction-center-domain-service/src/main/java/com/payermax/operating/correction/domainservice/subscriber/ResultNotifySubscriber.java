package com.payermax.operating.correction.domainservice.subscriber;

import com.alibaba.fastjson.JSONObject;
import com.google.common.eventbus.AllowConcurrentEvents;
import com.google.common.eventbus.Subscribe;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.domainservice.events.ResultNotifyEvent;
import com.payermax.operating.correction.integration.queue.MqResultNotifyRepository;
import com.payermax.operating.correction.integration.queue.dto.ResultNotifyDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 处理结果通知订阅者
 * @date 2023/5/17
 */
@Component
@Slf4j
@SuppressWarnings("unused")
public class ResultNotifySubscriber implements Subscriber {

    @Resource
    private MqResultNotifyRepository mqResultNotifyRepository;

    @Subscribe
    @AllowConcurrentEvents
    public void subscribe(ResultNotifyEvent event) {
        if (BooleanUtils.isNotTrue(event.getSystemInfo().getNotifyFlag())) {
            return;
        }
        log.info("result notify subscribe,event : [{}]", JSONObject.toJSONString(event));
        ResultNotifyDTO.ResultNotify notify = ResultNotifyDTO.ResultNotify.builder()
                .status(CommonStatusEnum.SUCCESS.name())
                .voucherNo(event.getVoucherInfo().getVoucherNo())
                .correctionCode(event.getSystemInfo().getCorrectionCode())
                .voucherType(event.getVoucherInfo().getVoucherType().name())
                .strategyProcess(CorrectionConstant.REGISTRATION_PROCESS.equals(event.getStrategyCode()) ? event.getMemo() : event.getStrategyCode())
                .tag(event.getSystemInfo().getSysSource())
                .redundantInfo(JSONObject.toJSONString(event.getReconcileRedundantDTO()))
                .build();
        mqResultNotifyRepository.handlerResultNotify(new ResultNotifyDTO(notify));

    }

}
