package com.payermax.operating.correction.domainservice.processor;

import com.google.common.collect.Maps;
import com.payermax.operating.correction.core.common.utils.AppContextHolder;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc processor 处理器
 * @date 2022/10/14
 */
@Component
public class ProcessorHolder {
    private final Map<String, Processor> processorMap = Maps.newConcurrentMap();

    @Resource
    private ApplicationContext applicationContext;


    @PostConstruct
    public void init() {
        Map<String, Processor> beans = AppContextHolder.getBeansByType(applicationContext, Processor.class);
        processorMap.putAll(beans);
    }

    public Processor getProcess(String beanName) {
        return processorMap.get(beanName);
    }
}
