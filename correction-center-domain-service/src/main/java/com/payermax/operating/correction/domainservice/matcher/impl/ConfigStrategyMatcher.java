package com.payermax.operating.correction.domainservice.matcher.impl;

import com.google.common.collect.Lists;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.AutoHandleCorrectionOrderDTO;
import com.payermax.operating.correction.domain.dto.CorrectionAutoStrategyCodeHandlerDTO;
import com.payermax.operating.correction.domainservice.matcher.AbstractMatcher;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyExpressionInfo;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyRule;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component(value = CorrectionConstant.CONFIG_STRATEGY_BEAN)
public class ConfigStrategyMatcher extends AbstractMatcher {

    @Override
    public List<CorrectionAutoStrategyCodeHandlerDTO> matchAutoHandlerStrategyInfo(StrategyRule strategyRule, AutoHandleCorrectionOrderDTO autoHandleCorrectionOrderDTO, GlobalOrderInfo globalOrderInfo) {
        if (Objects.isNull(globalOrderInfo)) {
            return Nullable.getNullVal();
        }
        String value = StringUtils.defaultString((String) PARSER.parseExpression(strategyRule.getSpel()).getValue(globalOrderInfo), CorrectionConstant.DEFAULT);
        List<StrategyExpressionInfo> strategyExpressionInfo = strategyRule.getStrategyExpressions().get(value);
        if (CollectionUtils.isEmpty(strategyExpressionInfo)) {
            return Nullable.getNullVal();
        }
        //按照策略优先级排序
        List<StrategyExpressionInfo> strategyExpressionInfoOrderByStrategyLevelDesc = strategyExpressionInfo.stream()
                .sorted(Comparator.comparing(StrategyExpressionInfo::getStrategyLevel, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        //遍历所有策略
        for (StrategyExpressionInfo expressionInfo : strategyExpressionInfoOrderByStrategyLevelDesc) {
            if (!super.strategyMatch(expressionInfo.getExps(), autoHandleCorrectionOrderDTO)) {
               continue;
            }
            CorrectionAutoStrategyCodeHandlerDTO autoStrategyCodeHandlerDTO = CorrectionAutoStrategyCodeHandlerDTO.builder()
                    .correctionNo(autoHandleCorrectionOrderDTO.getCorrectionNo())
                    .strategyCode(expressionInfo.getStrategyProcessor())
                    .operationCorrectionCode(expressionInfo.getChildCorrectionCode())
                    .childStrategyCode(expressionInfo.getChildStrategyProcessor())
                    .operator(CorrectionConstant.SYSTEM_OPERATION)
                    .build();
            return Lists.newArrayList(autoStrategyCodeHandlerDTO);
        }
        //走到这里 所有策略都没有匹配上
        return Nullable.getNullVal();
    }
}
