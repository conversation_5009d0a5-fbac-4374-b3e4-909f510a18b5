package com.payermax.operating.correction.domainservice.processor.impl;

import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.core.common.utils.ObjectsUtils;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.processor.assembler.ProcessorAssembler;
import com.payermax.operating.correction.domainservice.processor.utils.OrderStatusUtils;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.enums.TradeOrderStatus;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderResponse;
import com.payermax.operating.correction.integration.rpc.ordercenter.repository.OrderCenterRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 补交易单（超时关单补交易）
 * @date 2022/10/17
 */
@Component(value = "patchTradeOrderProcessor")
@Slf4j
public class PatchTradeOrderProcessor implements Processor {
    @Resource
    private OrderCenterRepository orderCenterRepository;

    @Resource
    private ProcessorAssembler processorAssembler;

    @Override
    public void preCheck(DomainCorrectionInfo correctionInfo) {
        String status = OrderStatusUtils.orderCenterTradeStatus(correctionInfo.getGlobalOrderInfo());
        AssertUtil.isTrue(ObjectsUtils.anyMatch(TradeOrderStatus.getByCode(status), TradeOrderStatus.TRADE_CLOSE, TradeOrderStatus.TRADE_FAIL), ReturnCode.PROCESSOR_VALID_EXCEPTION.getCode(), ReturnMsg.ORDER_CENTER_TIMEOUT_STATUS_MATCH_FAIL);
    }

    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {
        TradeOrderPatchOrderResponse response = orderCenterRepository.patchTradeOrder(processorAssembler.toPatchPayOrder(correctionInfo));
        response.defence();
        return processorAssembler.toBaseResProcess(response);
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo globalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        String status = OrderStatusUtils.orderCenterTradeStatus(globalOrderInfo);
        return TradeOrderStatus.isSuccessState(TradeOrderStatus.getByCode(status)) ? CommonStatusEnum.SUCCESS : CommonStatusEnum.PENDING;
    }

}
