package com.payermax.operating.correction.domainservice.processor.assembler;

import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.dto.MerchantInfo;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderRequest;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderResponse;
import com.payermax.operating.correction.integration.rpc.paylink.dto.PayLinkPatchNoticeRequest;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Date;
import java.util.Optional;

/**
 * The interface Processor assembler.
 *
 * <AUTHOR>
 * @desc 处理器Assembler
 * @date 2022 /10/16
 */
@Mapper(componentModel = "spring", imports = {Optional.class, Date.class, ProcessStatusEnum.class, DCVoucherType.class, MerchantInfo.class, VoucherInfo.class})
public interface ProcessorAssembler {


    /**
     * To patch pay order trade order patch order request.
     *
     * @param correctionInfo the correction info
     * @return the trade order patch order request
     */
    @Mappings({
            @Mapping(target = "originalOrderInfo.payRequestNo", expression = "java(globalOrderInfo.getTradeInfo().getPayRequest().getOriPayRequestNo())"),
            @Mapping(target = "originalOrderInfo.merchantNo", source = "globalOrderInfo.originalMerchantInfo.merchantNo"),
            @Mapping(target = "originalOrderInfo.merchantAppId", source = "globalOrderInfo.originalMerchantInfo.merchantAppId"),
            @Mapping(target = "originalOrderInfo.outTradeNo", expression = "java(globalOrderInfo.getOriginOutTradeNo())"),
            @Mapping(target = "originalOrderInfo.tradeOrderNo", expression = "java(globalOrderInfo.getTradeInfo().getTradeOrder().getOriTradeOrderNo())"),
    })
    TradeOrderPatchOrderRequest toPatchPayOrder(DomainCorrectionInfo correctionInfo);


    @Mappings({
            @Mapping(target = "originalOrderInfo.payRequestNo", expression = "java(domainCorrectionInfo.getGlobalOrderInfo().getTradeInfo().getRefundInfo().getRefundOrder().getOriPayRequestNoFromRefundExtendedField())"),
            @Mapping(target = "originalOrderInfo.tradeOrderNo", expression = "java(domainCorrectionInfo.getGlobalOrderInfo().getTradeInfo().getTradeOrder().getOriTradeOrderNo())"),
            @Mapping(target = "originalOrderInfo.outTradeNo", expression = "java(domainCorrectionInfo.getGlobalOrderInfo().getOriginOutTradeNo())"),
    })
    TradeOrderPatchOrderRequest toPatchRefundFailedDuplicateTradeOrder(DomainCorrectionInfo domainCorrectionInfo);

    /**
     * To base res process base res process.
     *
     * @param response the response
     * @return the base res process
     */
    @Mappings({
            @Mapping(target = "resVoucherInfo", expression = "java(new VoucherInfo(response.getVoucherNo(),response.getVoucherType()))"),
    })
    BaseResProcess toBaseResProcess(TradeOrderPatchOrderResponse response);

    @Mappings({
            @Mapping(target = "merchantNo", source = "correctionInfo.globalOrderInfo.originalMerchantInfo.merchantNo"),
            @Mapping(target = "appId", source = "correctionInfo.globalOrderInfo.originalMerchantInfo.merchantAppId"),
            @Mapping(target = "outTradeNo", expression = "java(correctionInfo.getGlobalOrderInfo().getOriginOutTradeNo())"),
    })
    PayLinkPatchNoticeRequest toPayLinkPatchNoticeRequest(DomainCorrectionInfo correctionInfo);

}
