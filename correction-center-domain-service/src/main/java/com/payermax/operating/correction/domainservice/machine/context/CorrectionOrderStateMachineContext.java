package com.payermax.operating.correction.domainservice.machine.context;

import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * The type Correction order state machine context.
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class CorrectionOrderStateMachineContext {

    /**
     * 当前执行订单
     */
    private DomainCorrectionInfo correctionOrderInfo;

    /**
     * Gets correction no.
     *
     * @return the correction no
     */
    public String getCorrectionNo() {
        return correctionOrderInfo.getCorrectionNo();
    }

    /**
     * Update current trade.
     *
     * @param status the status
     */
    public void updateCurrentTrade(ProcessStatusEnum status) {
        correctionOrderInfo.setProcessStatus(status);
    }
}
