package com.payermax.operating.correction.domainservice.utils;

import com.google.common.base.Joiner;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;

/**
 * <AUTHOR>
 * @desc 处理器bean对象处理器
 * @date 2022/12/14
 */
public class ProcessorBeanUtils {

    /**
     * 根据处理器名字和处理器响应生成唯一的处理对象
     */
    public static String getBeanNameUniqueKey(String processorName, CommonStatusEnum statusEnum) {
        return Joiner.on(Symbols.UNDERLINE).join(processorName, statusEnum.name());
    }

}
