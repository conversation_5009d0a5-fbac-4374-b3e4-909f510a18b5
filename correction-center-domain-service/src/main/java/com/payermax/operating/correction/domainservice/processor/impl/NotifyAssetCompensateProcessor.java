package com.payermax.operating.correction.domainservice.processor.impl;

import com.payermax.common.lang.model.dto.request.PageRequest;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.dto.MerchantInfo;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.processor.utils.OrderStatusUtils;
import com.payermax.operating.correction.domainservice.utils.CommonDomainUtil;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.enums.AssetPayOrderStatus;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.QueryCorrectionOrderPageDTO;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionOperationRepository;
import com.payermax.operating.correction.integration.rpc.asset.dto.AssetNotifyInfo;
import com.payermax.operating.correction.integration.rpc.asset.repository.AssetRepository;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * @description: 通知资产交换修改补单关联关系
 * @author: WangTao
 * @create: 2024-12-24 14:19
 **/
@Component(value = "assetNotifyCompensateProcessor")
@Slf4j
public class NotifyAssetCompensateProcessor implements Processor {

    @Autowired
    AssetRepository assetRepository;
    @Resource
    CorrectionOperationRepository operationRepository;

    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {
        String oriPaymentOrderNo = Optional.ofNullable(correctionInfo.getGlobalOrderInfo())
                .map(GlobalOrderInfo::getTradeInfo)
                .map(GlobalOrderInfo.TradeInfo::getRefundInfo)
                .map(GlobalOrderInfo.RefundOrderInfo::getRefundOrder)
                .map(GlobalOrderInfo.OrderInfo::getOriPayRequestNoFromRefundExtendedField).orElse(null);
        String productCode = Optional.ofNullable(correctionInfo.getGlobalOrderInfo())
                .map(GlobalOrderInfo::getProductCode)
                .orElse(null);
        String bizIdentify =  Optional.ofNullable(correctionInfo.getGlobalOrderInfo())
                .map(GlobalOrderInfo::getBizIdentify)
                .orElse(null);
        String merchantNo = Optional.ofNullable(correctionInfo.getGlobalOrderInfo())
                .map(GlobalOrderInfo::getOriginalMerchantInfo)
                .map(MerchantInfo::getMerchantNo)
                .orElse(null);
        String merchantOrderNo = Optional.ofNullable(correctionInfo.getGlobalOrderInfo())
                .map(GlobalOrderInfo::getOriginalMerchantInfo)
                .map(MerchantInfo::getMerOrderNo)
                .orElse(null);
        String tradeOrderNo = Optional.ofNullable(correctionInfo.getGlobalOrderInfo())
                .map(GlobalOrderInfo::getTradeInfo)
                .map(GlobalOrderInfo.TradeInfo::getTradeOrder)
                .map(GlobalOrderInfo.OrderInfo::getOrderNo)
                .orElse(null);

        AssertUtil.isTrue(StringUtils.isNotBlank(tradeOrderNo), ReturnCode.ILLEGAL_PARAMS.getCode(),ReturnCode.ILLEGAL_PARAMS.getMsg());

        //查询数据库匹配对应的信息-根据商户订单号进行查询
        QueryCorrectionOrderPageDTO queryCorrectionPage = QueryCorrectionOrderPageDTO.builder()
                .page(new PageRequest(1000L, 1L))
                .merchantOrderNo(merchantOrderNo)
                //这里只筛选重复支付退款类型
                .operationCorrectionCode(CorrectionConstant.CORRECTION_CHANNEL_REPEATPAY)
                .build();
        List<CorrectionOrderInfoDTO> ascOrderList = operationRepository.loadCorrectionOrderInfo(queryCorrectionPage)
                .getRecords()
                .stream()
                .sorted(Comparator.comparing(event -> event.getOpBasicInfo().getUtcCreate()))
                .collect(Collectors.toList());
        //根据推入差错的时间进行排序，并取对应的顺序
        int order = CommonDomainUtil.getCorrectSuffixFromOrderInfo(ascOrderList, correctionInfo);
        String correctNoForOrder = CommonDomainUtil.getCorrectSuffix(order);


        AssetNotifyInfo assetNotifyInfo = AssetNotifyInfo.builder()
                //这里支付单是通过退款单扩展字段中获取原支付单号
                .paymentOrderNo(oriPaymentOrderNo)
                .productCode(productCode)
                .merchantNo(merchantNo)
                .tradeOrderNo(tradeOrderNo)
                .correctTradeOrderNo(tradeOrderNo+correctNoForOrder)
                .bizIdentify(bizIdentify)
                .build();

        return assetRepository.correctionRepeatPay(assetNotifyInfo);
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo correctionGlobalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        AssetPayOrderStatus assetPayOrderStatus = AssetPayOrderStatus.getByVal(OrderStatusUtils.assetExchangePayOrderStatus(correctionGlobalOrderInfo));
        return AssetPayOrderStatus.SUCCESS == assetPayOrderStatus ? CommonStatusEnum.SUCCESS : CommonStatusEnum.PENDING;
    }
}
