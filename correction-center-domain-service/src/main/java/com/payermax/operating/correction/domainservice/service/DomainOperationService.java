package com.payermax.operating.correction.domainservice.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.payermax.common.lang.model.dto.request.PageRequest;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.OrderDetailType;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.enums.ValidType;
import com.payermax.operating.correction.domain.dto.ExtendInfoDTO;
import com.payermax.operating.correction.domain.dto.ExtraUserInfoDTO;
import com.payermax.operating.correction.domain.dto.PaymentInstanceDomainInfo;
import com.payermax.operating.correction.integration.config.nacos.model.CorrectionPayoutsCountryInfo;
import com.payermax.operating.correction.integration.config.nacos.model.DingTalkNotifyMappingInfo;
import com.payermax.operating.correction.integration.config.nacos.model.Expression;
import com.payermax.operating.correction.integration.dto.PaymentInstanceDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.*;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsFiledInfo;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * The interface Domain operation service.
 *
 * <AUTHOR>
 * @desc domain运营平台实现类
 * @date 2022 /10/25
 */
public interface DomainOperationService {

    /**
     * Add strategy info.
     *
     * @param toOperationStrategyInfo the to operation strategy info
     */
    void storeStrategyInfo(CorrectionOperationStrategyInfoDTO toOperationStrategyInfo);

    /**
     * Gets strategy info.
     *
     * @param strategyCode the strategy code
     * @return the strategy info
     */
    CorrectionOperationStrategyInfoDTO getStrategyInfo(String strategyCode);

    /**
     * Get strategy info by page list.
     *
     * @param page  the page
     * @param valid the valid
     * @return the list
     */
    Page<CorrectionOperationStrategyInfoDTO> getStrategyInfoByPage(PageRequest page, ValidType valid);

    /**
     * Store reason basic info.
     *
     * @param basicInfoDTO the basic info dto
     */
    void storeReasonBasicInfo(CorrectionBasicInfoDTO basicInfoDTO);

    /**
     * Update reason basic info.
     *
     * @param basicInfoDTO the basic info dto
     */
    void updateReasonBasicInfo(CorrectionBasicInfoDTO basicInfoDTO);

    /**
     * Gets reason basic info by page.
     *
     * @param page  the page
     * @param valid the valid
     * @return the reason basic info by page
     */
    Page<CorrectionBasicInfoDTO> getReasonBasicInfoByPage(PageRequest page, ValidType valid);

    /**
     * Get correction order by page page.
     *
     * @param queryCorrectionPage the correction query page
     * @return the page
     */
    Page<CorrectionOrderInfoDTO>getCorrectionOrderByPage(QueryCorrectionOrderPageDTO queryCorrectionPage);

    /**
     * Get correction order by page page.
     *
     * @param page          the page
     * @param correctionNos the correction nos
     * @param voucherNos    the voucher nos
     * @return the page
     */
    Page<CorrectionOrderInfoDTO>getCorrectionOrderByPage(PageRequest page,List<String>correctionNos,List<String>voucherNos);

    /**
     * Gets correction order handler record.
     *
     * @param correctionNo    the correctionNo
     * @param orderDetailType the order detail type
     * @return the correction order handler record
     */
    List<CorrectionOrderHandlerRecordDTO> getCorrectionOrderHandlerRecord(String correctionNo, OrderDetailType orderDetailType);

    /**
     * Gets global order info.
     *
     * @param voucherNo   the voucher no
     * @param voucherType the voucher type
     * @return the global order info
     */
    GlobalOrderInfo getGlobalOrderInfo(String voucherNo, DCVoucherType voucherType);

    /**
     * Store proofInfo.
     *
     * @param correctionNo the correction no
     * @param proofInfo    the extend info
     */
    void storeProofInfo(String correctionNo, ExtendInfoDTO proofInfo);

    /**
     * Store user info.
     *
     * @param correctionNo the correction no
     * @param userInfo     the user info
     */
    void storeUserInfo(String correctionNo, ExtraUserInfoDTO userInfo);

    /**
     * Store correction remark info.
     *
     * @param correctionNo the correction no
     * @param memo         the memo
     */
    void storeCorrectionRemarkInfo(String correctionNo,String memo);

    /**
     * Get all strategy code info map.
     *
     * @return the map
     */
    Map<String,String> getAllMappingStrategyCodeInfo();

    /**
     * Get all strategy code info map.
     *
     * @return the map
     */
    Map<String,CorrectionOperationStrategyInfoDTO> getAllMappingStrategyCode();


    /**
     * Gets all mapping reason info.
     *
     * @return the all mapping reason info
     */
    Map<String,String> getAllChildrenMappingReasonInfo();

    /**
     * Get all mapping parent reason info map.
     *
     * @return the map
     */
    Map<String,CorrectionBasicInfoDTO>getAllMappingParentReasonInfo();

    /**
     * Get basic info by parent code list.
     *
     * @param parentCorrectionCode the parent correction code
     * @param tradeType            the trade type
     * @return the list
     */
    List<CorrectionBasicInfoDTO>getBasicInfoByParentCode(String parentCorrectionCode,TradeType tradeType);

    /**
     * Gets payment instance list.
     *
     * @param correctionNo    the correction no
     * @param country         the country
     * @param paymentMethodNo the payment method no
     * @return the payment instance list
     */
    List<PaymentInstanceDTO> getPaymentInstanceList(String correctionNo, String country, String paymentMethodNo);

    /**
     * Get payment instance list list.
     *
     * @param paymentInstanceDomainInfo the payment instance domain info
     * @return the list
     */
    List<PaymentInstanceDTO>getPaymentInstanceList(PaymentInstanceDomainInfo paymentInstanceDomainInfo);


    /**
     * Rendering pay out info json array.
     *
     * @param paymentInstanceDomainInfo the payment instance domain info
     * @return the json array
     */
    List<PayoutsFiledInfo> renderingPayOutInfo(PaymentInstanceDomainInfo paymentInstanceDomainInfo);

    /**
     * Gets cashier product no.
     *
     * @param paymentInstanceDomainInfo the payment instance domain info
     */
    void getCashierProductNo(PaymentInstanceDomainInfo paymentInstanceDomainInfo);

    /**
     * Gets config strategy code.
     *
     * @param operatorCorrectionCode the operator correction code
     * @param tradeType              the trade type
     * @return the config strategy code
     */
    List<String> getConfigStrategyCode(String operatorCorrectionCode, TradeType tradeType);

    /**
     * Get correction payouts country list.
     *
     * @return the list
     */
    List<CorrectionPayoutsCountryInfo>getCorrectionPayoutsCountry();

    /**
     * Get strategy blacklist
     * @return
     */
    ConcurrentHashMap<String, List<Expression>> getStrategyBlacklist();

    /**
     * Gets all parent correction reason.
     *
     * @return the all parent correction reason
     */
    List<CorrectionBasicInfoDTO> getAllParentCorrectionReason();

    /**
     * Store op basic info.
     *
     * @param correction the correction
     * @param basicInfo  the basic info
     */
    void storeOpBasicInfo(String correction, OperationBasicInfo basicInfo);

    DingTalkNotifyMappingInfo getDingTalkMapping(String shareId);


    void handlerDirtyCorrectionNo(String correction,String tarStats);

}
