package com.payermax.operating.correction.domainservice.processor.impl;

import com.payermax.common.lang.model.dto.request.PageRequest;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.processor.assembler.ProcessorAssembler;
import com.payermax.operating.correction.domainservice.processor.utils.OrderStatusUtils;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.enums.AssetPayOrderStatus;
import com.payermax.operating.correction.integration.enums.TradeOrderStatus;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.QueryCorrectionOrderPageDTO;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionOperationRepository;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.OrderInfoDTO;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderRequest;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderResponse;
import com.payermax.operating.correction.integration.rpc.ordercenter.enums.PatchType;
import com.payermax.operating.correction.integration.rpc.ordercenter.repository.OrderCenterRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 重复支付补交易支付（内部）
 * @date 2023/3/9
 */
@Component(value = "patchInnerDuplicationPaymentProcessor")
@Slf4j
public class PatchInnerDuplicationPaymentProcessor implements Processor {

    @Resource
    private OrderCenterRepository orderCenterRepository;

    @Resource
    private ProcessorAssembler processorAssembler;

    @Resource
    private CorrectionOperationRepository operationRepository;

    @Override
    public void preCheck(DomainCorrectionInfo correctionInfo) {
        String status = OrderStatusUtils.assetExchangePayOrderStatus(correctionInfo.getGlobalOrderInfo());
        AssertUtil.isTrue(AssetPayOrderStatus.FAILED == AssetPayOrderStatus.getByVal(status), ReturnCode.PROCESSOR_VALID_EXCEPTION.getCode(), ReturnMsg.ASSET_STATUS_MATCH_FAIL);
    }

    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {
        TradeOrderPatchOrderRequest orderRequest = processorAssembler.toPatchPayOrder(correctionInfo);
        this.wrapPatchOrderInfo(orderRequest, correctionInfo);
        TradeOrderPatchOrderResponse response = orderCenterRepository.patchInnerDuplicateTradeOrder(orderRequest);
        response.defence();
        return processorAssembler.toBaseResProcess(response);
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo globalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        String status = OrderStatusUtils.orderCenterTradeStatus(globalOrderInfo);
        return TradeOrderStatus.isSuccessState(TradeOrderStatus.getByCode(status)) ? CommonStatusEnum.SUCCESS : CommonStatusEnum.PENDING;
    }

    private void wrapPatchOrderInfo(TradeOrderPatchOrderRequest orderRequest, DomainCorrectionInfo correctionInfo) {
        OrderInfoDTO originalOrderInfo = orderRequest.getOriginalOrderInfo();
        OrderInfoDTO patchOrderInfo = new OrderInfoDTO();
        orderRequest.setPatchOrderInfo(patchOrderInfo);
        orderRequest.setPatchType(PatchType.PATCH_TRADE_PAY_ORDER);

        //查询数据库匹配对应的信息
        QueryCorrectionOrderPageDTO queryCorrectionPage = QueryCorrectionOrderPageDTO.builder()
                .page(new PageRequest(1000L, 1L))
                .merchantOrderNo(originalOrderInfo.getOutTradeNo())
                .build();
        List<CorrectionOrderInfoDTO>
                ascOrderList = operationRepository.loadCorrectionOrderInfo(queryCorrectionPage).getRecords().stream().sorted(Comparator.comparing(event -> event.getOpBasicInfo().getUtcCreate())).collect(Collectors.toList());
        //根据推入差错的时间进行排序，并取对应的顺序
        int order = 1;
        for (int i = 0; i < ascOrderList.size(); i++) {
            if (correctionInfo.getCorrectionNo().equals(ascOrderList.get(i).getBaseInfo().getCorrectionNo())) {
                order = i + 1;
                break;
            }
        }
        //填充重复支付差错支付标识
        patchOrderInfo.setOutTradeNo(originalOrderInfo.getOutTradeNo() + CorrectionConstant.CORRECTION_FLAG + order);
        patchOrderInfo.setPayRequestNo(originalOrderInfo.getPayRequestNo() + CorrectionConstant.CORRECTION_FLAG );
        patchOrderInfo.setTradeOrderNo(originalOrderInfo.getTradeOrderNo() + CorrectionConstant.CORRECTION_FLAG + order);
        orderRequest.setCorrectionSuffix(CorrectionConstant.CORRECTION_FLAG + order);
    }

}
