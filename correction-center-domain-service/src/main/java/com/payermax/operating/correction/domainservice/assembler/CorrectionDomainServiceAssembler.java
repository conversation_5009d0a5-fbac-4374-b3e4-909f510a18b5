package com.payermax.operating.correction.domainservice.assembler;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.dto.CorrectionBaseInfo;
import com.payermax.operating.correction.core.common.dto.MerchantInfo;
import com.payermax.operating.correction.core.common.dto.OperationManualFillIn;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.domain.dto.*;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domain.enums.SourceEnum;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.OriginalTradeRedundantInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.ReconcileRedundantDTO;
import com.payermax.operating.correction.integration.queue.dto.CorrectionEventMessageInfo;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Currency;
import java.util.Date;
import java.util.Optional;

/**
 * The interface Correction info domain assembler.
 *
 * <AUTHOR>
 * @desc 差错对象转换类
 * @date 2022 /4/1
 */
@Mapper(componentModel = "spring", imports = {Optional.class, Date.class, ProcessStatusEnum.class, DCVoucherType.class,
        MerchantInfo.class, SourceEnum.class,EventTransferInfo.class, OperationManualFillIn.class, CorrectionBaseInfo.class,
        Money.class, Currency.class})
public interface CorrectionDomainServiceAssembler {


    /**
     * To domain correction domain correction info.
     *
     * @param operationDomain the operation domain
     * @return the domain correction info
     */
    @Mappings({
            @Mapping(target = "correctionNo", expression = "java(Optional.ofNullable(operationDomain.getBaseInfo()).map(CorrectionBaseInfo::getCorrectionNo).orElse(null))"),
            @Mapping(target = "oriVoucherInfo", source = "baseInfo.voucherInfo"),
            @Mapping(target = "detailDesc",source = "baseInfo.detailDesc"),
    })
    DomainCorrectionInfo toDomainCorrection(OperationDomainCorrectionInfo operationDomain);

    /**
     * To domain correction info domain correction info.
     *
     * @param dbOrderInfo the db order info
     * @return the domain correction info
     */
    @Mappings({
            @Mapping(target = "correctionNo", source = "baseInfo.correctionNo"),
            @Mapping(target = "voucherNo", source = "baseInfo.voucherInfo.voucherNo"),
            @Mapping(target = "voucherType", source = "baseInfo.voucherInfo.voucherType"),
            @Mapping(target = "tradeType", source = "baseInfo.tradeType"),
            @Mapping(target = "source", expression = "java(SourceEnum.getByOrder(dbOrderInfo.getEventSource()))"),
            @Mapping(target = "processStatus", expression = "java(ProcessStatusEnum.getByName(dbOrderInfo.getProcessStatus()))"),
            @Mapping(target = "operator", source = "opBasicInfo.operator"),
            @Mapping(target = "memo", source = "operationManual.memo"),
            @Mapping(target = "redundantInfo", source = "originalTradeRedundantInfo"),
    })
    DomainCorrectionInfo toDomainCorrectionInfo(CorrectionOrderInfoDTO dbOrderInfo);

    /**
     * To db correction order info correction order info dto.
     *
     * @param domainCorrectionInfo the domain correction info
     * @return the correction order info dto
     */
    @Mappings({
            @Mapping(target = "baseInfo.correctionNo", source = "correctionNo"),
            @Mapping(target = "baseInfo.correctionCode", source = "basicInfoDTO.correctionCode"),
            @Mapping(target = "baseInfo.voucherInfo.voucherNo", source = "voucherNo"),
            @Mapping(target = "baseInfo.voucherInfo.voucherType", source = "voucherType"),
            @Mapping(target = "baseInfo.tradeType", source = "tradeType"),
            @Mapping(target = "merchantOrderNo", expression = "java(Optional.ofNullable(domainCorrectionInfo.getMerchantInfo()).map(MerchantInfo::getMerOrderNo).orElse(null))"),
            @Mapping(target = "eventSource", expression = "java(domainCorrectionInfo.getSource().getOrder())"),
            @Mapping(target = "processStatus", expression = "java(domainCorrectionInfo.getProcessStatus().name())"),
            @Mapping(target = "opBasicInfo.operator", source = "operator"),
            @Mapping(target = "channelCode", source = "channelCode"),
            @Mapping(target = "detailDesc", source = "detailDesc"),
            @Mapping(target = "originalTradeRedundantInfo", source = "redundantInfo"),
    })
    CorrectionOrderInfoDTO toDBCorrectionOrderInfo(DomainCorrectionInfo domainCorrectionInfo);

    /**
     * To db correction order info correction order info dto.
     *
     * @param operationDomainCorrectionInfo the operation domain correction info
     * @return the correction order info dto
     */
    @Mappings({
            @Mapping(target = "baseInfo", source = "baseInfo"),
            @Mapping(target = "operationCorrectionCode", source = "operationCorrectionCode"),
            @Mapping(target = "strategyCode", source = "strategyCode"),
            @Mapping(target = "operationManual", source = "operationManual"),
            @Mapping(target = "opBasicInfo.operator", source = "operator"),
    })
    CorrectionOrderInfoDTO toDBCorrectionOrderInfo(OperationDomainCorrectionInfo operationDomainCorrectionInfo);

    /**
     * To operation domain correction info operation domain correction info.
     *
     * @param autoHandleCorrectionOrder the auto handle correction order
     * @return the operation domain correction info
     */
    @Mappings({
            @Mapping(target = "baseInfo.correctionNo", source = "correctionNo"),
            @Mapping(target = "strategyCode", source = "strategyCode"),
            @Mapping(target = "operator", source = "operator"),
            @Mapping(target = "operationCorrectionCode",source = "operationCorrectionCode"),
            @Mapping(target = "operationManual.memo",source = "childStrategyCode")
    })
    OperationDomainCorrectionInfo toOperationDomainCorrectionInfo(CorrectionAutoStrategyCodeHandlerDTO autoHandleCorrectionOrder);

    /**
     * To auto handle correction order auto handle correction order dto.
     *
     * @param domainCorrectionInfo the domain correction info
     * @return the auto handle correction order dto
     */
    @Mappings({
            @Mapping(target = "correctionNo", source = "correctionNo"),
            @Mapping(target = "correctionCode", source = "basicInfoDTO.correctionCode"),
            @Mapping(target = "channelCode", source = "channelCode"),
            @Mapping(target = "merchantNo", source = "merchantInfo.merchantNo"),
            @Mapping(target = "merchantOrderNo", source = "merchantInfo.merOrderNo"),
            @Mapping(target = "tradeType",source = "tradeType"),
            @Mapping(target = "sysSources",source = "sysSource"),
            @Mapping(target = "bizType",source = "merchantInfo.bizType")
    })
    AutoHandleCorrectionOrderDTO toAutoHandleCorrectionOrder(DomainCorrectionInfo domainCorrectionInfo);

    /**
     * To correction event message info correction event message info.
     *
     * @param operationDomainCorrectionInfo the operation domain correction info
     * @return the correction event message info
     */
    @Mappings({
            @Mapping(target = "messageBody.sysSource", source = "sysSource"),
            @Mapping(target = "messageBody.correctionInfo.voucherNo", source = "baseInfo.voucherInfo.voucherNo"),
            @Mapping(target = "messageBody.correctionInfo.voucherType", expression = "java(voucherInfo.getVoucherType().name())"),
            @Mapping(target = "messageBody.correctionInfo.correctionCode", source = "baseInfo.correctionCode"),
            @Mapping(target = "messageBody.correctionInfo.detailCode", source = "baseInfo.detailCode"),
            @Mapping(target = "messageBody.correctionInfo.detailDesc", source = "baseInfo.detailDesc"),
            @Mapping(target = "messageBody.amountInfo.payTotalMoney", source = "payTotalMoney"),
            @Mapping(target = "messageBody.retryType", source = "retryType"),
    })
    CorrectionEventMessageInfo toCorrectionEventMessageInfo(OperationDomainCorrectionInfo operationDomainCorrectionInfo);


    /**
     * To redundant info original trade redundant info.
     *
     * @param orderInfo      the order info
     * @param correctionInfo the correction info
     * @return the original trade redundant info
     */
    @Mappings({
            @Mapping(target = "productCode", source = "orderInfo.productCode"),
            @Mapping(target = "merchantNo", source = "orderInfo.originalMerchantInfo.merchantNo"),
            @Mapping(target = "refundTypeSource", expression = "java(Optional.ofNullable(orderInfo.getTradeInfo()).map(GlobalOrderInfo.TradeInfo::getRefundInfo).map(GlobalOrderInfo.RefundOrderInfo::getRefundType).orElse(null))"),
            @Mapping(target = "retryType", source = "correctionInfo.retryType"),
            @Mapping(target = "errorCode", source = "correctionInfo.baseInfo.detailCode"),
            @Mapping(target = "errorMsg", source = "correctionInfo.baseInfo.detailDesc"),
    })
    ReconcileRedundantDTO toRedundantInfo(GlobalOrderInfo orderInfo,OperationDomainCorrectionInfo correctionInfo);


    /**
     * Manual to correction info dto.
     *
     * @param orderInfoDTO 差错单
     * @return the correction info dto
     */
    @Mappings({
            @Mapping(target = "baseInfo.correctionCode", source = "baseInfo.correctionCode"),
            @Mapping(target = "baseInfo.detailDesc", source = "baseInfo.detailDesc"),
            @Mapping(target = "baseInfo.detailCode", source = "baseInfo.detailCode"),
            @Mapping(target = "sysSource", source = "sysSource"),
            @Mapping(target = "payTotalMoney", source = "payTotalMoney"),
            @Mapping(target = "retryInfo", expression = "java(new com.payermax.operating.correction.domain.dto.RetryInfo())"),
    })
    OperationDomainCorrectionInfo toCorrectionInfo(CorrectionOrderInfoDTO orderInfoDTO);


    /**
     * To redundant info reconcile redundant dto.
     *
     * @param channelReconcileRedundant the channel reconcile redundant
     * @return the reconcile redundant dto
     */
    ReconcileRedundantDTO toRedundantInfo(ChannelReconcileRedundantDTO channelReconcileRedundant);


    @Mappings({
            @Mapping(target = "baseInfo.correctionNo", source = "correctionNo"),
            @Mapping(target = "transferInfo", source = "transferInfo"),
    })
    OperationDomainCorrectionInfo toOperationDomainCorrectionInfo(EventTransferInfo transferInfo);

    @Mappings({
            @Mapping(target = "amount", expression = "java(Optional.ofNullable(channelReconcileRedundantDTO.getPayAmount()).map(Money::getAmount).orElse(null))"),
            @Mapping(target = "currency", expression = "java(Optional.ofNullable(channelReconcileRedundantDTO.getPayAmount()).map(Money::getCurrency).map(Currency::getCurrencyCode).orElse(null))"),
    })
    CorrectionEventMessageInfo.ChannelReconcileRedundantInfo toChannelReconcileRedundantInfo(ChannelReconcileRedundantDTO channelReconcileRedundantDTO);

    AutoHandleCorrectionOrderDTO toMatcherAutoHandleCorrectionOrderDTO(AutoHandleCorrectionOrderDTO autoHandleCorrectionOrderDTO);
}
