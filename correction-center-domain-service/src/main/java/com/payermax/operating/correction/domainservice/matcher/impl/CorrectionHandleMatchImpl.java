package com.payermax.operating.correction.domainservice.matcher.impl;

import com.google.common.collect.Lists;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domainservice.matcher.CorrectionHandleMatch;
import com.payermax.operating.correction.integration.config.nacos.model.MatcherRule;
import com.payermax.operating.correction.integration.config.nacos.model.RuleInfo;
import com.payermax.operating.correction.integration.config.nacos.repository.ConfigRepository;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc 差错处理匹配器
 * @date 2023/2/20
 */
@Component
public class CorrectionHandleMatchImpl implements CorrectionHandleMatch {

    @Resource
    private ConfigRepository configRepository;

    private ExpressionParser parser = new SpelExpressionParser();

    @Override
    public List<String> matchRule(String correctionCode, GlobalOrderInfo globalOrderInfo) {
        if (!Optional.ofNullable(globalOrderInfo).map(GlobalOrderInfo::getAssetInfo).map(GlobalOrderInfo.AssetInfo::getTradeType).isPresent()) {
            return Nullable.getNullVal();
        }
        List<MatcherRule> ruleInfo = configRepository.getRuleInfo(correctionCode, globalOrderInfo.getAssetInfo().getTradeType());
        if (CollectionUtils.isEmpty(ruleInfo)) {
            return Nullable.getNullVal();
        }

        List<String> list = Lists.newArrayList();
        for (MatcherRule rule : ruleInfo) {
            String value = StringUtils.defaultString((String) parser.parseExpression(rule.getSpel()).getValue(globalOrderInfo), StringUtils.EMPTY);
            String childCorrectionCode = Optional.ofNullable(rule.getRule().get(value)).map(RuleInfo::getChildCorrectionCode).orElse(StringUtils.EMPTY);
            list.add(childCorrectionCode);
        }
        return list;
    }
}
