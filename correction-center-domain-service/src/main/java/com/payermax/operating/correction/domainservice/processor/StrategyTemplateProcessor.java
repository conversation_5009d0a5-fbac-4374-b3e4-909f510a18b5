package com.payermax.operating.correction.domainservice.processor;

import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;

/**
 * The interface Strategy template processor.
 *
 * <AUTHOR>
 * @desc 策略模板执行器
 * @date 2022 /10/19
 */
public interface StrategyTemplateProcessor {

    /**
     * Do action.
     *
     * @param correctionInfo the correction info
     */
    void doAction(DomainCorrectionInfo correctionInfo);
}
