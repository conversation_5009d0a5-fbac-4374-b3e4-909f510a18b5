package com.payermax.operating.correction.domainservice.repository;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.GlobalOrderReadEvent;
import com.payermax.operating.correction.core.common.enums.OrderDetailType;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.config.nacos.model.CorrectionSystemInfo;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyProcessorInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionBasicInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.ReconcileRedundantDTO;
import com.payermax.operating.correction.integration.rpc.cashiercore.dto.CashierCorePaymentInstanceInfo;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsCoreInfo;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsRuleInfo;
import com.payermax.operating.correction.integration.rpc.productcenter.dto.CashierProductInfo;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * The interface Domain repository.
 *
 * <AUTHOR>
 * @desc 获取资源并对资源做空校验
 * @date 2022/10/8
 */
public interface IDomainRepository {

    /**
     * Gets valid basic info.
     *
     * @param correctionCode the correction code
     * @return thevalid basic info
     */
    CorrectionBasicInfoDTO getValidBasicInfo(String correctionCode);

    /**
     * Gets valid correction no.
     *
     * @param voucherNo voucherNo
     * @param tradeType the trade type
     * @return thevalid correction no
     */
    String getValidCorrectionNo(String voucherNo, TradeType tradeType,String correctionCode);

    String getValidCorrectionNo(String voucherNo, String correctionCode, Money payoutMoney,String account);

    /**
     * Gets valid correction sys.
     *
     * @param correctionCode the correction code
     * @param sysSource      the sys source
     * @return thevalid correction sys
     */
    CorrectionSystemInfo getValidCorrectionSys(String correctionCode, String sysSource);


    /**
     * Gets valid strategy info.
     *
     * @param strategyCode the strategy code
     * @return thevalid strategy info
     */
    StrategyProcessorInfo getValidStrategyInfo(String strategyCode);

    /**
     * Gets valid correction order info.
     *
     * @param correctionNo the correction no
     * @param queryType    the query type
     * @param readEvent    the read event
     * @return thevalid correction order info
     */
    DomainCorrectionInfo getValidCorrectionOrderInfo(String correctionNo, OrderDetailType queryType, GlobalOrderReadEvent readEvent);

    /**
     * Gets valid correction order info.
     *
     * @param correctionCode the correction code
     * @param voucherNo      the voucher no
     * @return thevalid correction order info
     */
    DomainCorrectionInfo getValidCorrectionOrderInfo(String correctionCode, String voucherNo);

    /**
     * Gets valid correction order info dto.
     *
     * @param correctionNo the correction no
     * @return thevalid correction order info dto
     */
    CorrectionOrderInfoDTO getValidCorrectionOrderInfoDto(String correctionNo);

    /**
     * Gets valid correction order info dto.
     *
     * @param correctionCode the correction code
     * @param voucherNo      the voucher no
     * @return thevalid correction order info dto
     */
    CorrectionOrderInfoDTO getValidCorrectionOrderInfoDto(String correctionCode, String voucherNo);

    /**
     * Valid choose strategy code boolean.
     *
     * @param strategyCode   the strategy code
     * @param correctionCode the correction code
     * @param tradeType      the trade type
     * @return theboolean
     */
    Boolean validChooseStrategyCode(String strategyCode,String correctionCode,TradeType tradeType);


    /**
     * Gets valid parent reason basic info.
     *
     * @param correctionCode the correction code
     * @return thevalid parent reason basic info
     */
    CorrectionBasicInfoDTO getValidParentReasonBasicInfo(String correctionCode);

    /**
     * Gets valid cashier product info.
     *
     * @param paymentInstanceInfo the payment instance info
     * @return thevalid cashier product info
     */
    CashierProductInfo getValidCashierProductInfo(CashierCorePaymentInstanceInfo paymentInstanceInfo);

    /**
     * Validation payout rule.
     *
     * @param payoutsRuleInfo the payouts rule info
     * @param payoutsCoreInfo the payouts core info
     */
    void validationPayoutRule(PayoutsRuleInfo payoutsRuleInfo, PayoutsCoreInfo payoutsCoreInfo);

    /**
     * Valid operation code boolean.
     *
     * @param operationCorrectionCode the operation correction code
     * @param parentCorrectionCode    the parent correction code
     * @return theboolean
     */
    void validOperationCode(String operationCorrectionCode,String parentCorrectionCode);

    /**
     * Update correction irrelevant info.
     *
     * @param domainCorrectionInfo the domain correction info
     */
    void updateCorrectionIrrelevantInfo(DomainCorrectionInfo domainCorrectionInfo);

    /**
     * Get system infos list.
     *
     * @param correctionCode the correction code
     * @param sysSources     the sys sources
     * @return the list
     */
    List<CorrectionSystemInfo>getSystemInfos(String correctionCode,String sysSources);

    /**
     * Gets global order info.
     *
     * @param voucherInfo the voucher info
     * @param readEvent   the read event
     * @return the global order info
     */
    GlobalOrderInfo getGlobalOrderInfo(VoucherInfo voucherInfo, GlobalOrderReadEvent readEvent);

    /**
     * Gets all register handler reason.
     *
     * @return the all register handler reason
     */
    List<String> getAllRegisterHandlerReason();

    /**
     * refreshParentBasicInfoCache
     */
    void refreshParentBasicInfoCache();

    /**
     * handlerResultNotify
     *
     * @param correctionInfo correctionInfo
     */
    void handlerResultNotify(DomainCorrectionInfo correctionInfo);

    List<CorrectionOrderInfoDTO> getCorrectionOrderInfos(String correctionCode,CorrectionOrderInfoDTO checkingOrderInfo, ReconcileRedundantDTO checkingChannelRedundantInfo, DCVoucherType dcVoucherType);

    CorrectionBasicInfoDTO getValidBasicInfo(String parentCorrectionCode, TradeType tradeType);

    /**
     * handlerIntersectionMark
     * @param correctionCode 差错原因
     * @param tradeType 交易类型
     */
    Boolean handlerIntersectionMark(String correctionCode,TradeType tradeType);


    /**
     *
     * @param templateId
     * @return
     */
    List<String> queryDataCenterOrderNo(String templateId, String fieldName);
}
