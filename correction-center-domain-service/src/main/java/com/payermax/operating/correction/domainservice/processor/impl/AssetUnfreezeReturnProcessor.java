package com.payermax.operating.correction.domainservice.processor.impl;

import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.processor.utils.OrderStatusUtils;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.enums.AssetPayOrderStatus;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.queue.MqResultNotifyRepository;
import com.payermax.operating.correction.integration.queue.dto.ResultNotifyDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 解冻并返回处理器
 * @date 2022/10/17
 */
@Component(value = "assetUnfreezeReturnProcessor")
@Slf4j
@Deprecated
public class AssetUnfreezeReturnProcessor implements Processor {
    @Resource
    private MqResultNotifyRepository mqResultNotifyRepository;

    @Override
    public void preCheck(DomainCorrectionInfo correctionInfo) {
        String payOrderStatus = OrderStatusUtils.assetExchangePayOrderStatus(correctionInfo.getGlobalOrderInfo());
        AssertUtil.isTrue(AssetPayOrderStatus.PROCESSING == AssetPayOrderStatus.getByVal(payOrderStatus), ReturnCode.PROCESSOR_VALID_EXCEPTION.getCode(), ReturnMsg.ASSET_STATUS_MATCH_FAIL);

    }

    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {
        ResultNotifyDTO.ResultNotify resultNotify = ResultNotifyDTO.ResultNotify.builder()
                .status(CommonStatusEnum.FAILURE.name())
                .voucherNo(correctionInfo.getOriVoucherInfo().getVoucherNo())
                .voucherType(correctionInfo.getOriVoucherInfo().getVoucherType().name())
                .tag(CorrectionConstant.TOPIC_TAG_ASSET)
                .build();
        mqResultNotifyRepository.handlerResultNotify(new ResultNotifyDTO(resultNotify));
        return new BaseResProcess(correctionInfo.getOriVoucherInfo(), Nullable.getNullVal());
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo globalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        String payOrderStatus = OrderStatusUtils.assetExchangePayOrderStatus(globalOrderInfo);
        return AssetPayOrderStatus.FAILED == AssetPayOrderStatus.getByVal(payOrderStatus) ? CommonStatusEnum.SUCCESS : CommonStatusEnum.PENDING;
    }
}
