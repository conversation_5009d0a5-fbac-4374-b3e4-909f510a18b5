package com.payermax.operating.correction.domainservice.machine;

import com.alibaba.cola.statemachine.Action;
import com.alibaba.cola.statemachine.Condition;
import com.alibaba.cola.statemachine.StateMachine;
import com.alibaba.cola.statemachine.StateMachineFactory;
import com.alibaba.cola.statemachine.builder.StateMachineBuilder;
import com.alibaba.cola.statemachine.builder.StateMachineBuilderFactory;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.Publisher;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.utils.EventCenter;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.RetryInfo;
import com.payermax.operating.correction.domain.enums.CorrectionEvent;
import com.payermax.operating.correction.domain.enums.ProcessStatusEnum;
import com.payermax.operating.correction.domainservice.events.ResultNotifyEvent;
import com.payermax.operating.correction.domainservice.machine.context.CorrectionOrderStateMachineContext;
import com.payermax.operating.correction.domainservice.processor.ProcessorHolder;
import com.payermax.operating.correction.domainservice.processor.StrategyTemplateProcessor;
import com.payermax.operating.correction.domainservice.utils.CommonDomainUtil;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.persistence.kvstore.repository.KVRepository;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionHandlerUniInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc 差错状态机
 * @date 2022/10/29
 */
@Slf4j
@Component
public class CorrectionOrderStateMachine {
    private StateMachine<ProcessStatusEnum, CorrectionEvent, CorrectionOrderStateMachineContext> stateMachine;

    @PostConstruct
    public void init() {
        stateMachine = buildTradeStateMachine();
    }

    @Resource
    private CorrectionRepository correctionRepository;

    @Resource
    private ProcessorHolder processorHolder;

    @Resource(name = "bizAsyncExecutor")
    private ThreadPoolTaskExecutor asyncExecutor;

    @Resource
    private StrategyTemplateProcessor templateProcessor;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private KVRepository kvRepository;

    private static final String MACHINE_ID = "CORRECTION_STATE_MACHINE";

    public void sendEvent(ProcessStatusEnum processStatus, CorrectionEvent correctionEvent, CorrectionOrderStateMachineContext correctionOrderStateMachineContext) {
        stateMachine.fireEvent(processStatus, correctionEvent, correctionOrderStateMachineContext);
    }

    private StateMachine<ProcessStatusEnum, CorrectionEvent, CorrectionOrderStateMachineContext> buildTradeStateMachine() {
        StateMachineBuilder<ProcessStatusEnum, CorrectionEvent, CorrectionOrderStateMachineContext> builder = StateMachineBuilderFactory.create();
        //选择策略为更为:处理中【单独金融交换】
        builder.externalTransitions()
                .fromAmong(ProcessStatusEnum.PROCESSED,ProcessStatusEnum.VERIFICATION,ProcessStatusEnum.REVIEWED)
                .to(ProcessStatusEnum.PENDING)
                .on(CorrectionEvent.EVENT_CHOOSE)
                .when(checkCorrectionStatus(ProcessStatusEnum.PROCESSED,ProcessStatusEnum.VERIFICATION,ProcessStatusEnum.REVIEWED))
                .perform(updateAccept());
        //选择策略更为:已处理待审核【非金融交换推送，需要走审核流程】
        builder.externalTransitions()
                .fromAmong(ProcessStatusEnum.PROCESSED, ProcessStatusEnum.VERIFICATION, ProcessStatusEnum.REVIEWED)
                .to(ProcessStatusEnum.REVIEWING)
                .on(CorrectionEvent.EVENT_CHOOSE_AND_REVIEWED)
                .when(checkCorrectionStatus(ProcessStatusEnum.PROCESSED, ProcessStatusEnum.VERIFICATION, ProcessStatusEnum.REVIEWED))
                .perform(updateReviewing());
        //选择策略更为:转交待处理【转交给他人】
        builder.externalTransitions()
                .fromAmong(ProcessStatusEnum.PROCESSED)
                .to(ProcessStatusEnum.REVIEWED)
                .on(CorrectionEvent.EVENT_TRANSFER)
                .when(checkCorrectionStatus(ProcessStatusEnum.PROCESSED))
                .perform(eventTransfer());
        //选择策略更为:核实待处理【自己转交给自己】
        builder.externalTransitions()
                .fromAmong(ProcessStatusEnum.PROCESSED)
                .to(ProcessStatusEnum.VERIFICATION)
                .on(CorrectionEvent.EVENT_VERIFICATION)
                .when(checkCorrectionStatus(ProcessStatusEnum.PROCESSED))
                .perform(eventTransfer());

        //事件多次转移
        builder.externalTransitions()
                .fromAmong(ProcessStatusEnum.REVIEWED, ProcessStatusEnum.VERIFICATION)
                .to(ProcessStatusEnum.REVIEWED)
                .on(CorrectionEvent.EVENT_TRANSFER)
                .when(checkCorrectionStatus(ProcessStatusEnum.REVIEWED,ProcessStatusEnum.VERIFICATION))
                .perform(eventTransfer());

        //审批拒绝更新为待处理
        builder.externalTransition()
                .from(ProcessStatusEnum.REVIEWING)
                .to(ProcessStatusEnum.PROCESSED)
                .on(CorrectionEvent.APPROVAL_REJECT)
                .when(checkCorrectionStatus(ProcessStatusEnum.REVIEWING))
                .perform(updateReject());

        //审批通过更新为处理中
        builder.externalTransition()
                .from(ProcessStatusEnum.REVIEWING)
                .to(ProcessStatusEnum.PENDING)
                .on(CorrectionEvent.APPROVAL_AGREE)
                .when(checkCorrectionStatus(ProcessStatusEnum.REVIEWING))
                .perform(updateAgreement());

        //更新为处理成功
        builder.externalTransition()
                .from(ProcessStatusEnum.PENDING)
                .to(ProcessStatusEnum.SUCCESS)
                .on(CorrectionEvent.HANDLER_SUCCESS)
                .when(checkCorrectionStatus(ProcessStatusEnum.PENDING))
                .perform(updateSuccess());
        //更新为待处理
        builder.externalTransition()
                .from(ProcessStatusEnum.SUCCESS)
                .to(ProcessStatusEnum.PROCESSED)
                .on(CorrectionEvent.NOTIFY_FAILED)
                .when(checkCorrectionStatus(ProcessStatusEnum.SUCCESS))
                .perform(notifyFailed());
        //失败后重试
        builder.externalTransition()
                .from(ProcessStatusEnum.PENDING)
                .to(ProcessStatusEnum.PROCESSED)
                .on(CorrectionEvent.HANDLER_FAILED)
                .when(checkCorrectionStatus(ProcessStatusEnum.PENDING))
                .perform(handlerFailed());
        //外部处理成功 或差错登记处理
        builder.externalTransition()
                .from(ProcessStatusEnum.PROCESSED)
                .to(ProcessStatusEnum.SUCCESS)
                .on(CorrectionEvent.REGISTRATION_COMPLETE)
                .when(checkCorrectionStatus(ProcessStatusEnum.PROCESSED))
                .perform(externalHandleSuccess());


        builder.build(MACHINE_ID);
        StateMachine<ProcessStatusEnum, CorrectionEvent, CorrectionOrderStateMachineContext> correctionStateMachine = StateMachineFactory.get(MACHINE_ID);
        correctionStateMachine.showStateMachine();
        return correctionStateMachine;
    }

    private boolean matchCurrentStatus(DomainCorrectionInfo orderInfo, ProcessStatusEnum... processStatus) {
        for (ProcessStatusEnum currentStatus : processStatus) {
            if (orderInfo.getProcessStatus() == currentStatus) {
                return true;
            }
        }
        return false;
    }

    /**
     * 状态满足条件
     *
     * @param processStatus
     * @return
     */
    private Condition<CorrectionOrderStateMachineContext> checkCorrectionStatus(ProcessStatusEnum... processStatus) {
        return ctx -> matchCurrentStatus(ctx.getCorrectionOrderInfo(), processStatus);
    }

    /**
     * 事件转移
     *
     * @return
     */
    private Action<ProcessStatusEnum, CorrectionEvent, CorrectionOrderStateMachineContext> eventTransfer() {
        return (from, to, event, ctx) -> {
            boolean result = correctionRepository.restoreStatus(ctx.getCorrectionNo(), from.name(), to.name());
            AssertUtil.isTrue(result, ReturnCode.STATE_MACHINE_PROCESS_FAIL.getCode(), ReturnCode.STATE_MACHINE_PROCESS_FAIL.getMsg());

            ctx.updateCurrentTrade(to);
            DomainCorrectionInfo info = ctx.getCorrectionOrderInfo();

            CorrectionOrderHandlerRecordDTO handlerRecordDTO = this.buildHandlerRecord(info.getCorrectionNo(), CommonDomainUtil.getStrategyCode(info.getStrategyProcessor()),
                    event, info.getOperator(), CommonStatusEnum.SUCCESS, StringUtils.EMPTY);
            //唯一标识
            handlerRecordDTO.setProcessResult(info.getOperator() + Symbols.UNDERLINE + info.getReviewer());
            handlerRecordDTO.getHandlerInfo().setProcessRequest(CommonDomainUtil.getUUID());
            correctionRepository.storeOrderHandlerRecordInfo(handlerRecordDTO);
        };
    }

    /**
     * 更新为已受理
     *
     * @return
     */
    private Action<ProcessStatusEnum, CorrectionEvent, CorrectionOrderStateMachineContext> updateAccept() {
        return (from, to, event, ctx) -> {
            boolean result = correctionRepository.restoreStatus(ctx.getCorrectionNo(), from.name(), to.name());
            AssertUtil.isTrue(result, ReturnCode.STATE_MACHINE_PROCESS_FAIL.getCode(), ReturnCode.STATE_MACHINE_PROCESS_FAIL.getMsg());
            ctx.updateCurrentTrade(to);
            DomainCorrectionInfo info = ctx.getCorrectionOrderInfo();
            correctionRepository.storeOrderHandlerRecordInfo(this.buildHandlerRecord(info.getCorrectionNo(),
                    CommonDomainUtil.getStrategyCode(info.getStrategyProcessor()), event, info.getOperator(), CommonStatusEnum.SUCCESS, StringUtils.EMPTY));
            //开始执行策略
            info.setRetryInfo(new RetryInfo());
            asyncExecutor.execute(() -> templateProcessor.doAction(info));
        };
    }

    private Action<ProcessStatusEnum, CorrectionEvent, CorrectionOrderStateMachineContext> updateReviewing() {
        return (from, to, event, ctx) -> {
            boolean result = correctionRepository.restoreStatus(ctx.getCorrectionNo(), from.name(), to.name());
            AssertUtil.isTrue(result, ReturnCode.STATE_MACHINE_PROCESS_FAIL.getCode(), ReturnCode.STATE_MACHINE_PROCESS_FAIL.getMsg());
            ctx.updateCurrentTrade(to);
            DomainCorrectionInfo info = ctx.getCorrectionOrderInfo();
            correctionRepository.storeOrderHandlerRecordInfo(this.buildHandlerRecord(info.getCorrectionNo(),
                    CommonDomainUtil.getStrategyCode(info.getStrategyProcessor()), event, info.getOperator(), CommonStatusEnum.SUCCESS, StringUtils.EMPTY));
        };
    }


    /**
     * 更新审批拒绝
     *
     * @return
     */
    private Action<ProcessStatusEnum, CorrectionEvent, CorrectionOrderStateMachineContext> updateReject() {
        return (from, to, event, ctx) -> {
            boolean result = correctionRepository.restoreStatus(ctx.getCorrectionNo(), from.name(), to.name());
            AssertUtil.isTrue(result, ReturnCode.STATE_MACHINE_PROCESS_FAIL.getCode(), ReturnCode.STATE_MACHINE_PROCESS_FAIL.getMsg());
            ctx.updateCurrentTrade(to);
            DomainCorrectionInfo info = ctx.getCorrectionOrderInfo();
            //将之前选择成功的记录废弃
            String strategyCode = CommonDomainUtil.getStrategyCode(info.getStrategyProcessor());
            CorrectionOrderHandlerRecordDTO historyHandlerRecord = this.buildHandlerRecord(info.getCorrectionNo(), strategyCode, null, info.getOperator(), CommonStatusEnum.SUCCESS, null);
            String processRequest = CommonDomainUtil.getUUID();
            historyHandlerRecord.getHandlerInfo().setProcessRequest(processRequest);
            correctionRepository.patchModifyHandlerRecordDiscard(historyHandlerRecord);
            //添加废弃的操作记录
            CorrectionOrderHandlerRecordDTO currentHandlerRecord = this.buildHandlerRecord(info.getCorrectionNo(), strategyCode, event, info.getOperator(), CommonStatusEnum.DISCARD, info.getApprovalCommit());
            currentHandlerRecord.getHandlerInfo().setProcessRequest(processRequest);
            correctionRepository.storeOrderHandlerRecordInfo(currentHandlerRecord);
        };
    }

    /**
     * 更新审批通过
     *
     * @return
     */
    private Action<ProcessStatusEnum, CorrectionEvent, CorrectionOrderStateMachineContext> updateAgreement() {
        return (from, to, event, ctx) -> {
            boolean result = correctionRepository.restoreStatus(ctx.getCorrectionNo(), from.name(), to.name());
            AssertUtil.isTrue(result, ReturnCode.STATE_MACHINE_PROCESS_FAIL.getCode(), ReturnCode.STATE_MACHINE_PROCESS_FAIL.getMsg());
            ctx.updateCurrentTrade(to);
            DomainCorrectionInfo info = ctx.getCorrectionOrderInfo();
            info.setRetryInfo(new RetryInfo());
            //添加操作记录
            correctionRepository.storeOrderHandlerRecordInfo(this.buildHandlerRecord(info.getCorrectionNo(),
                    CommonDomainUtil.getStrategyCode(info.getStrategyProcessor()), event, info.getOperator(), CommonStatusEnum.SUCCESS, info.getApprovalCommit()));
            //开始执行策略
            asyncExecutor.execute(() -> templateProcessor.doAction(info));
        };
    }

    /**
     * 更新差错处理成功
     *
     * @return
     */
    private Action<ProcessStatusEnum, CorrectionEvent, CorrectionOrderStateMachineContext> updateSuccess() {
        return (from, to, event, ctx) -> {
            boolean result = correctionRepository.restoreStatus(ctx.getCorrectionNo(), from.name(), to.name(), new Date());
            AssertUtil.isTrue(result, ReturnCode.STATE_MACHINE_PROCESS_FAIL.getCode(), ReturnCode.STATE_MACHINE_PROCESS_FAIL.getMsg());
            ctx.updateCurrentTrade(to);
            //执行器全部处理完毕则结果通知
            DomainCorrectionInfo correctionInfo = ctx.getCorrectionOrderInfo();
            correctionInfo.getSystemInfo().forEach(sysSource -> {
                EventCenter.publish(Publisher.HANDLE_RESULT.name(), new ResultNotifyEvent(correctionInfo.getOriVoucherInfo(), sysSource, CommonDomainUtil.getStrategyCode(correctionInfo.getStrategyProcessor()), correctionInfo.getMemo(),correctionInfo.getRedundantInfo()));
            });
            kvRepository.removeGlobalOrder(correctionInfo.getOriVoucherInfo());
        };
    }

    /**
     * 更新差错处理失败
     *
     * @return
     */
    private Action<ProcessStatusEnum, CorrectionEvent, CorrectionOrderStateMachineContext> notifyFailed() {
        return (from, to, event, ctx) -> {
            transactionTemplate.execute((status) -> {
                boolean result = correctionRepository.restoreStatus(ctx.getCorrectionNo(), from.name(), to.name());
                AssertUtil.isTrue(result, ReturnCode.STATE_MACHINE_PROCESS_FAIL.getCode(), ReturnCode.STATE_MACHINE_PROCESS_FAIL.getMsg());
                ctx.updateCurrentTrade(to);
                DomainCorrectionInfo info = ctx.getCorrectionOrderInfo();
                //将此次的所有(策略，审批，处理)记录均更新为废弃，并填充处理请求
                boolean patchUpdate = correctionRepository.patchModifyHandlerRecordDiscard(info.getCurrentHandlerRecord());
                AssertUtil.isTrue(patchUpdate, ReturnCode.STATE_MACHINE_PROCESS_FAIL.getCode(), ReturnCode.STATE_MACHINE_PROCESS_FAIL.getMsg());
                return Boolean.TRUE;
            });
        };
    }

    /**
     * 失败后重试
     *
     * @return
     */
    private Action<ProcessStatusEnum, CorrectionEvent, CorrectionOrderStateMachineContext> handlerFailed() {
        return (from, to, event, ctx) -> {
            transactionTemplate.execute((status) -> {
                boolean result = correctionRepository.restoreStatus(ctx.getCorrectionNo(), from.name(), to.name());
                AssertUtil.isTrue(result, ReturnCode.STATE_MACHINE_PROCESS_FAIL.getCode(), ReturnCode.STATE_MACHINE_PROCESS_FAIL.getMsg());
                ctx.updateCurrentTrade(to);
                DomainCorrectionInfo info = ctx.getCorrectionOrderInfo();

                //将此次的所有(策略，审批，处理)记录均更新为废弃，并填充处理请求
                boolean patchUpdate = correctionRepository.patchModifyHandlerRecordDiscard(info.getCurrentHandlerRecord());
                AssertUtil.isTrue(patchUpdate, ReturnCode.STATE_MACHINE_PROCESS_FAIL.getCode(), ReturnCode.STATE_MACHINE_PROCESS_FAIL.getMsg());

                //将之前记录策略更新为废弃
                correctionRepository.restoreHandlerStatus(info.getCurrentHandlerRecord().getHandlerInfo(), CommonStatusEnum.PENDING.name(),
                        CommonStatusEnum.DISCARD.name());

                return Boolean.TRUE;
            });
        };
    }

    private CorrectionOrderHandlerRecordDTO buildHandlerRecord(String correctionNo, String strategyCode, CorrectionEvent event, String operator, CommonStatusEnum status, String desc) {
        CorrectionOrderHandlerRecordDTO orderHandlerRecord = new CorrectionOrderHandlerRecordDTO();
        orderHandlerRecord.setHandlerInfo(new CorrectionHandlerUniInfo(correctionNo, strategyCode, Optional.ofNullable(event).map(Enum::name).orElse(Nullable.getNullVal())));
        orderHandlerRecord.setStatus(status);
        orderHandlerRecord.setProcessResult(desc);
        orderHandlerRecord.setOpBasicInfo(new OperationBasicInfo(operator));
        return orderHandlerRecord;
    }

    /**
     * 更新差错处理成功
     *
     * @return
     */
    private Action<ProcessStatusEnum, CorrectionEvent, CorrectionOrderStateMachineContext> externalHandleSuccess() {
        return (from, to, event, ctx) -> {
            transactionTemplate.execute((status) -> {
                boolean result = correctionRepository.restoreStatus(ctx.getCorrectionNo(), from.name(), to.name(), new Date());
                AssertUtil.isTrue(result, ReturnCode.STATE_MACHINE_PROCESS_FAIL.getCode(), ReturnCode.STATE_MACHINE_PROCESS_FAIL.getMsg());
                DomainCorrectionInfo info = ctx.getCorrectionOrderInfo();
                correctionRepository.storeOrderHandlerRecordInfo(this.buildHandlerRecord(info.getCorrectionNo(),
                        CorrectionConstant.REGISTRATION_PROCESS, event, info.getOperator(), CommonStatusEnum.SUCCESS, StringUtils.EMPTY));
                ctx.updateCurrentTrade(to);
                //执行器全部处理完毕则结果通知
                info.getSystemInfo().forEach(sysSource -> {
                    EventCenter.publish(Publisher.HANDLE_RESULT.name(), new ResultNotifyEvent(info.getOriVoucherInfo(), sysSource, CommonDomainUtil.getStrategyCode(info.getStrategyProcessor()), info.getMemo(),info.getRedundantInfo()));
                });
                return Boolean.TRUE;
            });
        };
    }

}
