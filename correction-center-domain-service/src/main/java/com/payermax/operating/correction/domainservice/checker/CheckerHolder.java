package com.payermax.operating.correction.domainservice.checker;

import com.google.common.collect.Maps;
import com.payermax.operating.correction.core.common.utils.AppContextHolder;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;

/**
 * <AUTHOR>
 * @desc checker容器
 * @date 2022/10/14
 */
@Component
public class CheckerHolder {
    private Map<String, Checker> checkerMap = Maps.newConcurrentMap();

    @Resource
    private ApplicationContext applicationContext;


    @PostConstruct
    public void init() {
        Map<String, Checker> beans = AppContextHolder.getBeansByType(applicationContext, Checker.class);
        checkerMap.putAll(beans);
    }

    public Checker getChecker(String beanName) {
        return checkerMap.get(beanName);
    }
}
