package com.payermax.operating.correction.domainservice.message;

import com.alibaba.fastjson.JSONObject;
import com.payermax.infra.ionia.rocketmq.handler.BaseMqMessageListener;
import com.payermax.operating.correction.domainservice.service.DomainBizService;
import com.payermax.operating.correction.integration.queue.dto.CorrectionDelayHandlerMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 差错事件接收消息消费者
 * @date 2022/9/27
 */
@RocketMQMessageListener(
        topic = "${correction.topic.delay.handler}",
        consumerGroup = "correction-center-delay-consumer-group",
        consumeThreadNumber = 5
)
@Component
@Slf4j
public class CorrectionDelayHandlerConsumerImpl extends BaseMqMessageListener<CorrectionDelayHandlerMessage> implements RocketMQListener<CorrectionDelayHandlerMessage> {

    @Resource
    private DomainBizService domainBizService;

    @Override
    protected void handleMessage(CorrectionDelayHandlerMessage msg) throws Exception {
        log.info("CorrectionDelayHandlerConsumerImpl handleMessage,msg : [{}]", msg.getCorrectionNo());
        domainBizService.execStrategy(msg.getCorrectionNo(),msg.getRetry());
    }

    @Override
    protected void overMaxRetryTimesMessage(CorrectionDelayHandlerMessage msg) {
        log.error("CorrectionDelayHandlerConsumerImpl consumer overMaxRetryTimesMessage, msg:[{}]]", JSONObject.toJSONString(msg));
    }

    @Override
    protected int maxRetryTimes() {
        return 2;
    }

    @Override
    public void onMessage(CorrectionDelayHandlerMessage eventMessageInfo) {
        super.dispatchMessage(eventMessageInfo);
    }
}
