package com.payermax.operating.correction.domainservice.checker;

import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domainservice.matcher.CorrectionHandleMatch;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 抽象校验器
 * @date 2023/4/17
 */
@Component(value = CorrectionConstant.ABSTRACT_CHECKER)
public class AbstractChecker implements Checker{

    @Resource
    private CorrectionHandleMatch handleMatch;

    @Override
    public void check(CorrectionOrderInfoDTO correctionInfo, GlobalOrderInfo globalOrderInfo, OperationDomainCorrectionInfo info) {
        List<String> childCorrectionCodeList = handleMatch.matchRule(correctionInfo.getBaseInfo().getCorrectionCode(), globalOrderInfo);
        if (CollectionUtils.isNotEmpty(childCorrectionCodeList)) {
            AssertUtil.isTrue(childCorrectionCodeList.contains(correctionInfo.getOperationCorrectionCode()), ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.HANDLER_CORRECTION_CODE_INVALID);
        }
    }
}
