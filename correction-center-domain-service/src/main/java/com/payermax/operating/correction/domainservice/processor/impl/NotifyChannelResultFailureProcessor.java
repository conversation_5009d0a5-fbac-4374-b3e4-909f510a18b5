package com.payermax.operating.correction.domainservice.processor.impl;

import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.processor.utils.OrderStatusUtils;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.enums.AssetPayOrderStatus;
import com.payermax.operating.correction.integration.enums.ChannelOrderStatus;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.financial.dto.ChannelResultNotifyInfo;
import com.payermax.operating.correction.integration.rpc.financial.enums.ChannelNotifyType;
import com.payermax.operating.correction.integration.rpc.financial.repository.ChannelExchangeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 差错通知渠道失败
 * @date 2022/12/1
 */
@Component(value = "channelNotifyFailureProcessor")
@Slf4j
public class NotifyChannelResultFailureProcessor implements Processor {

    @Resource
    private ChannelExchangeRepository channelExchangeRepository;

    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {
        ChannelResultNotifyInfo resultNotifyInfo = ChannelResultNotifyInfo.builder()
                .channelCommitNo(correctionInfo.getOriVoucherInfo().getVoucherNo())
                .notifyType(ChannelNotifyType.REFUND)
                .processStatus(CommonStatusEnum.FAILURE)
                .payAmount(correctionInfo.getPayTotalMoney())
                .thirdOrderNo(correctionInfo.getCorrectionNo())
                .respCode(correctionInfo.getRedundantInfo().getErrorCode())
                .respMsg(correctionInfo.getRedundantInfo().getErrorMsg())
                .build();
        return channelExchangeRepository.financialResultNotify(resultNotifyInfo);
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo globalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        ChannelOrderStatus channelOrderStatus = ChannelOrderStatus.getChannelByVal(OrderStatusUtils.channelCommitStatus(globalOrderInfo));
        AssetPayOrderStatus assetPayOrderStatus = AssetPayOrderStatus.getByVal(OrderStatusUtils.assetExchangePayOrderStatus(globalOrderInfo));
        return channelOrderStatus == ChannelOrderStatus.FAILED && AssetPayOrderStatus.FAILED == assetPayOrderStatus ? CommonStatusEnum.SUCCESS : CommonStatusEnum.PENDING;
    }

}
