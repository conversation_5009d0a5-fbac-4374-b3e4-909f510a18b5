package com.payermax.operating.correction.domainservice.processor.impl;

import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.processor.assembler.ProcessorAssembler;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.paylink.PayLinkReository;
import com.payermax.operating.correction.integration.rpc.paylink.dto.PayLinkPatchNoticeResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * @description: 链接支付补通知处理器
 * @author: WangTao
 * @create: 2025-03-18 17:19
 **/
@Component(value = "patchPayLinkNoticeProcessor")
@Slf4j
public class PatchPayLinkNoticeProcessor implements Processor {

    @Autowired
    private PayLinkReository payLinkReository;

    @Resource
    private ProcessorAssembler processorAssembler;


    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {
        String productCode = Optional.ofNullable(correctionInfo)
                .map(DomainCorrectionInfo::getGlobalOrderInfo)
                .map(GlobalOrderInfo::getProductCode)
                .orElse(null);
        if(StringUtils.isBlank(productCode) || !CorrectionConstant.PAYLINK_PRODUCT_CODE.equals(productCode)){
            return BaseResProcess.builder()
                    .status(CommonStatusEnum.SUCCESS)
                    .resVoucherInfo(new VoucherInfo())
                    .build();
        }

        PayLinkPatchNoticeResponse payLinkPatchNoticeResponse = payLinkReository.patchNotice(processorAssembler.toPayLinkPatchNoticeRequest(correctionInfo));

        return BaseResProcess.builder()
                .status(CommonStatusEnum.SUCCESS.name().equals(payLinkPatchNoticeResponse.getStatus()) ? CommonStatusEnum.SUCCESS : CommonStatusEnum.FAILURE)
                .resVoucherInfo(new VoucherInfo())
                .build();
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo correctionGlobalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        return ObjectUtils.defaultIfNull(correctionHandlerRecordInfo.getStatus(), CommonStatusEnum.PENDING);
    }
}
