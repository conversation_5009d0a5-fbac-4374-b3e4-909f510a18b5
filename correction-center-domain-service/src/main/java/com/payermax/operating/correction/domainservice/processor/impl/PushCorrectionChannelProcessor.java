package com.payermax.operating.correction.domainservice.processor.impl;

import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.processor.utils.OrderStatusUtils;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.enums.ChannelOrderStatus;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.queue.MqResultNotifyRepository;
import com.payermax.operating.correction.integration.queue.dto.ResultNotifyDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @desc 推送差错渠道处理器
 * @date 2022/12/14
 */
@Component(value = "pushCorrectionChannelProcessor")
@Slf4j
public class PushCorrectionChannelProcessor implements Processor {

    @Resource
    private MqResultNotifyRepository mqResultNotifyRepository;

    @Override
    public void preCheck(DomainCorrectionInfo correctionInfo) {
        //渠道请求单信息必须是请求中
        String channelRequestStatus = OrderStatusUtils.channelRequestStatus(correctionInfo.getGlobalOrderInfo());
        AssertUtil.isTrue(ChannelOrderStatus.getChannelByVal(channelRequestStatus) == ChannelOrderStatus.PENDING, ReturnCode.PROCESSOR_VALID_EXCEPTION.getCode(), ReturnMsg.CHANNEL_REQUEST_STATUS_MATCH_FAIL);
    }

    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {
        ResultNotifyDTO.ResultNotify resultNotify = ResultNotifyDTO.ResultNotify.builder()
                .status(CommonStatusEnum.PENDING.name())
                .voucherNo(correctionInfo.getOriVoucherInfo().getVoucherNo())
                .voucherType(correctionInfo.getOriVoucherInfo().getVoucherType().name())
                .correctionNo(correctionInfo.getCorrectionNo())
                .strategyProcess(CorrectionConstant.CORRECTION_CHANNEL_PROCESS)
                .detailCode(correctionInfo.getRedundantInfo().getErrorCode())
                .detailMsg(correctionInfo.getRedundantInfo().getErrorMsg())
                .tag(CorrectionConstant.TOPIC_TAG_CHANNEL)
                .build();
        mqResultNotifyRepository.handlerResultNotify(new ResultNotifyDTO(resultNotify));
        return new BaseResProcess(correctionInfo.getOriVoucherInfo(), Nullable.getNullVal());
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo globalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        String payOrderStatus = OrderStatusUtils.channelRequestStatus(globalOrderInfo);
        ChannelOrderStatus orderStatus = ChannelOrderStatus.getChannelByVal(payOrderStatus);
        return ChannelOrderStatus.SUCCESS == orderStatus || ChannelOrderStatus.FAILED == orderStatus ? CommonStatusEnum.SUCCESS : CommonStatusEnum.PENDING;
    }

}
