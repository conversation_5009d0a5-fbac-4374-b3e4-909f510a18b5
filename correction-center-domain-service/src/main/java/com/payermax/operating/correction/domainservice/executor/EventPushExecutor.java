package com.payermax.operating.correction.domainservice.executor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 事件处理器异步执行器
 *
 * <AUTHOR>
 * @date 2022/6/24 14:09
 */
@Configuration
@Slf4j
public class EventPushExecutor {

    /**
     * 事件处理器异步执行器
     *
     * @return ThreadPoolTaskExecutor
     */
    @Bean(name = "eventPushHandlerExecutor")
    public ThreadPoolTaskExecutor eventPushHandlerExecutor() {

        log.info("---------- [事件推送执行线程池开始加载] ----------");

        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolMdcExecutor();

        /* 核心线程池大小 */
        threadPoolTaskExecutor.setCorePoolSize(20);

        /* 最大线程数 */
        threadPoolTaskExecutor.setMaxPoolSize(500);

        /* 队列容量 */
        threadPoolTaskExecutor.setQueueCapacity(1200);

        /* 活跃时间 */
        threadPoolTaskExecutor.setKeepAliveSeconds(60);

        /* 设置线程池前缀 */
        threadPoolTaskExecutor.setThreadNamePrefix("Event-push-Async-Executor-");

        /* 设置执行饱和后拒绝策略 */
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());

        /* 初始化 */
        threadPoolTaskExecutor.initialize();

        threadPoolTaskExecutor.getThreadPoolExecutor().prestartAllCoreThreads();

        log.info("---------- [事件推送执行线程池加载完成] ----------");

        return threadPoolTaskExecutor;
    }
}
