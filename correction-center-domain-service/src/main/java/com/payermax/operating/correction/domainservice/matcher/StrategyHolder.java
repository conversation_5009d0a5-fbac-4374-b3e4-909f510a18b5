package com.payermax.operating.correction.domainservice.matcher;

import com.google.common.collect.Maps;
import com.payermax.operating.correction.core.common.enums.StrategyMode;
import com.payermax.operating.correction.core.common.utils.AppContextHolder;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 策略匹配器
 * @date 2023/8/30
 */
@Component
public class StrategyHolder {

    private final Map<String, StrategyMatcher> strategyMap = Maps.newConcurrentMap();

    @Resource
    private ApplicationContext applicationContext;


    @PostConstruct
    public void init() {
        Map<String, StrategyMatcher> beans = AppContextHolder.getBeansByType(applicationContext, StrategyMatcher.class);
        strategyMap.putAll(beans);
    }

    public StrategyMatcher getProcess(StrategyMode strategyMode, String defaultBean) {
        StrategyMatcher strategyMatcher = strategyMap.get(strategyMode.getBeanName());
        if (Objects.isNull(strategyMatcher)) {
            strategyMatcher = strategyMap.get(defaultBean);
        }
        return strategyMatcher;
    }
}
