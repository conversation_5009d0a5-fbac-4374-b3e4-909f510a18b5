package com.payermax.operating.correction.domainservice.matcher.impl;

import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.AutoHandleCorrectionOrderDTO;
import com.payermax.operating.correction.domain.dto.CorrectionAutoStrategyCodeHandlerDTO;
import com.payermax.operating.correction.domainservice.matcher.AbstractMatcher;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyExpressionInfo;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyRule;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.payouts.repository.PayoutsRepository;
import com.payermax.operating.correction.integration.rpc.reconcile.ReconcileRepository;
import com.payermax.operating.correction.integration.rpc.reconcile.dto.ReconcileInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 出款 先失败，对账发现再退票
 */
@Slf4j
@Component(value = CorrectionConstant.PAYOUT_BOUNCEBACK_BY_RECONCILE_MATCHER)
public class ReconcilePayoutFailedAndRefundMatcher extends AbstractMatcher {

    @Resource
    private IDomainRepository domainRepository;

    @Resource
    private PayoutsRepository payoutsRepository;

    @Resource
    private ReconcileRepository reconcileRepository;

    @Override
    public List<CorrectionAutoStrategyCodeHandlerDTO> matchAutoHandlerStrategyInfo(StrategyRule strategyRule, AutoHandleCorrectionOrderDTO autoHandleCorrectionOrderDTO, GlobalOrderInfo globalOrderInfo) {
        List<StrategyExpressionInfo> strategyExpressionInfoList = strategyRule.getStrategyExpressions().get(CorrectionConstant.DEFAULT);
        //如果没配置规则，或者匹配规则不满足，则返回空
        if(CollectionUtils.isEmpty(strategyExpressionInfoList)){
            return Nullable.getNullVal();
        }
        if (strategyExpressionInfoList.size() > 1) {
            return Nullable.getNullVal();
        }
        //定制化处理器，并不支持多个维度策略的配置，这里只会配置一个策略，因此可以直接取第一个
        StrategyExpressionInfo strategyExpressionInfo = strategyExpressionInfoList.get(0);//CHECKED
        if (!super.strategyMatch(strategyExpressionInfo.getExps(), autoHandleCorrectionOrderDTO)) {
            return Nullable.getNullVal();
        }

        log.info("ReconcilePayoutFailedAndRefundMatcher matchAutoHandlerStrategyInfo correctionNo :{}", autoHandleCorrectionOrderDTO.getCorrectionNo());

        // 不是对账来源
        if (!CorrectionConstant.CHANNEL_RECONCILE.equals(autoHandleCorrectionOrderDTO.getSysSources())) {
            log.warn("ReconcilePaymentRepeatMatcher matchAutoHandlerStrategyInfo is not CHANNEL_RECONCILE correctionNo :{}", autoHandleCorrectionOrderDTO.getCorrectionNo());
            return Nullable.getNullVal();
        }

        // 不是出款
        if (!TradeType.PAYOUTS.equals(autoHandleCorrectionOrderDTO.getTradeType())) {
            log.warn("ReconcilePayoutFailedAndRefundMatcher matchAutoHandlerStrategyInfo tradeType is not PAYOUTS correctionNo :{}", autoHandleCorrectionOrderDTO.getCorrectionNo());
            return Nullable.getNullVal();
        }

        String payoutStatus = payoutsRepository.orderQueryStatus(autoHandleCorrectionOrderDTO.getMerchantNo(), autoHandleCorrectionOrderDTO.getMerchantOrderNo());

        // 出款不是失败
        if (!"FAILED".equals(payoutStatus)) {
            log.warn("ReconcilePayoutFailedAndRefundMatcher matchAutoHandlerStrategyInfo payoutStatus is not FAILED correctionNo :{}", autoHandleCorrectionOrderDTO.getCorrectionNo());
            return Nullable.getNullVal();
        }

        // 对账信息校验
        List<ReconcileInfo> reconcileInfos = reconcileRepository.queryReconcileByChannelCommitNo(autoHandleCorrectionOrderDTO.getVoucherNo());

        // 必须是原单失败，有成功和退票
        boolean hasSuccess = reconcileInfos.stream().anyMatch(x -> !ReconcileInfo.PAYOUT_STATUS_SUCCESS.equals(x.getOrderStatus()));
        boolean hasBounceback = reconcileInfos.stream().anyMatch(x -> !ReconcileInfo.PAYOUT_STATUS_BOUNCEBACK.equals(x.getOrderStatus()));
        if ( !hasSuccess || !hasBounceback ) {
            log.warn("ReconcilePayoutFailedAndRefundMatcher matchAutoHandlerStrategyInfo reconcileInfos is not success or bounceback correctionNo :{}", autoHandleCorrectionOrderDTO.getCorrectionNo());
            return Nullable.getNullVal();
        }

        CorrectionAutoStrategyCodeHandlerDTO strategyCodeHandler = CorrectionAutoStrategyCodeHandlerDTO.builder()
                .strategyCode(strategyExpressionInfo.getStrategyProcessor())
                .childStrategyCode(strategyExpressionInfo.getChildStrategyProcessor())
                .operator(CorrectionConstant.SYSTEM_OPERATION)
                .correctionNo(autoHandleCorrectionOrderDTO.getCorrectionNo())
                .operationCorrectionCode(strategyExpressionInfo.getChildCorrectionCode())
                .build();

        return Collections.singletonList(strategyCodeHandler);
    }
}
