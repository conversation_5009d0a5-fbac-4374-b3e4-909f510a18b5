package com.payermax.operating.correction.domainservice.checker.impl;

import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.domain.dto.OperationDomainCorrectionInfo;
import com.payermax.operating.correction.domainservice.checker.AbstractChecker;
import com.payermax.operating.correction.integration.enums.ChannelOrderStatus;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import org.springframework.stereotype.Component;

@Component(value = "CC020_CHECKER")
public class BounceBackChecker extends AbstractChecker {
    @Override
    public void check(CorrectionOrderInfoDTO correctionInfo, GlobalOrderInfo globalOrderInfo, OperationDomainCorrectionInfo info) {
        if (!CorrectionConstant.REGISTRATION_PROCESS.equals(info.getStrategyCode())) {
            //交易必须是出款

            AssertUtil.isTrue(TradeType.PAYOUTS == globalOrderInfo.getAssetInfo().getTradeType(), ReturnCode.BUSINESS_EXCEPTION.getCode(), ReturnMsg.TRADE_TYPE_INVALID);
            //原交易链路信息校验
            globalOrderInfo.channelRequestInfoValidation();
            AssertUtil.isTrue(ChannelOrderStatus.SUCCESS == ChannelOrderStatus.getChannelByVal(globalOrderInfo.getChannelInfo().getChannelRequest().getStatus()), ReturnCode.BUSINESS_EXCEPTION.getCode(), ReturnMsg.TRADE_ORDER_INFO_INVALID);
        }
    }
}
