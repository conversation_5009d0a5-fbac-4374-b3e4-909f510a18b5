package com.payermax.operating.correction.domainservice.matcher;

import com.payermax.operating.correction.domain.dto.AutoHandleCorrectionOrderDTO;
import com.payermax.operating.correction.domain.dto.CorrectionAutoStrategyCodeHandlerDTO;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyRule;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;

import java.util.List;

public interface StrategyMatcher {

    List<CorrectionAutoStrategyCodeHandlerDTO> matchAutoHandlerStrategyInfo(StrategyRule strategyRule, AutoHandleCorrectionOrderDTO autoHandleCorrectionOrderDTO, GlobalOrderInfo globalOrderInfo);
}
