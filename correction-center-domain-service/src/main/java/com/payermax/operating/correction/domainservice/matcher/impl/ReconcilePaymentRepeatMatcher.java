package com.payermax.operating.correction.domainservice.matcher.impl;

import cn.hutool.core.util.NumberUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.enums.DCVoucherType;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.AutoHandleCorrectionOrderDTO;
import com.payermax.operating.correction.domain.dto.CorrectionAutoStrategyCodeHandlerDTO;
import com.payermax.operating.correction.domainservice.assembler.CorrectionDomainServiceAssembler;
import com.payermax.operating.correction.domainservice.matcher.AbstractMatcher;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyExpressionInfo;
import com.payermax.operating.correction.integration.config.nacos.model.StrategyRule;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.financial.dto.ChannelCommitResp;
import com.payermax.operating.correction.integration.rpc.financial.dto.ChannelCommitReq;
import com.payermax.operating.correction.integration.rpc.financial.repository.ChannelExchangeRepository;
import com.payermax.operating.correction.integration.rpc.payouts.repository.PayoutsRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 出款 先失败，对账发现再退票
 */
@Slf4j
@Component(value = CorrectionConstant.RECONCILE_PAYMENT_REPEAT_MATCHER)
public class ReconcilePaymentRepeatMatcher extends AbstractMatcher {

    @Resource
    private IDomainRepository domainRepository;

    @Resource
    private PayoutsRepository payoutsRepository;

    @Resource
    private ChannelExchangeRepository channelExchangeRepository;

    @Resource
    private CorrectionDomainServiceAssembler correctionDomainServiceAssembler;

    @Override
    public List<CorrectionAutoStrategyCodeHandlerDTO> matchAutoHandlerStrategyInfo(StrategyRule strategyRule, AutoHandleCorrectionOrderDTO autoHandleCorrectionOrderDTO, GlobalOrderInfo globalOrderInfo) {
        List<StrategyExpressionInfo> strategyExpressionInfoList = strategyRule.getStrategyExpressions().get(CorrectionConstant.DEFAULT);
        //如果没配置规则，或者匹配规则不满足，则返回空
        if(CollectionUtils.isEmpty(strategyExpressionInfoList)){
            return Nullable.getNullVal();
        }
        if (strategyExpressionInfoList.size() > 1) {
            return Nullable.getNullVal();
        }


        log.info("ReconcilePaymentRepeatMatcher matchAutoHandlerStrategyInfo correctionNo :{}", autoHandleCorrectionOrderDTO.getCorrectionNo());


        if (!CorrectionConstant.CHANNEL_RECONCILE.equals(autoHandleCorrectionOrderDTO.getSysSources())) {
            log.warn("ReconcilePaymentRepeatMatcher matchAutoHandlerStrategyInfo is not CHANNEL_RECONCILE correctionNo :{}", autoHandleCorrectionOrderDTO.getCorrectionNo());
            return Nullable.getNullVal();
        }

        // 只能是入款
        if (!TradeType.PAYMENT.equals(autoHandleCorrectionOrderDTO.getTradeType())) {
            log.warn("ReconcilePaymentRepeatMatcher matchAutoHandlerStrategyInfo tradeType is not PAYOUTS correctionNo :{}", autoHandleCorrectionOrderDTO.getCorrectionNo());
            return Nullable.getNullVal();
        }

        // 只能是渠道提交单
        if (!DCVoucherType.CHANNEL_COMMIT_NO.equals(autoHandleCorrectionOrderDTO.getVoucherType())) {
            log.warn("ReconcilePaymentRepeatMatcher matchAutoHandlerStrategyInfo voucherType is not CHANNEL_COMMIT_NO correctionNo :{}", autoHandleCorrectionOrderDTO.getCorrectionNo());
            return Nullable.getNullVal();
        }

        String repeatChannelCommitNo = autoHandleCorrectionOrderDTO.getVoucherNo();

        // 截取最后一个_前面的字符串
        String channelCommitNo = repeatChannelCommitNo.substring(0, autoHandleCorrectionOrderDTO.getVoucherNo().lastIndexOf("_"));

        try {
            // 截取最后一个_到最后的字符串 , 必须是数字
            String repeatNum = repeatChannelCommitNo.substring(autoHandleCorrectionOrderDTO.getVoucherNo().lastIndexOf("_") + 1);
            NumberUtil.parseNumber(repeatNum);
        } catch (Exception e) {
            log.error("ReconcilePaymentRepeatMatcher matchAutoHandlerStrategyInfo repeatNum is not number correctionNo :{} repeatChannelCommitNo: {}",
                    autoHandleCorrectionOrderDTO.getCorrectionNo(), repeatChannelCommitNo);
            return Nullable.getNullVal();
        }

        // 从金融交换获取状态
        ChannelCommitResp channelCommitResp = channelExchangeRepository.queryChannelByChannelPayCommitNo(ChannelCommitReq.builder()
                .channelPayCommitNo(channelCommitNo)
                .paymentType("20")
                .build());

        if (channelCommitResp == null) {
            log.warn("ReconcilePaymentRepeatMatcher matchAutoHandlerStrategyInfo channelCommitResp is null correctionNo :{}", autoHandleCorrectionOrderDTO.getCorrectionNo());
            return Nullable.getNullVal();
        }

        // 只能是成功
        if (!"SUCCESS".equals(channelCommitResp.getStatus())) {
            log.warn("ReconcilePaymentRepeatMatcher matchAutoHandlerStrategyInfo channel status is not SUCCESS correctionNo :{}", autoHandleCorrectionOrderDTO.getCorrectionNo());
            return Nullable.getNullVal();
        }

        //定制化处理器，并不支持多个维度策略的配置，这里只会配置一个策略，因此可以直接取第一个
        StrategyExpressionInfo strategyExpressionInfo = strategyExpressionInfoList.get(0);//CHECKED
        // 对账推送过来的数据没有渠道code,需要从金融交换获取。另外不改变源对象值，故生成新对象
        AutoHandleCorrectionOrderDTO matcherAutoHandleCorrectionOrderDTO = correctionDomainServiceAssembler.toMatcherAutoHandleCorrectionOrderDTO(autoHandleCorrectionOrderDTO);
        matcherAutoHandleCorrectionOrderDTO.setChannelCode(channelCommitResp.getChannelCode());
        if (!super.strategyMatch(strategyExpressionInfo.getExps(), matcherAutoHandleCorrectionOrderDTO)) {
            log.warn("ReconcilePaymentRepeatMatcher matchAutoHandlerStrategyInfo is not match correctionNo :{} channelCode {}", autoHandleCorrectionOrderDTO.getCorrectionNo(), channelCommitResp.getChannelCode());
            return Nullable.getNullVal();
        }

        CorrectionAutoStrategyCodeHandlerDTO strategyCodeHandler = CorrectionAutoStrategyCodeHandlerDTO.builder()
                .strategyCode(strategyExpressionInfo.getStrategyProcessor()) //
                .childStrategyCode(strategyExpressionInfo.getChildStrategyProcessor()) //
                .operator(CorrectionConstant.SYSTEM_OPERATION)
                .correctionNo(autoHandleCorrectionOrderDTO.getCorrectionNo()) //
                .operationCorrectionCode(strategyExpressionInfo.getChildCorrectionCode()) //
                .build();

        return Collections.singletonList(strategyCodeHandler);
    }
}
