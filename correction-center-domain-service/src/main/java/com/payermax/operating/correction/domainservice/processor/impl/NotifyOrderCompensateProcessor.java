package com.payermax.operating.correction.domainservice.processor.impl;

import com.payermax.common.lang.model.dto.request.PageRequest;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.processor.assembler.ProcessorAssembler;
import com.payermax.operating.correction.domainservice.processor.utils.OrderStatusUtils;
import com.payermax.operating.correction.domainservice.utils.CommonDomainUtil;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.enums.TradeOrderStatus;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderInfoDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.QueryCorrectionOrderPageDTO;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionOperationRepository;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderRequest;
import com.payermax.operating.correction.integration.rpc.ordercenter.dto.TradeOrderPatchOrderResponse;
import com.payermax.operating.correction.integration.rpc.ordercenter.repository.OrderCenterRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * 通知订单中心补交易单
 * 此处理器是针对重复支付-自动退款退不出去，订单中心补交易单操作
 *
 * @author: WangTao
 * @create: 2024-12-24 14:07
 **/
@Component(value = "orderNotifyCompensateProcessor")
@Slf4j
public class NotifyOrderCompensateProcessor implements Processor {

    @Resource
    private OrderCenterRepository orderCenterRepository;

    @Resource
    private ProcessorAssembler processorAssembler;

    @Resource
    private CorrectionOperationRepository operationRepository;

    @Override
    public BaseResProcess exec(DomainCorrectionInfo domainCorrectionInfo) {
        TradeOrderPatchOrderRequest orderRequest = processorAssembler.toPatchRefundFailedDuplicateTradeOrder(domainCorrectionInfo);
        String outTradeNo = Objects.nonNull(orderRequest.getOriginalOrderInfo()) ? orderRequest.getOriginalOrderInfo().getOutTradeNo() : null;
        //查询数据库匹配对应的信息-根据商户订单号进行查询
        QueryCorrectionOrderPageDTO queryCorrectionPage = QueryCorrectionOrderPageDTO.builder()
                .page(new PageRequest(1000L, 1L))
                .merchantOrderNo(outTradeNo)
                //这里只筛选重复支付退款类型
                .operationCorrectionCode(CorrectionConstant.CORRECTION_CHANNEL_REPEATPAY)
                .build();
        List<CorrectionOrderInfoDTO> ascOrderList = operationRepository.loadCorrectionOrderInfo(queryCorrectionPage)
                .getRecords()
                .stream()
                .sorted(Comparator.comparing(event -> event.getOpBasicInfo().getUtcCreate()))
                .collect(Collectors.toList());
        //根据推入差错的时间进行排序，并取对应的顺序
        int order = CommonDomainUtil.getCorrectSuffixFromOrderInfo(ascOrderList, domainCorrectionInfo);
        //填充重复支付差错支付标识
        orderRequest.setCorrectionSuffix(CommonDomainUtil.getCorrectSuffix(order));

        TradeOrderPatchOrderResponse response = orderCenterRepository.patchRefundFailedDuplicateTradeOrder(orderRequest);
        response.defence();
        return processorAssembler.toBaseResProcess(response);
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo correctionGlobalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo) {
        String status = OrderStatusUtils.orderCenterTradeStatus(correctionGlobalOrderInfo);
        return TradeOrderStatus.isSuccessState(TradeOrderStatus.getByCode(status)) ? CommonStatusEnum.SUCCESS : CommonStatusEnum.PENDING;
    }
}
