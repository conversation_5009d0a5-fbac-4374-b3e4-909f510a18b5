package com.payermax.operating.correction.domainservice.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.payermax.common.lang.model.dto.request.PageRequest;
import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.constant.Symbols;
import com.payermax.operating.correction.core.common.dto.OperationBasicInfo;
import com.payermax.operating.correction.core.common.dto.OperationManualFillIn;
import com.payermax.operating.correction.core.common.dto.VoucherInfo;
import com.payermax.operating.correction.core.common.enums.*;
import com.payermax.operating.correction.core.common.utils.Nullable;
import com.payermax.operating.correction.domain.dto.ExtendInfoDTO;
import com.payermax.operating.correction.domain.dto.ExtraUserInfoDTO;
import com.payermax.operating.correction.domain.dto.PaymentInstanceDomainInfo;
import com.payermax.operating.correction.domainservice.repository.IDomainRepository;
import com.payermax.operating.correction.domainservice.service.DomainOperationService;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.config.nacos.model.CorrectionPayoutsCountryInfo;
import com.payermax.operating.correction.integration.config.nacos.model.DingTalkNotifyMappingInfo;
import com.payermax.operating.correction.integration.config.nacos.model.Expression;
import com.payermax.operating.correction.integration.config.nacos.repository.ConfigRepository;
import com.payermax.operating.correction.integration.dto.PaymentInstanceDTO;
import com.payermax.operating.correction.integration.persistence.rdms.dto.*;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionOperationRepository;
import com.payermax.operating.correction.integration.persistence.rdms.repository.CorrectionRepository;
import com.payermax.operating.correction.integration.rpc.cashiercore.dto.CashierCorePaymentInstanceInfo;
import com.payermax.operating.correction.integration.rpc.cashiercore.repository.CashierCoreRepository;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.payouts.dto.PayoutsFiledInfo;
import com.payermax.operating.correction.integration.rpc.payouts.repository.PayoutsRepository;
import com.payermax.operating.correction.integration.rpc.productcenter.dto.CashierProductInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc 业务运行实现类
 * @date 2022/10/25
 */
@Component
public class DomainOperationServiceImpl implements DomainOperationService {

    @Resource
    private ConfigRepository configRepository;

    @Resource
    private CorrectionOperationRepository operationRepository;

    @Resource
    private CorrectionRepository correctionRepository;

    @Resource
    private IDomainRepository domainRepository;


    @Resource
    private CashierCoreRepository cashierCoreRepository;

    @Resource
    private PayoutsRepository payoutsRepository;

    @Override
    public void storeStrategyInfo(CorrectionOperationStrategyInfoDTO strategyInfoDTO) {
        operationRepository.storeOperationStrategyInfo(strategyInfoDTO);
    }

    @Override
    public CorrectionOperationStrategyInfoDTO getStrategyInfo(String strategyCode) {
        return operationRepository.loadOperationStrategy(strategyCode);
    }

    @Override
    public Page<CorrectionOperationStrategyInfoDTO> getStrategyInfoByPage(PageRequest pageRequest, ValidType valid) {
        return operationRepository.loadOperationStrategyInfo(pageRequest, valid);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void storeReasonBasicInfo(CorrectionBasicInfoDTO basicInfoDTO) {
        //存储原因
        operationRepository.storeCorrectionBasicInfo(basicInfoDTO);
        //将之前的记录无效
        operationRepository.invalidStrategyInfoReasonMapping(basicInfoDTO.getCorrectionCode(), basicInfoDTO.getTradeType());
        //重新存储映射信息
        operationRepository.storeStrategyInfoReasonMapping(basicInfoDTO.getStrategyCodeList(), basicInfoDTO.getCorrectionCode(), basicInfoDTO.getOpBasicInfo().getOperator(), basicInfoDTO.getTradeType());
    }

    @Override
    public void updateReasonBasicInfo(CorrectionBasicInfoDTO basicInfoDTO) {
        operationRepository.refreshReasonBasic(basicInfoDTO);
    }

    @Override
    public Page<CorrectionBasicInfoDTO> getReasonBasicInfoByPage(PageRequest page, ValidType valid) {
        return operationRepository.loadReasonBasicInfo(page, valid);
    }

    @Override
    public Page<CorrectionOrderInfoDTO> getCorrectionOrderByPage(QueryCorrectionOrderPageDTO queryCorrectionPage) {
        return operationRepository.loadCorrectionOrderInfo(queryCorrectionPage);
    }

    @Override
    public Page<CorrectionOrderInfoDTO> getCorrectionOrderByPage(PageRequest page, List<String> correctionNos, List<String> voucherNos) {
        return operationRepository.loadCorrectionOrderInfo(page, correctionNos, voucherNos);
    }

    @Override
    public List<CorrectionOrderHandlerRecordDTO> getCorrectionOrderHandlerRecord(String correctionNo, OrderDetailType detailType) {
        CorrectionHandlerUniInfo correctionHandlerUniInfo = new CorrectionHandlerUniInfo();
        correctionHandlerUniInfo.setCorrectionNo(correctionNo);
        return correctionRepository.loadCorrectionRecordList(correctionHandlerUniInfo, detailType);
    }

    @Override
    public GlobalOrderInfo getGlobalOrderInfo(String voucherNo, DCVoucherType voucherType) {
        return domainRepository.getGlobalOrderInfo(new VoucherInfo(voucherNo, voucherType), GlobalOrderReadEvent.DISPLAY);
    }

    @Override
    public void storeProofInfo(String correctionNo, ExtendInfoDTO proofInfo) {
        CorrectionOrderInfoDTO orderInfoDTO = correctionRepository.loadCorrectionOrderInfo(correctionNo);
        AssertUtil.notNull(orderInfoDTO, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.CORRECTION_NO_INVALID);
        String extendInfoStr = StringUtils.defaultIfBlank(orderInfoDTO.getOperationManual().getExtendInfo(), Symbols.ARRAY_STR);
        List<ExtendInfoDTO> extendInfoDTOS = JSONObject.parseObject(extendInfoStr, new TypeReference<List<ExtendInfoDTO>>() {
        });
        extendInfoDTOS.add(proofInfo);
        OperationManualFillIn operationManual = new OperationManualFillIn();
        operationManual.setExtendInfo(JSONObject.toJSONString(extendInfoDTOS));
        operationRepository.storeManualFillInfo(correctionNo, operationManual);
    }

    @Override
    public void storeUserInfo(String correctionNo, ExtraUserInfoDTO userInfo) {
        CorrectionOrderInfoDTO orderInfoDTO = correctionRepository.loadCorrectionOrderInfo(correctionNo);
        AssertUtil.notNull(orderInfoDTO, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.CORRECTION_NO_INVALID);
        String extraUserInfoStr = StringUtils.defaultIfBlank(orderInfoDTO.getOperationManual().getExtraUserInfo(), Symbols.ARRAY_STR);
        List<ExtraUserInfoDTO> extraUserInfoDTOS = JSONObject.parseObject(extraUserInfoStr, new TypeReference<List<ExtraUserInfoDTO>>() {
        });
        userInfo.setToken(userInfo.getToken());
        userInfo.setSeqNo(extraUserInfoDTOS.size() + 1);
        extraUserInfoDTOS.add(userInfo);
        OperationManualFillIn operationManual = new OperationManualFillIn();
        operationManual.setExtraUserInfo(JSONObject.toJSONString(extraUserInfoDTOS));
        operationRepository.storeManualFillInfo(correctionNo, operationManual);
    }

    @Override
    public void storeCorrectionRemarkInfo(String correctionNo, String memo) {
        CorrectionOrderInfoDTO orderInfoDTO = new CorrectionOrderInfoDTO(correctionNo);
        OperationManualFillIn operationManual = new OperationManualFillIn();
        operationManual.setMemo(memo);
        orderInfoDTO.setOperationManual(operationManual);
        operationRepository.storeManualFillInfo(correctionNo, operationManual);
    }

    @Override
    public Map<String, String> getAllMappingStrategyCodeInfo() {
        List<CorrectionOperationStrategyInfoDTO> strategyAll = operationRepository.loadOperationStrategyAll();
        return strategyAll.stream().collect(Collectors.toMap(CorrectionOperationStrategyInfoDTO::getStrategyCode, CorrectionOperationStrategyInfoDTO::getStrategyName, (key1, key2) -> key2));
    }

    @Override
    public Map<String, CorrectionOperationStrategyInfoDTO> getAllMappingStrategyCode() {
        List<CorrectionOperationStrategyInfoDTO> strategyAll = operationRepository.loadOperationStrategyAll();
        return strategyAll.stream().collect(Collectors.toMap(CorrectionOperationStrategyInfoDTO::getStrategyCode, Function.identity(), (key1, key2) -> key2));
    }

    @Override
    public Map<String, String> getAllChildrenMappingReasonInfo() {
        List<CorrectionBasicInfoDTO> basicAllInfo = operationRepository.loadAllChildrenBasicInfo();
        return basicAllInfo.stream().collect(Collectors.toMap(CorrectionBasicInfoDTO::getCorrectionCode, CorrectionBasicInfoDTO::getCorrectionName, (key1, key2) -> key2));
    }

    @Override
    public Map<String, CorrectionBasicInfoDTO> getAllMappingParentReasonInfo() {
        return configRepository.getParentReasonBasicMap();
    }

    @Override
    public List<CorrectionBasicInfoDTO> getBasicInfoByParentCode(String parentCorrectionCode, TradeType tradeType) {
        return operationRepository.loadChildrenReasonBasicByParent(parentCorrectionCode, tradeType);
    }

    @Override
    public List<PaymentInstanceDTO> getPaymentInstanceList(String correctionNo, String country, String paymentMethodNo) {
        DomainCorrectionInfo correctionInfo = domainRepository.getValidCorrectionOrderInfo(correctionNo, OrderDetailType.All_QUERY, GlobalOrderReadEvent.DISPLAY);
        GlobalOrderInfo globalOrderInfo = correctionInfo.getGlobalOrderInfo();
        //build
        PaymentInstanceDomainInfo paymentInstanceDomainInfo = new PaymentInstanceDomainInfo(country, paymentMethodNo, CorrectionConstant.PAYOUTS_PAYMENT_METHODS);
        paymentInstanceDomainInfo.setBizIdentify(globalOrderInfo.getBizIdentify());
        paymentInstanceDomainInfo.setMerchantNo(globalOrderInfo.getOriginalMerchantInfo().getMerchantNo());
        paymentInstanceDomainInfo.setPayAmount(correctionInfo.getPayTotalMoney());
        return this.getPaymentInstanceList(paymentInstanceDomainInfo);
    }

    @Override
    public List<PaymentInstanceDTO> getPaymentInstanceList(PaymentInstanceDomainInfo paymentInstanceDomainInfo) {
        CashierCorePaymentInstanceInfo cashierCorePaymentInstanceInfo = this.buildPaymentInstance(paymentInstanceDomainInfo);
        cashierCoreRepository.getPaymentInstance(cashierCorePaymentInstanceInfo, paymentInstanceDomainInfo.getPayAmount(), paymentInstanceDomainInfo.getBizIdentify(), paymentInstanceDomainInfo.getMerchantNo());
        return cashierCorePaymentInstanceInfo.getPaymentInstanceList();
    }

    @Override
    public List<PayoutsFiledInfo> renderingPayOutInfo(PaymentInstanceDomainInfo paymentInstanceDomainInfo) {
        //调用出款服务获取出款信息
        return payoutsRepository.queryPayoutsFieldInfo(paymentInstanceDomainInfo.getCountry(), paymentInstanceDomainInfo.getCashierProductNo());
    }

    @Override
    public void getCashierProductNo(PaymentInstanceDomainInfo paymentInstanceDomainInfo) {
        CashierCorePaymentInstanceInfo cashierCorePaymentInstanceInfo = this.buildPaymentInstance(paymentInstanceDomainInfo);
        //先获取售卖编码
        CashierProductInfo cashierProductInfo = domainRepository.getValidCashierProductInfo(cashierCorePaymentInstanceInfo);
        paymentInstanceDomainInfo.setCashierProductNo(cashierProductInfo.getCashierProductNo());
    }

    @Override
    public List<String> getConfigStrategyCode(String operatorCorrectionCode, TradeType tradeType) {
        List<CorrectionOperationUniqueDTO> mappingList = correctionRepository.getCorrectionCodeMappingValidList(operatorCorrectionCode, tradeType);
        return mappingList.stream().map(CorrectionOperationUniqueDTO::getStrategyCode).collect(Collectors.toList());
    }

    @Override
    public List<CorrectionPayoutsCountryInfo> getCorrectionPayoutsCountry() {
        return configRepository.getPayOutsCountry();
    }

    @Override
    public ConcurrentHashMap<String, List<Expression>> getStrategyBlacklist() {
        return configRepository.getStrategyBlacklist();
    }

    @Override
    public List<CorrectionBasicInfoDTO> getAllParentCorrectionReason() {
        return configRepository.getParentReasonBasicInfoByMemory();
    }

    @Override
    public void storeOpBasicInfo(String correctionNo, OperationBasicInfo basicInfo) {
        operationRepository.storeCorrectionOpBasicInfo(correctionNo, basicInfo);
    }

    @Override
    public DingTalkNotifyMappingInfo getDingTalkMapping(String shareId) {
        DingTalkNotifyMappingInfo notifyInfo = configRepository.getNotifyInfo(shareId);
        AssertUtil.notNull(notifyInfo, ReturnCode.ILLEGAL_PARAMS.getCode(), ReturnMsg.INCORRECT_REVIEWER);
        return notifyInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlerDirtyCorrectionNo(String correctionNo,String tarStats) {
        CorrectionOrderInfoDTO oderInfo = domainRepository.getValidCorrectionOrderInfoDto(correctionNo);
        correctionRepository.restoreStatus(correctionNo, oderInfo.getProcessStatus(), tarStats);
        oderInfo.getOpBasicInfo().setOperator(CorrectionConstant.SYSTEM_HANDLER_DIRTY);
        oderInfo.setOperationManual(Nullable.getNullVal());
        correctionRepository.storeCorrectionStrategyInfo(oderInfo);
    }

    private CashierCorePaymentInstanceInfo buildPaymentInstance(PaymentInstanceDomainInfo paymentInstanceDomainInfo) {
        CashierCorePaymentInstanceInfo cashierCorePaymentInstanceInfo = new CashierCorePaymentInstanceInfo();
        PaymentInstanceDTO paymentInstanceDTO = new PaymentInstanceDTO(paymentInstanceDomainInfo.getPaymentMethodNo());
        paymentInstanceDTO.setTargetOrg(paymentInstanceDomainInfo.getTargetOrg());
        paymentInstanceDTO.setBizIdentify(paymentInstanceDomainInfo.getBizIdentify());
        //build
        cashierCorePaymentInstanceInfo.setPaymentInstanceList(Lists.newArrayList(paymentInstanceDTO));
        cashierCorePaymentInstanceInfo.setPaymentType(paymentInstanceDomainInfo.getPaymentType());
        cashierCorePaymentInstanceInfo.setCountry(paymentInstanceDomainInfo.getCountry());
        return cashierCorePaymentInstanceInfo;
    }

}
