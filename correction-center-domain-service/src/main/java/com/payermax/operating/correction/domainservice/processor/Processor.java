package com.payermax.operating.correction.domainservice.processor;

import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;

/**
 * The interface Processor.
 *
 * <AUTHOR>
 * @desc 最小单位执行器
 * @date 2022 /9/21
 */
public interface Processor {

    /**
     * 前置校验
     *
     * @param correctionInfo the correction info
     */
    default void preCheck(DomainCorrectionInfo correctionInfo) {
    };

    /**
     * Fill in process request.
     *
     * @param handlerRecordDTO the handler record dto
     * @param correctionInfo   the correction info
     */
    default void fillInProcessRequest(CorrectionOrderHandlerRecordDTO handlerRecordDTO,DomainCorrectionInfo correctionInfo){
    };

    /**
     * Final result post processing.
     *
     * @param correctionInfo the correction info
     * @param commonStatus   the common status
     */
    default void finalResultPostProcessing(DomainCorrectionInfo correctionInfo,CommonStatusEnum commonStatus){

    };

    /**
     * Exec result.
     *
     * @param correctionInfo the correction info
     * @return the result
     */
    BaseResProcess exec(DomainCorrectionInfo correctionInfo);

    /**
     * Suc handler common status enum.
     *
     * @param correctionGlobalOrderInfo   the correction global order info
     * @param correctionHandlerRecordInfo the correction handler record info
     * @return the common status enum
     */
    CommonStatusEnum handlerResult(GlobalOrderInfo correctionGlobalOrderInfo, CorrectionOrderHandlerRecordDTO correctionHandlerRecordInfo);

}
