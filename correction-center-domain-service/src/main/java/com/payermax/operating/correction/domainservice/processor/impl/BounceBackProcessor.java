package com.payermax.operating.correction.domainservice.processor.impl;

import com.payermax.common.lang.util.AssertUtil;
import com.payermax.operating.correction.core.common.constant.CorrectionConstant;
import com.payermax.operating.correction.core.common.dto.BaseResProcess;
import com.payermax.operating.correction.core.common.enums.CommonStatusEnum;
import com.payermax.operating.correction.core.common.enums.ReturnCode;
import com.payermax.operating.correction.core.common.enums.ReturnMsg;
import com.payermax.operating.correction.core.common.enums.TradeType;
import com.payermax.operating.correction.domainservice.processor.Processor;
import com.payermax.operating.correction.domainservice.vo.DomainCorrectionInfo;
import com.payermax.operating.correction.integration.enums.ChannelOrderStatus;
import com.payermax.operating.correction.integration.persistence.rdms.dto.CorrectionOrderHandlerRecordDTO;
import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import com.payermax.operating.correction.integration.rpc.financial.dto.ChannelResultNotifyInfo;
import com.payermax.operating.correction.integration.rpc.financial.repository.ChannelExchangeRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @desc 解冻并扣除处理器
 * @date 2022/10/17
 */
@Component(value = CorrectionConstant.BOUNCE_BACK_PROCESSOR)
@Slf4j
public class BounceBackProcessor implements Processor {

    @Resource
    private ChannelExchangeRepository channelExchangeRepository;

    @Override
    public void preCheck(DomainCorrectionInfo correctionInfo) {
        //交易必须是出款

        AssertUtil.isTrue(TradeType.PAYOUTS == correctionInfo.getTradeType(), ReturnCode.BUSINESS_EXCEPTION.getCode(), ReturnMsg.TRADE_TYPE_INVALID);
        //原交易链路信息校验
        GlobalOrderInfo globalOrderInfo = correctionInfo.getGlobalOrderInfo();
        AssertUtil.isTrue(Objects.nonNull(globalOrderInfo), ReturnCode.BUSINESS_EXCEPTION.getCode(), ReturnMsg.TRADE_ORDER_INFO_INVALID);
        globalOrderInfo.channelRequestInfoValidation();
        AssertUtil.isTrue(ChannelOrderStatus.SUCCESS == ChannelOrderStatus.getChannelByVal(globalOrderInfo.getChannelInfo().getChannelRequest().getStatus()), ReturnCode.BUSINESS_EXCEPTION.getCode(), ReturnMsg.TRADE_ORDER_INFO_INVALID);
    }

    @Override
    public BaseResProcess exec(DomainCorrectionInfo correctionInfo) {
        ChannelResultNotifyInfo resultNotifyInfo = ChannelResultNotifyInfo.builder()
                .channelCommitNo(correctionInfo.getOriVoucherInfo().getVoucherNo())
                .payAmount(correctionInfo.getPayTotalMoney())
                .build();
        return channelExchangeRepository.bounceBack(resultNotifyInfo);
    }

    @Override
    public CommonStatusEnum handlerResult(GlobalOrderInfo globalOrderInfo, CorrectionOrderHandlerRecordDTO handlerRecordDTO) {
        return ObjectUtils.defaultIfNull(handlerRecordDTO.getStatus(), CommonStatusEnum.PENDING);
    }
}
