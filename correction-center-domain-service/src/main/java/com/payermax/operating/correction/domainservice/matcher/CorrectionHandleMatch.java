package com.payermax.operating.correction.domainservice.matcher;

import com.payermax.operating.correction.integration.rpc.datacenter.dto.GlobalOrderInfo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;

/**
 * The interface Correction handle match.
 *
 * <AUTHOR>
 * @desc 差错处理匹配器
 * @date 2023 /2/20
 */
public interface CorrectionHandleMatch {
    /**
     * 策略code匹配
     *
     * @param correctionCode  the correction code
     * @param globalOrderInfo the global order info
     * @return String childCorrectionCode
     */
    List<String> matchRule(String correctionCode, GlobalOrderInfo globalOrderInfo);
}
