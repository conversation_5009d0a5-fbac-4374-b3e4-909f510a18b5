package com.payermax.operating.correction.facade.request;

import com.payermax.operating.correction.facade.common.ReasonBasicInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 添加原因基础信息
 * @date 2022/11/1
 */
@Data
public class SaveReasonBasicInfo extends ReasonBasicInfo implements Serializable {

    private static final long      serialVersionUID = -234904283426832L;


    /**
     * 运营处理策略列表
     */
    private List<String> strategyCodeList;
}
