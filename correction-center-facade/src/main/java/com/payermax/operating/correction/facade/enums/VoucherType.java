package com.payermax.operating.correction.facade.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @desc 与数据中心的凭证映射类型
 * @date 2022/9/27
 */
public enum VoucherType {
    TRADE_TOKEN_NO("交易Token"),
    R_MERCHANT_ORDER_NO("商户订单号（收）"),
    D_MERCHANT_ORDER_NO("商户订单号（付）"),
    R_TRADE_ORDER_NO("交易订单号"),
    PAYOUTS_REQUEST_NO("出款订单号"),
    PAY_REQUEST_NO("支付请求单号"),
    REFUND_TRADE_NO("退款交易订单号"),
    ASSET_PAY_ORDER("资产交换订单号"),
    CHANNEL_REQUEST_NO("渠道请求单号"),
    CHANNEL_COMMIT_NO("渠道提交单号"),
    THIRD_ORDER_NO("三方机构单号"),
    COMMIT_AND_THIRD("渠道提交单号-三方单号"),
    TRADE_RECON_NO("交易对账单"),
    ;

    @Getter
    private String name;

    VoucherType(String name) {
        this.name = name;
    }
}
