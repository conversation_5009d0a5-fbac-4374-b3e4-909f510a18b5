package com.payermax.operating.correction.facade.common;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 冗余信息
 * @date 2023/4/19
 */
@Data
public class RedundantInfo implements Serializable {

    private static final long      serialVersionUID = -23429236927822645L;

    /**
     * 产品码
     */
    private String productCode;

    /**
     * 退款类型
     */
    private String refundTypeSource;

    /**
     * 重试类型
     */
    private String retryType;

    /**
     * 详情code
     */
    private String detailCode;

    /**
     * 差错详情描述
     */
    private String detailMsg;

    /**
     * 渠道交易完成时间
     */
    private String channelCompleteTime;

}
