package com.payermax.operating.correction.facade.common;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 差错中心核心数据信息
 * @date 2022/3/4
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
public class OrderCorrectionBaseInfo implements Serializable {

    private static final long serialVersionUID = 908237487263478L;
    /**
     * 系统来源
     * @mock ASSET_EXCHANGE,ORDER_CENTER
     */
    @NotBlank(message = "sysSource can't be blank")
    private String sysSource;

    /**
     * 事件基础信息
     */
    @NotNull(message = "correctionInfo can't be null")
    private CorrectionEventInfo correctionInfo;
    /**
     * 差错金额信息
     */
    private CorrectionAmountInfo amountInfo;


    /**
     * 对账冗余信息
     */
    private ChannelReconcileRedundantInfo reconcileRedundantInfo;

    /**
     * 重试类型
     */
    private String retryType;

}
