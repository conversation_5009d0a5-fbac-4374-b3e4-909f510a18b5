package com.payermax.operating.correction.facade.common;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 订单关联查询响应信息
 * @date 2022/11/9
 */
@Data
@NoArgsConstructor
public class OrderInfo implements Serializable {

    private static final long serialVersionUID = 234789273492398L;
    /**
     * 商户订单号
     */
    private String merchantOrderNo;
    /**
     *交易单
     */
    private String tradeOrder;
    /**
     * 交易单状态
     */
    private String tradeStatus;
    /**
     * 产品码
     */
    private String productCode;
    /**
     * 支付金额
     */
    private String payAmount;
    /**
     * 支付币种
     */
    private String payCurrency;

    /**
     * 支付单号
     */
    private String payOrderNo;
    /**
     * 支付单状态
     */
    private String payOrderState;

    /**
     * 渠道提交单
     */
    private String channelCommitNo;
    /**
     * 提交单状态
     */
    private String channelStatus;
    /**
     * 三方单号
     */
    private String thirdOrderNo;

    public OrderInfo(String productCode) {
        this.productCode = productCode;
    }
}
