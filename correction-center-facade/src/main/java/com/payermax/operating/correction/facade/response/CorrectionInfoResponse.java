package com.payermax.operating.correction.facade.response;

import com.payermax.operating.correction.facade.common.CorrectionEventInfo;
import com.payermax.operating.correction.facade.enums.StatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 差错返回响应
 * @date 2022/4/7
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CorrectionInfoResponse implements Serializable {

    private static final long serialVersionUID = 3746276487249L;

    /**
     *差错信息
     **/
    private CorrectionEventInfo correctionInfo;

    /**
     *处理结果状态
     */
    private StatusEnum status;

    /**
     *差错处理结果状态
     */
    private String statusName;

    public CorrectionInfoResponse(CorrectionEventInfo correctionInfo, StatusEnum status) {
        this.correctionInfo = correctionInfo;
        this.status = status;
    }
}
