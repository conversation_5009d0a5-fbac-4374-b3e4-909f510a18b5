package com.payermax.operating.correction.facade.response;

import com.payermax.operating.correction.facade.common.CorrectionOperationBasicInfo;
import com.payermax.operating.correction.facade.common.OrderInfo;
import com.payermax.operating.correction.facade.common.VoucherInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @desc 差错关联信息
 * <AUTHOR>
 * @date 2022/11/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CorrectionRelationInfo implements Serializable {

    private static final long serialVersionUID = 1092382637864L;

    private CorrectionOperationBasicInfo basicInfo;

    private OrderInfo originalOrderInfo;

    private OrderInfo responseOrderInfo;

    private String completeTime;

}
