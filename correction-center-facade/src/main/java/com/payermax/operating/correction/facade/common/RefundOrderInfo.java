package com.payermax.operating.correction.facade.common;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 退款订单信息
 * @date 2022/11/9
 */
@Data
public class RefundOrderInfo implements Serializable {

    private static final long serialVersionUID = 973246724628737L;

    /**
     * 退款交易单号
     */
    private String tradeNo;
    /**
     * 退款申请状态
     */
    private String tradeStatus;
    /**
     * 退款金额
     */
    private String refundAmount;
    /**
     * 退款币种
     */
    private String refundCurrency;
    /**
     * 渠道提交单号
     */
    private String channelCommitNo;
    /**
     * 渠道提交单状态
     */
    private String channelStatus;

    /**
     * 渠道请求单单号
     */
    private String channelRequestNo;

    /**
     * 渠道请求单状态
     */
    private String channelRequestStatus;

    /**
     * 退款类型
     */
    private String refundType;

    /**
     * 错误码code
     */
    private String channelRespCode;

    /**
     * 错误码msg
     */
    private String channelRespMsg;
}
