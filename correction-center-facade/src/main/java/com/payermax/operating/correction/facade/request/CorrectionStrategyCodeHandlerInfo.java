package com.payermax.operating.correction.facade.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 策略处理基本所需信息
 * @date 2022/10/19
 */
@Data
@NoArgsConstructor
public class CorrectionStrategyCodeHandlerInfo implements Serializable {
    private static final long serialVersionUID = -1L;
    /**
     * 策略Code
     */
    private String strategyCode;

    /**
     * 运营原因
     */
    private String operationCorrectionCode;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 差错单
     */
    private String correctionNo;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 额外信息
     */
    private String extraInfoJson;

    /**
     * 转交信息
     */
    private ForwardInfo forwardInfo;

    /**
     * 备注
     */
    private String memo;

}
