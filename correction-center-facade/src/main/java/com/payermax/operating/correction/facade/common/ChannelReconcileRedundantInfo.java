package com.payermax.operating.correction.facade.common;

import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 渠道对账冗余信息
 * @date 2023/5/23
 */
@Data
@NoArgsConstructor
public class ChannelReconcileRedundantInfo extends RedundantInfo implements Serializable {

    private static final long serialVersionUID = -23429049857822645L;

    /**
     * 主体
     */
    private String entity;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 机构名称
     */
    @Getter
    private String orgName;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 完成时间(外部完成时间)
     */
    private String completeTime;

    /**
     * 提交单完成时间(金融交换)
     */
    private String commitCompleteTime;

    /**
     * 外部状态
     */
    private String channelStatus;

    /**
     * 提交单状态(金融交换)
     */
    private String commitStatus;

    /**
     * 批次信息
     */
    private String batchNo;

    /**
     * 提交单号
     */
    private String channelCommitNo;

    /**
     * 三方单号
     */
    private String thirdOrderNo;

    /**
     * 四方单号
     */
    private String fourthOrderNo;

    /**
     * 支付币种
     */
    private String currency;

    /**
     * 金额
     */
    private BigDecimal amount;


    /**
     * 对账规则Id
     */
    private String reconcileRuleId;

    /**
     * 交易对账差错凭证号
     */
    private String errorOrderNo;

    /**
     * 交易类型(外部)
     */
    private String channelTradeType;

    /**
     * 客资负责人
     */
    @Getter
    private String owner;

    private List<String> ownerList;

    private List<String> orgNameList;

    public void setOrgName(String orgName) {
        if (StringUtils.isNotBlank(orgName)) {
            this.orgName = orgName.replace("ERROR:", StringUtils.EMPTY);
        } else {
            this.orgName = orgName;
        }
    }

    public void setOwner(String owner) {
        if (StringUtils.isNotBlank(owner)) {
            this.owner = owner.replace("ERROR:", StringUtils.EMPTY);
        } else {
            this.owner = owner;
        }
    }
}
