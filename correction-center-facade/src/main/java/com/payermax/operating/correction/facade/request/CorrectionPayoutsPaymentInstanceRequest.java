package com.payermax.operating.correction.facade.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 差错处理支付实例请求对象
 * @date 2022/12/15
 */
@Data
@NoArgsConstructor
public class CorrectionPayoutsPaymentInstanceRequest implements Serializable {

    private static final long serialVersionUID = 9234092478942903L;

    private String correctionNo;

    private PayoutsPaymentInstanceRequest paymentInstanceRequest;
}
