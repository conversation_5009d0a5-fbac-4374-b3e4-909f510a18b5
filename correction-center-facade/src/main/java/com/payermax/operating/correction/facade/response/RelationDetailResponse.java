package com.payermax.operating.correction.facade.response;

import com.payermax.common.lang.util.money.Money;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 关联查询详情页面
 * @date 2022/11/15
 */
@Data
@NoArgsConstructor
public class RelationDetailResponse implements Serializable {

    private static final long serialVersionUID = 142985298423598L;


    private String correctionNo;

    private String completeTime;

    private String processStatus;

    private List<OrderDetailInfo> originalOrderInfo;

    private List<OrderDetailInfo> handlerOrderInfo;

    @Data
    public static class OrderDetailInfo implements Serializable {

        private static final long serialVersionUID = 142985298423587L;

        /**
         * 原始商户信息
         */
        private MerchantInfo merchantInfo;

        /**
         * 交易信息
         */
        private TradeInfo tradeInfo;

        /**
         * 资产交换信息
         */
        private AssetInfo assetInfo;

        /**
         * 渠道信息
         */
        private ChannelInfo channelInfo;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MerchantInfo implements Serializable {

        private static final long serialVersionUID = 142985298401587L;

        /**
         * 商户号
         */
        private String merchantNo;

        /**
         * 商户订单号
         */
        private String merchantOrderNo;

    }

    /**
     * <AUTHOR>
     * @desc 交易信息
     * @date 2022/9/22
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TradeInfo implements Serializable {

        private static final long serialVersionUID = 142958298423587L;

        /**
         * 交易单
         */
        private OrderInfo tradeOrder;

        /**
         * 退款单
         */
        private OrderInfo refundOrder;

    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AssetInfo implements Serializable {

        private static final long serialVersionUID = 1492845298423587L;
        /**
         * 资产交换订单
         */
        private OrderInfo assetOrder;

        /**
         * 支付金额
         */
        private Money payMoney;

        /**
         * 交易类型
         */
        private String tradeType;
    }

    /**
     * <AUTHOR>
     * @desc 渠道信息
     * @date 2022/9/22
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChannelInfo implements Serializable {

        private static final long serialVersionUID = 142985293283587L;

        /**
         * 渠道提交单
         */
        private List<ChannelCommitInfo> channelCommit;

        /**
         * 最后一个渠道提交单
         */
        private ChannelCommitInfo lastChannelCommit;

        /**
         * 渠道请求单
         */
        private OrderInfo channelRequest;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ChannelCommitInfo implements Serializable {

        private static final long serialVersionUID = 2387642947245L;

        /**
         * 渠道提交单
         */
        private OrderInfo commitInfo;

        private String channelCode;
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderInfo implements Serializable {

        private static final long serialVersionUID = 2471985298423587L;
        /**
         * 订单号
         */
        private String orderNo;

        /**
         * 订单状态
         */
        private String statusName;
    }


    public RelationDetailResponse(String correctionNo, String completeTime, String processStatus) {
        this.correctionNo = correctionNo;
        this.completeTime = completeTime;
        this.processStatus = processStatus;
    }
}
