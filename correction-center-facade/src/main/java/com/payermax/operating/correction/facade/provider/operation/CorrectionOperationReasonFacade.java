package com.payermax.operating.correction.facade.provider.operation;

import com.payermax.common.lang.model.dto.Page;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.correction.facade.common.BaseReasonBasicInfo;
import com.payermax.operating.correction.facade.common.QueryPageRequest;
import com.payermax.operating.correction.facade.common.ReasonBasicInfo;
import com.payermax.operating.correction.facade.request.SaveReasonBasicInfo;

import java.util.List;

/**
 * The interface Correction operation reason facade.
 *
 * <AUTHOR>
 * @desc 差错原因provider
 * @date 2022 /11/8
 */
public interface CorrectionOperationReasonFacade {


    /**
     * Query reason info by page result.
     *
     * @param pageRequest the page request
     * @return the result
     */
    Result<Page<ReasonBasicInfo>> queryReasonInfoByPage(QueryPageRequest pageRequest);

    /**
     * Store reason info result.
     *
     * @param saveReasonBasicInfo the save reason basic info
     * @return the result
     */
    Result storeReasonInfo(SaveReasonBasicInfo saveReasonBasicInfo);

    /**
     * 规则无效化
     *
     * @param correctionCode the correction code
     * @param operator       the operator
     * @return the result
     */
    Result invalidReasonBasic(String correctionCode, String operator);

    /**
     * Query all parent correction code result.
     *
     * @return the result
     */
    Result<List<BaseReasonBasicInfo>> queryAllParentCorrectionCode();

    /**
     * Query strategy list result.
     *
     * @param correctionCode the correction code
     * @param tradeType      the trade type
     * @return the result
     */
    Result<List<String>> queryStrategyList(String correctionCode, String tradeType);

    /**
     * Filter child correction code result.
     *
     * @param parentCorrectionCode the parent correction code
     * @param tradeType            the trade type
     * @return the result
     */
    Result<List<BaseReasonBasicInfo>>filterChildCorrectionCode(String parentCorrectionCode,String tradeType);
}
