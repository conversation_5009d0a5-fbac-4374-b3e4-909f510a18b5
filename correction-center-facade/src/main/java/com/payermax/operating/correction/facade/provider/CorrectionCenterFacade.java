package com.payermax.operating.correction.facade.provider;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.correction.facade.common.CorrectionEventInfo;
import com.payermax.operating.correction.facade.common.OrderCorrectionBaseInfo;
import com.payermax.operating.correction.facade.response.UnionCorrectionOrderInfoResponse;
import com.payermax.operating.correction.facade.request.ExternalResultInfo;
import com.payermax.operating.correction.facade.response.CorrectionOrderInfoResponse;

/**
 * 差错接口API
 *
 * <AUTHOR>
 * @desc 差错中心服务提供
 * @date 2022 /3/4
 */
public interface CorrectionCenterFacade {

    /**
     * 批量事件查询
     *
     * @param eventInfos 凭证信息
     * @return the result
     */
    Result<CorrectionOrderInfoResponse> eventQuery(CorrectionEventInfo eventInfos);

    /**
     * Push send event result.
     *
     * @param eventInfo the event info
     * @return the result
     */
    Result<CorrectionOrderInfoResponse> pushSendEvent(OrderCorrectionBaseInfo eventInfo);


    /**
     * Event query result.
     *
     * @param correctionNo 差错单号
     * @return the result
     */
    Result<CorrectionOrderInfoResponse> eventQuery(String correctionNo);

    /**
     * Event handle notify result.
     *
     * @param eventInfos the event infos
     * @return the result
     */
    Result eventExternalResultNotify(ExternalResultInfo eventInfos);

    /**
     * Union event query result.
     *
     * @param eventInfo the event infos
     * @return the result
     */
    Result<UnionCorrectionOrderInfoResponse> unionEventQuery(CorrectionEventInfo eventInfo);
}
