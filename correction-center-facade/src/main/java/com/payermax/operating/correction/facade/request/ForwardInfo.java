package com.payermax.operating.correction.facade.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 转交信息
 * @date 2023/6/1
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ForwardInfo implements Serializable {

    private static final long serialVersionUID = -9823489325356L;

    /**
     * 差错单号
     */
    private String correctionNo;

    /**
     * 转交类型
     */
    private String forwardType;

    /**
     * 复核人
     */
    private String reviewer;

    /**
     * 操作人
     */
    private String operator;

}
