package com.payermax.operating.correction.facade.request;

import com.payermax.operating.correction.facade.common.OrderCorrectionBaseInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 系统调用API
 * @date 2022/3/9
 */
@Data
@NoArgsConstructor
@ToString
public class CorrectionSystemInfoRequest extends OrderCorrectionBaseInfo implements Serializable {
    private static final long      serialVersionUID = -3310616568460585685L;
}
