package com.payermax.operating.correction.facade.provider.operation;

import com.payermax.common.lang.model.dto.Page;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.common.lang.model.dto.request.PageRequest;
import com.payermax.operating.correction.facade.common.BaseReasonBasicInfo;
import com.payermax.operating.correction.facade.common.CorrectionEventInfo;
import com.payermax.operating.correction.facade.common.QueryPageRequest;
import com.payermax.operating.correction.facade.request.*;
import com.payermax.operating.correction.facade.response.CorrectionOrderDetailInfoResponse;
import com.payermax.operating.correction.facade.response.CorrectionOrderHandlerBasicInfo;

import java.util.List;

/**
 * The interface Correction operation order handler provider.
 *
 * <AUTHOR>
 * @desc 订单操作相关
 * @date 2022 /11/8
 */
public interface CorrectionOperationOrderHandlerFacade {

    /**
     * Query reason info by page result.
     *
     * @param handlerPageRequest the handler page request
     * @return the result
     */
    Result<Page<CorrectionOrderHandlerBasicInfo>> queryCorrectionHandlerList(CorrectionOrderHandlerPageRequest handlerPageRequest);


    /**
     * Query correction order detail info result.
     *
     * @param correctionNo the correction no
     * @return the result
     */
    Result<CorrectionOrderDetailInfoResponse> queryCorrectionOrderDetailInfo(String correctionNo);


    /**
     * Query correction order detail info result.
     *
     * @param voucherInfo the voucher info
     * @return the result
     */
    Result<CorrectionOrderDetailInfoResponse> queryCorrectionOrderDetailInfo(CorrectionEventInfo voucherInfo);

    /**
     * Add upload info result.
     *
     * @param addUploadInfo the add upload info
     * @return the result
     */
    Result addUploadInfo(AddUploadInfo addUploadInfo);

    /**
     * Approval result.
     *
     * @param approvalEvent the approval event
     * @return the result
     */
    Result approval(ApprovalEvent approvalEvent);

    /**
     * Add correction event result.
     *
     * @param manualInfo the manual info
     * @return the result
     */
    Result addCorrectionEvent(OrderCorrectionManualInfoRequest manualInfo);


    /**
     * Choose strategy info result.
     *
     * @param strategyCodeHandlerInfo the strategy code handler info
     * @return the result
     */
    Result chooseStrategyInfo(CorrectionStrategyCodeHandlerInfo strategyCodeHandlerInfo);

    /**
     * hanler registration信息
     *
     * @param memo         the memo
     * @param correctionNo the correction no
     * @param operator     the operator
     * @return the result
     */
    @Deprecated
    Result handlerRegistration(String memo, String correctionNo, String operator);

    /**
     * Event transfer result.
     *
     * @param forwardInfo the forward info
     * @return the result
     */
    Result eventTransfer(ForwardInfo forwardInfo);

    /**
     * Query personal correction pool result.
     *
     * @param primaryQueryRequest the primary query request
     * @return the result
     */
    Result<Page<CorrectionOrderHandlerBasicInfo>> queryPersonalCorrectionPool(CorrectionPrimaryQueryRequest primaryQueryRequest);

    /**
     * Query all register handler reason result.
     *
     * @return the result
     */
    Result<List<String>>queryAllRegisterHandlerReason();

}
