package com.payermax.operating.correction.facade.provider.operation;

import com.payermax.common.lang.model.dto.Page;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.correction.facade.request.CorrectionPrimaryQueryRequest;
import com.payermax.operating.correction.facade.response.CorrectionRelationInfo;
import com.payermax.operating.correction.facade.response.RelationDetailResponse;

/**
 * The interface Correction operation relation query provider.
 *
 * <AUTHOR>
 * @desc 差错关联查询dubbo接口
 * @date 2022 /11/13
 */
public interface CorrectionOperationRelationQueryFacade {

    /**
     * Query relation list result.
     *
     * @param pageRequest the page request
     * @return the result
     */
    Result<Page<CorrectionRelationInfo>> queryRelationList(CorrectionPrimaryQueryRequest pageRequest);

    /**
     * Query detail result.
     *
     * @param correctionNo the correction no
     * @return the result
     */
    Result<RelationDetailResponse>queryDetail(String correctionNo);

}
