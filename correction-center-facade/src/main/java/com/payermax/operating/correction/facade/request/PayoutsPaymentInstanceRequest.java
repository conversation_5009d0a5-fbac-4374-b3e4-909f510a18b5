package com.payermax.operating.correction.facade.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 支付实例获取
 * @date 2022/12/7
 */
@Data
@NoArgsConstructor
public class PayoutsPaymentInstanceRequest implements Serializable {

    private static final long serialVersionUID = -983475095303975L;

    private String country;

    private String paymentMethodNo;

    private String targetOrg;

}
