package com.payermax.operating.correction.facade.request;

import com.payermax.operating.correction.facade.common.ChannelReconcileRedundantInfo;
import com.payermax.operating.correction.facade.common.CorrectionEventInfo;
import com.payermax.operating.correction.facade.common.QueryPageRequest;
import com.payermax.operating.correction.facade.common.RedundantInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 差错订单出来分页查询
 * @date 2022/11/8
 */
@Data
public class CorrectionOrderHandlerPageRequest extends QueryPageRequest implements Serializable {

    private static final long serialVersionUID = 427893749274623L;

    /**
     * 差错code
     */
    private CorrectionEventInfo eventInfo;

    /**
     * 运营操作code
     */
    private String operationCorrectionCode;

    /**
     * 差错单号
     */
    private String correctionNo;

    /**
     * 差错状态
     */
    private List<String> status;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 商户订单号
     */
    private String merOrderNo;

    /**
     * 来源系统
     */
    private String sysSource;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 冗余信息
     */
    private ChannelReconcileRedundantInfo redundantInfo;

    /**
     * 复核人
     */
    private String reviewer;

    /**
     * 操作人
     */
    private String operator;
}
