package com.payermax.operating.correction.facade.response;

import com.payermax.operating.correction.facade.common.CorrectionEventInfo;
import com.payermax.operating.correction.facade.enums.StatusEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @desc 差错渠道结果响应
 * <AUTHOR>
 * @date 2022/12/7
 */
@Data
@NoArgsConstructor
public class CorrectionOrderInfoResponse extends CorrectionInfoResponse implements Serializable {

    private static final long serialVersionUID = 187672836474L;

    /**
     * 差错单号
     */
    private String correctionNo;

    /**
     * 三方单号
     */
    private String thirdOrderNo;

    public CorrectionOrderInfoResponse(CorrectionEventInfo correctionInfo, StatusEnum status, String correctionNo) {
        super(correctionInfo, status);
        this.correctionNo = correctionNo;
    }
}
