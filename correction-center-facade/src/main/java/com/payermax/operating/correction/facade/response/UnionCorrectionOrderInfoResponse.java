package com.payermax.operating.correction.facade.response;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.facade.response.CorrectionOrderInfoResponse;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 联合差错订单查询响应
 * @date 2023/7/24
 */
@Data
@NoArgsConstructor
public class UnionCorrectionOrderInfoResponse extends CorrectionOrderInfoResponse implements Serializable {

    private static final long serialVersionUID = 3746276487249L;

    /**
     * 渠道提交单号
     */
    private String channelCommitNo;

    /**
     * 提交单状态
     */
    private String channelCommitStatus;

    /**
     * 支付金额
     */
    private Money payAmount;

    /**
     * 系统来源
     */
    private List<String> sysSourceName;

    /**
     * 处理策略
     */
    private String strategyCodeName;

    /**
     * 差错原因名称
     */
    private String correctionCodeName;

    /**
     * 交易类型
     */
    private String tradeType;
    /**
     * 商户号
     */
    private String merchantNo;
    /**
     * 商户订单号
     */
    private String merchantOrderNo;
    /**
     * 支付完成时间（提交单的完成时间）
     */
    private String payFinishTime;

}
