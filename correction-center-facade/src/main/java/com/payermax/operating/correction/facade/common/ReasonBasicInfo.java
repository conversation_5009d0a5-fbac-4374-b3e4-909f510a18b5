package com.payermax.operating.correction.facade.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 差错原因基础信息
 * @date 2022/11/1
 */
@Data
@NoArgsConstructor
public class ReasonBasicInfo implements Serializable {

    private static final long serialVersionUID = -10239817381L;

    /**
     * 基础信息
     */
    private BaseReasonBasicInfo basicInfo;

    /**
     * 详情描述
     */
    private String desc;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 有效值状态
     */
    private String valid;

    /**
     * 执行规则
     */
    private ExecCondition execCondition;

    /**
     * 操作人记录
     */
    private OperationRecordInfo recordInfo;

    /**
     * 父类基础信息
     */
    private BaseReasonBasicInfo parentBasicInfo;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExecCondition implements Serializable {

        private static final long serialVersionUID = -9824872647835435L;

        private Condition channelCode;

        private Condition merchantNo;

        private Condition productCode;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Condition implements Serializable{

        private static final long serialVersionUID = -982487262315435L;

        /**
         * 条件值
         */
        private String conditionVal;

        /**
         *
         */
        private String value;
    }
}
