package com.payermax.operating.correction.facade.common;

import com.payermax.common.lang.util.money.Money;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 差错金额信息
 * @date 2022/3/16
 */
@Data
public class CorrectionAmountInfo implements Serializable {

    private static final long serialVersionUID = 109172378713764L;
    /**
     * 支付总金额
     */
    private Money payTotalMoney;

}
