package com.payermax.operating.correction.facade.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 基础原因信息
 * @date 2022/11/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseReasonBasicInfo implements Serializable {

    private static final long      serialVersionUID = -10273423537381L;

    /**
     * 差错Code
     */
    private String correctionCode;

    /**
     * 差错名称
     */
    private String correctionName;
}
