package com.payermax.operating.correction.facade.response;

import com.payermax.operating.correction.facade.common.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 差错订单详情页面
 * @date 2022/11/9
 */
@Data
@NoArgsConstructor
public class CorrectionOrderDetailInfoResponse implements Serializable {


    private static final long serialVersionUID = 8978273482742764L;

    /**
     * 差错基本信息
     */
    private CorrectionOperationBasicInfo correctionInfo;

    /**
     * 来源系统
     */
    private List<String>sysSources;

    /**
     * 运营可选原因列表
     */
    private List<BaseReasonBasicInfo> basicInfoList;

    /**
     * 订单信息列表
     */
    private List<OrderInfo> orderInfoList;

    /**
     * 退款订单信息
     */
    private List<RefundOrderInfo> refundInfoList;

    /**
     * 上传列表
     */
    private List<UploadInfo> uploadList;

    /**
     * 操作记录列表
     */
    private List<HandlerRecordInfo> handlerRecordList;

    /**
     * 备注
     */
    private String memo;

    public CorrectionOrderDetailInfoResponse(List<OrderInfo> orderInfoList) {
        this.orderInfoList = orderInfoList;
    }

    public CorrectionOrderDetailInfoResponse(List<OrderInfo> orderInfoList, List<RefundOrderInfo> refundInfoList) {
        this.orderInfoList = orderInfoList;
        this.refundInfoList = refundInfoList;
    }
}
