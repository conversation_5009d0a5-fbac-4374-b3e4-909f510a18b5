package com.payermax.operating.correction.facade.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 操作人记录
 * @date 2022/11/1
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OperationRecordInfo implements Serializable {


    private static final long      serialVersionUID = -23429822645L;

    private Long utcCreateTimeStamp;

    private Long utcUpdateTimeStamp;

    private Long completeTimeStamp;

    private String operator;
}
