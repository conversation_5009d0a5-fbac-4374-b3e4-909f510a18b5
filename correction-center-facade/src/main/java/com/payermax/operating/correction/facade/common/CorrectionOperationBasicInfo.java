package com.payermax.operating.correction.facade.common;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @desc 操作平台差错订单基础信息
 * <AUTHOR>
 * @date 2022/11/8
 */
@Data
public class CorrectionOperationBasicInfo implements Serializable {

    private static final long serialVersionUID = 897537538573342456L;

    private String correctionNo;

    private String correctionName;

    private String correctionCode;

    private String voucherNo;

    private String tradeType;

    private String strategyCode;

    private String strategyName;

    private String operationCorrectionCode;

    private String operationCorrectionName;

    private String correctionAmount;

    private String correctionCurrency;

}
