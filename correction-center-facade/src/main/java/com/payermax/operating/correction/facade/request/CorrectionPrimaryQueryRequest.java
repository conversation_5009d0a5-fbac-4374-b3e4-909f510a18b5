package com.payermax.operating.correction.facade.request;

import com.payermax.operating.correction.facade.common.QueryPageRequest;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 关联查询请求
 * @date 2022/11/13
 */
@Data
public class CorrectionPrimaryQueryRequest extends QueryPageRequest implements Serializable {

    private static final long serialVersionUID = 62983742290734L;

    private String correctionNos;

    private String voucherNos;
}
