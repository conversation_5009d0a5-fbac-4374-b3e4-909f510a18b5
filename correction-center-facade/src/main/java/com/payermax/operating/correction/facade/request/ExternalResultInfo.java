package com.payermax.operating.correction.facade.request;

import com.payermax.operating.correction.facade.common.CorrectionEventInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 外部处理结果信息
 * @date 2023/5/17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExternalResultInfo implements Serializable {

    private static final long serialVersionUID = -9823489325356L;


    /**
     * 事件信息
     */
    private CorrectionEventInfo eventInfo;

    /**
     * 备注
     */
    private String memo;
}
