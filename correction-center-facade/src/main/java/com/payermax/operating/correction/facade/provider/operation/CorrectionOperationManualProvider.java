package com.payermax.operating.correction.facade.provider.operation;

import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.correction.facade.request.CorrectionStrategyCodeHandlerInfo;

/**
 * The interface Correction center operation provider.
 *
 * <AUTHOR>
 * @desc 运营操作平台接口
 * @date 2022 /10/19
 */
public interface CorrectionOperationManualProvider {

    /**
     * Exec strategy will be skiped approval.
     *
     * @param strategyCodeHandlerInfo the strategy code handler info
     * @return the result
     */
    Result execStrategySkipApproval(CorrectionStrategyCodeHandlerInfo strategyCodeHandlerInfo);

    /**
     * Strategy code process result.
     *
     * @param correctionNo the correction no
     * @return the result
     */
    Result processStrategy(String correctionNo);

}
