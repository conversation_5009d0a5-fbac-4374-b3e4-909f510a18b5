package com.payermax.operating.correction.facade.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 目标机构信息
 * @date 2022/12/15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TargetOrgInfo implements Serializable {

    private static final long serialVersionUID = 198427398423598L;

    private String targetOrg;

    /**
     * 目标机构名称
     */
    private String targetOrgName;
}
