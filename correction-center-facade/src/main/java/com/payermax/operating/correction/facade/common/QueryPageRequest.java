package com.payermax.operating.correction.facade.common;

import com.payermax.common.lang.model.dto.request.PageRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 分页查询请求
 * @date 2022/10/31
 */
@Data
@NoArgsConstructor
public class QueryPageRequest implements Serializable {

    private static final long serialVersionUID = 8927498274328924L;

    /**
     * 分页信息
     */
    @NotNull(message = "page can't be null")
    private PageRequest page;

    /**
     * 有效值
     */
    private String valid;

    /**
     * 操作人
     */
    private String operator;
}
