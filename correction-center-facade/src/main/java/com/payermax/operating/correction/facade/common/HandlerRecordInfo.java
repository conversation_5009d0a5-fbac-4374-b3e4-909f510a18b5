package com.payermax.operating.correction.facade.common;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 操作记录信息表
 * @date 2022/11/9
 */
@Data
@NoArgsConstructor
public class HandlerRecordInfo implements Serializable {
    private static final long serialVersionUID = 384772349802349L;

    private String strategyCode;

    private String strategyName;

    private String handlerEvent;

    private String handlerStatus;

    private String desc;

    private String operator;

    private Long utcCreateTimeStamp;

}
