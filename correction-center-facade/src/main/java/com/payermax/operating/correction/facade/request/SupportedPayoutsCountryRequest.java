package com.payermax.operating.correction.facade.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 可支持国家出款列表
 * @date 2023/2/22
 */
@Data
@NoArgsConstructor
public class SupportedPayoutsCountryRequest implements Serializable {

    private static final long serialVersionUID = 4537362972347825L;

    private String name;

    private String value;

    private List<String> paymentMethodList;
}
