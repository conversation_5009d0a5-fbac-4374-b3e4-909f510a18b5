package com.payermax.operating.correction.facade.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 出款信息渲染
 * @date 2022/12/8
 */
@Data
@NoArgsConstructor
public class PayoutsInfoRenderingResponse implements Serializable {

    private static final long serialVersionUID = 31243623498423598L;

    private String label;

    private String id;

    private String type = "input";


    public PayoutsInfoRenderingResponse(String label, String id) {
        this.label = label;
        this.id = id;
    }
}
