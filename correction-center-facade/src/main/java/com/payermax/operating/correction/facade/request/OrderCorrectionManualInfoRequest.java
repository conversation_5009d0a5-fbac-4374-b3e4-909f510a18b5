package com.payermax.operating.correction.facade.request;

import com.payermax.operating.correction.facade.common.OrderCorrectionBaseInfo;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @desc 差错中心请求
 * @date 2022/3/4
 */
@Getter
@Setter
@NoArgsConstructor
@ToString
public class OrderCorrectionManualInfoRequest extends OrderCorrectionBaseInfo {

    private static final long      serialVersionUID = -982347264425356L;

    /**
     * 差错单号(CC004时需要填写)
     */
    private String correctionNo;

    /**
     * 操作人
     *
     * @mock YESONGLIN
     */
    @NotBlank(message = "operator can't be blank")
    private String operator;

    /**
     * 交易类型
     */
    private String tradeType;
}
