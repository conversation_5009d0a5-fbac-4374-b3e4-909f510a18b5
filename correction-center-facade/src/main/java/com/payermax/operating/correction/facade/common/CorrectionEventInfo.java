package com.payermax.operating.correction.facade.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 差错事件基础信息
 * @date 2022/4/7
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CorrectionEventInfo implements Serializable {

    private static final long serialVersionUID = 2034827397443L;

    /**
     * 凭证号
     *
     * @mock DPC15496116703326774506184035
     */
    @NotBlank(message = "voucherNo is mandatory")
    private String voucherNo;

    /**
     * 凭证类型(VOUCHER_SCENE)
     *
     * @mock CHANNEL_COMMIT_NO
     */
    @NotBlank(message = "voucherType is mandatory")
    private String voucherType;

    /**
     * 差错场景对应差错码
     *
     * @mock CC001
     */
    private String correctionCode;


    /**
     * 详情原因code(详情code)
     */
    private String detailCode;

    /**
     * 详情描述(详情描述)
     */
    private String detailDesc;

}
