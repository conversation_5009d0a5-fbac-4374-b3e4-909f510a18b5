package com.payermax.operating.correction.facade.request;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 审批事件
 * @date 2022/11/9
 */
@Data
@NoArgsConstructor
public class ApprovalEvent extends CorrectionStrategyCodeHandlerInfo implements Serializable {

    private static final long serialVersionUID = 2746824927432L;

    private String correctionNo;
    /**
     * 审批动作
     */
    private String approvalAction;
    /**
     * 描述
     */
    private String desc;
    /**
     * 操作人
     */
    private String operator;

}
