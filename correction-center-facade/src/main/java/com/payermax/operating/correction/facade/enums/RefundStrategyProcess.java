package com.payermax.operating.correction.facade.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @desc 退款处理流程
 * @date 2022/12/13
 */
public enum RefundStrategyProcess {
    ORIGINAL_CHANNEL_RETRY,
    CORRECTION_CHANNEL_PROCESS

    ;

    public static RefundStrategyProcess getByName(String name) {
        return StringUtils.isBlank(name) ? null : RefundStrategyProcess.valueOf(name);
    }
}
