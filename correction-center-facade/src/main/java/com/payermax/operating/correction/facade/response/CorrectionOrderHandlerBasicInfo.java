package com.payermax.operating.correction.facade.response;

import com.payermax.common.lang.util.money.Money;
import com.payermax.operating.correction.facade.common.CorrectionOperationBasicInfo;
import com.payermax.operating.correction.facade.common.OperationRecordInfo;
import com.payermax.operating.correction.facade.common.RedundantInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 差错订单基础操作信息
 * @date 2022/11/8
 */
@Data
public class CorrectionOrderHandlerBasicInfo implements Serializable {

    private static final long serialVersionUID = 472879827487624L;

    private CorrectionOperationBasicInfo correctionInfo;

    private String merchantOrderNo;

    private String merchantNo;

    private String merchantName;

    private String refundNo;

    private String status;

    private String channelCode;

    private OperationRecordInfo recordInfo;

    private RedundantInfo redundantInfo;

    private List<String> systemSources;

    private String tradeTypeName;

    private String processStatusName;

    private String memo;

    private String reviewer;

}
