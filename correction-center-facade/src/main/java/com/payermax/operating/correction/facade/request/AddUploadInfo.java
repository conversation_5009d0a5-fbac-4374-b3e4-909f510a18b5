package com.payermax.operating.correction.facade.request;

import com.payermax.operating.correction.facade.common.UploadInfo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 新增上传信息
 * @date 2022/11/9
 */
@Data
@NoArgsConstructor
public class AddUploadInfo implements Serializable {

    private static final long serialVersionUID = 98274382634876L;

    private String correctionNo;

    private UploadInfo uploadInfo;
}
