package com.payermax.operating.correction.facade.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @desc 出款核心参数渲染响应
 * @date 2022/12/20
 */
@Data
@NoArgsConstructor
public class PayoutsCoreInfoRenderingResponse implements Serializable {

    private static final long serialVersionUID = 2389472920942094L;

    private String cashierProductNo;

    private List<PayoutsInfoRenderingResponse> renderingList;
}
