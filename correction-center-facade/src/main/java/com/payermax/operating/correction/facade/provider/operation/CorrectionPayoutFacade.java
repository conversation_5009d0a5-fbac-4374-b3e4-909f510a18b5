package com.payermax.operating.correction.facade.provider.operation;

import com.alibaba.fastjson.JSONObject;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.correction.facade.request.CorrectionPayoutsPaymentInstanceRequest;
import com.payermax.operating.correction.facade.request.PayoutsPaymentInstanceRequest;
import com.payermax.operating.correction.facade.request.SupportedPayoutsCountryRequest;
import com.payermax.operating.correction.facade.response.PayoutsCoreInfoRenderingResponse;
import com.payermax.operating.correction.facade.response.PayoutsInfoRenderingResponse;
import com.payermax.operating.correction.facade.response.TargetOrgInfo;

import java.util.List;

/**
 * The interface Correction payout facade.
 *
 * <AUTHOR>
 * @desc 差错出款facade
 * @date 2022 /12/8
 */
public interface CorrectionPayoutFacade {

    /**
     * Query payouts param info result.
     *
     * @param paymentInstance the payment instance
     * @return the result
     */
    Result<PayoutsCoreInfoRenderingResponse> queryPayoutsParamInfo(PayoutsPaymentInstanceRequest paymentInstance);

    /**
     * 退票登记
     *
     * @param correctionNo the correction no
     * @return the result
     */
    Result bounceBackRegister(String correctionNo);

    /**
     * Query target org list result.
     *
     * @param paymentInstanceRequest the payment instance request
     * @return the result
     */
    Result<List<TargetOrgInfo>> queryTargetOrgList(CorrectionPayoutsPaymentInstanceRequest paymentInstanceRequest);

    /**
     * Save payout rule info result.
     *
     * @param json the json
     * @return the result
     */
    Result savePayoutRuleInfo(JSONObject json);

    /**
     * Query supported payouts country result.
     *
     * @return the result
     */
    Result<List<SupportedPayoutsCountryRequest>> querySupportedPayoutsCountry();
}
