package com.payermax.operating.correction.facade.request;

import com.payermax.operating.correction.facade.common.OperationRecordInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @desc 策略信息
 * @date 2022/10/25
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StrategyInfo implements Serializable {

    private static final long serialVersionUID = 4535872347825L;

    /**
     * 策略code
     */
    private String strategyCode;

    /**
     * 策略名
     */
    private String strategyName;

    /**
     * 扩展显示
     */
    private String extendShow;

    /**
     * 扩展用户信息收集展示
     */
    private String extraUserInfo;

    /**
     * 有效值状态
     */
    private String valid;

    /**
     * 操作人记录
     */
    private OperationRecordInfo recordInfo;
}
