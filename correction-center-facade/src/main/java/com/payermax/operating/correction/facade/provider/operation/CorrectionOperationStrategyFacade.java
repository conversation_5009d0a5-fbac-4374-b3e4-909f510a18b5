package com.payermax.operating.correction.facade.provider.operation;

import com.payermax.common.lang.model.dto.Page;
import com.payermax.common.lang.model.dto.Result;
import com.payermax.operating.correction.facade.common.QueryPageRequest;
import com.payermax.operating.correction.facade.request.StrategyInfo;

import java.util.List;

/**
 * @desc 策略相关provider
 * <AUTHOR>
 * @date 2022/11/8
 */
public interface CorrectionOperationStrategyFacade {

    /**
     * Add strategy info result.
     *
     * @param strategyInfo the strategy info
     * @return the result
     */
    Result storeStrategyInfo(StrategyInfo strategyInfo);

    /**
     * Query strategy info result.
     *
     * @param strategyCode the strategy code
     * @return the result
     */
    Result<StrategyInfo> queryStrategyInfo(String strategyCode);

    /**
     * Query strategy info by page result.
     *
     * @param pageRequest the page request
     * @return the result
     */
    Result<Page<StrategyInfo>> queryStrategyInfoByPage(QueryPageRequest pageRequest);

    /**
     * Filter strategy code result.
     *
     * @param operationCorrectionCode the operation correction code
     * @param tradeType               the trade type
     * @return the result
     */
    Result<List<StrategyInfo>> filterStrategyCode(String operationCorrectionCode, String tradeType);
}
