#!/bin/sh

cd ../ && mvn -U -Dmaven.test.skip=true clean package

# fin-correction-center build
echo "package fin-correction-center Start!"

cd correction-center-service-implementation/target

mkdir correction-center-service-implementation-build

cp correction-center-service-implementation.jar correction-center-service-implementation-build

mkdir correction-center-service-implementation-build/awslogs

tar zcvf root.tar.gz correction-center-service-implementation-build

cd ../../

echo "package fin-correction-center done!"