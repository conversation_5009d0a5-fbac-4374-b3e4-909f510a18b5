[general]
state_file = /var/lib/awslogs/agent-state
use_gzip_http_content_encoding = true

[{project_name}/{env}/error]
log_group_name = {project_name}/{env}/error
log_stream_name = {ip_address}-{hostname}-{instance_id}
file = /work/{project_name}-build/logs/error*.log

[{project_name}/{env}/info]
log_group_name = {project_name}/{env}/info
log_stream_name = {ip_address}-{hostname}-{instance_id}
file = /work/{project_name}-build/logs/info*.log